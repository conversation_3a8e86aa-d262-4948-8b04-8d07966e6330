/**
 * SeaSystem - ملف JavaScript الرئيسي
 * Main JavaScript File
 */

// إعدادات عامة
const SeaSystem = {
    // إعدادات النظام
    config: {
        currency: 'ر.س',
        dateFormat: 'YYYY-MM-DD',
        timeFormat: 'HH:mm:ss',
        language: 'ar'
    },
    
    // دوال مساعدة
    utils: {
        // تنسيق الأرقام
        formatNumber: function(number, decimals = 2) {
            return parseFloat(number).toLocaleString('ar-SA', {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            });
        },
        
        // تنسيق العملة
        formatCurrency: function(amount, decimals = 2) {
            return this.formatNumber(amount, decimals) + ' ' + SeaSystem.config.currency;
        },
        
        // تنسيق التاريخ
        formatDate: function(date, format = null) {
            const d = new Date(date);
            if (format === 'short') {
                return d.toLocaleDateString('ar-SA');
            }
            return d.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        },
        
        // تنسيق الوقت
        formatTime: function(date) {
            const d = new Date(date);
            return d.toLocaleTimeString('ar-SA');
        },
        
        // التحقق من صحة البريد الإلكتروني
        validateEmail: function(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        },
        
        // التحقق من صحة رقم الهاتف
        validatePhone: function(phone) {
            const re = /^[0-9+\-\s()]+$/;
            return re.test(phone) && phone.length >= 10;
        },
        
        // إنشاء UUID بسيط
        generateUUID: function() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
    },
    
    // دوال واجهة المستخدم
    ui: {
        // عرض رسالة نجاح
        showSuccess: function(message, duration = 3000) {
            this.showAlert(message, 'success', duration);
        },
        
        // عرض رسالة خطأ
        showError: function(message, duration = 5000) {
            this.showAlert(message, 'danger', duration);
        },
        
        // عرض رسالة تحذير
        showWarning: function(message, duration = 4000) {
            this.showAlert(message, 'warning', duration);
        },
        
        // عرض رسالة معلومات
        showInfo: function(message, duration = 3000) {
            this.showAlert(message, 'info', duration);
        },
        
        // عرض تنبيه
        showAlert: function(message, type = 'info', duration = 3000) {
            const alertId = 'alert-' + Date.now();
            const alertHtml = `
                <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show position-fixed" 
                     style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    <i class="fas fa-${this.getAlertIcon(type)} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', alertHtml);
            
            // إزالة التنبيه تلقائياً
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    alert.remove();
                }
            }, duration);
        },
        
        // الحصول على أيقونة التنبيه
        getAlertIcon: function(type) {
            const icons = {
                'success': 'check-circle',
                'danger': 'exclamation-triangle',
                'warning': 'exclamation-circle',
                'info': 'info-circle'
            };
            return icons[type] || 'info-circle';
        },
        
        // عرض مؤشر التحميل
        showLoading: function(element = null) {
            const loadingHtml = `
                <div class="d-flex justify-content-center align-items-center p-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <span class="ms-2">جاري التحميل...</span>
                </div>
            `;
            
            if (element) {
                element.innerHTML = loadingHtml;
            } else {
                document.body.insertAdjacentHTML('beforeend', `
                    <div id="global-loading" class="position-fixed top-0 start-0 w-100 h-100 
                         d-flex justify-content-center align-items-center" 
                         style="background: rgba(0,0,0,0.5); z-index: 9999;">
                        <div class="bg-white p-4 rounded">
                            ${loadingHtml}
                        </div>
                    </div>
                `);
            }
        },
        
        // إخفاء مؤشر التحميل
        hideLoading: function() {
            const loading = document.getElementById('global-loading');
            if (loading) {
                loading.remove();
            }
        },
        
        // تأكيد الحذف
        confirmDelete: function(message = 'هل أنت متأكد من الحذف؟') {
            return confirm(message);
        },
        
        // تحديث عداد الصفحة
        updatePageCounter: function(current, total) {
            const counter = document.getElementById('page-counter');
            if (counter) {
                counter.textContent = `${current} من ${total}`;
            }
        }
    },
    
    // دوال AJAX
    ajax: {
        // طلب GET
        get: function(url, callback, errorCallback = null) {
            this.request('GET', url, null, callback, errorCallback);
        },
        
        // طلب POST
        post: function(url, data, callback, errorCallback = null) {
            this.request('POST', url, data, callback, errorCallback);
        },
        
        // طلب PUT
        put: function(url, data, callback, errorCallback = null) {
            this.request('PUT', url, data, callback, errorCallback);
        },
        
        // طلب DELETE
        delete: function(url, callback, errorCallback = null) {
            this.request('DELETE', url, null, callback, errorCallback);
        },
        
        // طلب عام
        request: function(method, url, data, callback, errorCallback = null) {
            const xhr = new XMLHttpRequest();
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            callback(response);
                        } catch (e) {
                            callback(xhr.responseText);
                        }
                    } else {
                        if (errorCallback) {
                            errorCallback(xhr.status, xhr.statusText);
                        } else {
                            SeaSystem.ui.showError('حدث خطأ في الاتصال بالخادم');
                        }
                    }
                }
            };
            
            xhr.open(method, url, true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            
            if (data) {
                xhr.send(JSON.stringify(data));
            } else {
                xhr.send();
            }
        }
    },
    
    // دوال النماذج
    forms: {
        // التحقق من صحة النموذج
        validate: function(formId) {
            const form = document.getElementById(formId);
            if (!form) return false;
            
            let isValid = true;
            const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
            
            inputs.forEach(input => {
                if (!input.value.trim()) {
                    this.showFieldError(input, 'هذا الحقل مطلوب');
                    isValid = false;
                } else {
                    this.clearFieldError(input);
                    
                    // التحقق من نوع الحقل
                    if (input.type === 'email' && !SeaSystem.utils.validateEmail(input.value)) {
                        this.showFieldError(input, 'البريد الإلكتروني غير صحيح');
                        isValid = false;
                    }
                    
                    if (input.type === 'tel' && !SeaSystem.utils.validatePhone(input.value)) {
                        this.showFieldError(input, 'رقم الهاتف غير صحيح');
                        isValid = false;
                    }
                }
            });
            
            return isValid;
        },
        
        // عرض خطأ في الحقل
        showFieldError: function(field, message) {
            this.clearFieldError(field);
            
            field.classList.add('is-invalid');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            errorDiv.textContent = message;
            field.parentNode.appendChild(errorDiv);
        },
        
        // إزالة خطأ الحقل
        clearFieldError: function(field) {
            field.classList.remove('is-invalid');
            const errorDiv = field.parentNode.querySelector('.invalid-feedback');
            if (errorDiv) {
                errorDiv.remove();
            }
        },
        
        // تنظيف النموذج
        reset: function(formId) {
            const form = document.getElementById(formId);
            if (form) {
                form.reset();
                const errors = form.querySelectorAll('.invalid-feedback');
                errors.forEach(error => error.remove());
                
                const invalidFields = form.querySelectorAll('.is-invalid');
                invalidFields.forEach(field => field.classList.remove('is-invalid'));
            }
        },
        
        // الحصول على بيانات النموذج
        getData: function(formId) {
            const form = document.getElementById(formId);
            if (!form) return {};
            
            const formData = new FormData(form);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            return data;
        }
    },
    
    // دوال الجداول
    tables: {
        // تحديث الجدول
        refresh: function(tableId) {
            const table = document.getElementById(tableId);
            if (table) {
                // يمكن إضافة منطق تحديث الجدول هنا
                SeaSystem.ui.showInfo('تم تحديث الجدول');
            }
        },
        
        // فرز الجدول
        sort: function(tableId, columnIndex, direction = 'asc') {
            const table = document.getElementById(tableId);
            if (!table) return;
            
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            rows.sort((a, b) => {
                const aValue = a.cells[columnIndex].textContent.trim();
                const bValue = b.cells[columnIndex].textContent.trim();
                
                if (direction === 'asc') {
                    return aValue.localeCompare(bValue, 'ar');
                } else {
                    return bValue.localeCompare(aValue, 'ar');
                }
            });
            
            rows.forEach(row => tbody.appendChild(row));
        },
        
        // البحث في الجدول
        search: function(tableId, searchTerm) {
            const table = document.getElementById(tableId);
            if (!table) return;
            
            const rows = table.querySelectorAll('tbody tr');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm.toLowerCase())) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });
            
            // تحديث عداد النتائج
            const counter = document.getElementById('search-results-count');
            if (counter) {
                counter.textContent = `${visibleCount} نتيجة`;
            }
        }
    },
    
    // دوال التخزين المحلي
    storage: {
        // حفظ البيانات
        set: function(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (e) {
                console.error('خطأ في حفظ البيانات:', e);
                return false;
            }
        },
        
        // استرجاع البيانات
        get: function(key, defaultValue = null) {
            try {
                const value = localStorage.getItem(key);
                return value ? JSON.parse(value) : defaultValue;
            } catch (e) {
                console.error('خطأ في استرجاع البيانات:', e);
                return defaultValue;
            }
        },
        
        // حذف البيانات
        remove: function(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (e) {
                console.error('خطأ في حذف البيانات:', e);
                return false;
            }
        },
        
        // مسح جميع البيانات
        clear: function() {
            try {
                localStorage.clear();
                return true;
            } catch (e) {
                console.error('خطأ في مسح البيانات:', e);
                return false;
            }
        }
    }
};

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التوقيت
    SeaSystem.initTime();
    
    // تهيئة النماذج
    SeaSystem.initForms();
    
    // تهيئة الجداول
    SeaSystem.initTables();
    
    // تهيئة الأحداث العامة
    SeaSystem.initEvents();
});

// تهيئة التوقيت
SeaSystem.initTime = function() {
    // تحديث الوقت كل ثانية
    setInterval(() => {
        const timeElements = document.querySelectorAll('.current-time');
        timeElements.forEach(element => {
            element.textContent = SeaSystem.utils.formatTime(new Date());
        });
        
        const dateElements = document.querySelectorAll('.current-date');
        dateElements.forEach(element => {
            element.textContent = SeaSystem.utils.formatDate(new Date());
        });
    }, 1000);
};

// تهيئة النماذج
SeaSystem.initForms = function() {
    // إضافة مستمعي الأحداث للنماذج
    const forms = document.querySelectorAll('form[data-validate="true"]');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!SeaSystem.forms.validate(this.id)) {
                e.preventDefault();
                SeaSystem.ui.showError('يرجى تصحيح الأخطاء في النموذج');
            }
        });
    });
    
    // تنسيق حقول الأرقام
    const numberInputs = document.querySelectorAll('input[type="number"]');
    numberInputs.forEach(input => {
        input.addEventListener('input', function() {
            if (this.value && !isNaN(this.value)) {
                this.value = parseFloat(this.value).toFixed(2);
            }
        });
    });
};

// تهيئة الجداول
SeaSystem.initTables = function() {
    // إضافة وظائف الفرز للجداول
    const sortableHeaders = document.querySelectorAll('th[data-sortable="true"]');
    sortableHeaders.forEach(header => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            const table = this.closest('table');
            const columnIndex = Array.from(this.parentNode.children).indexOf(this);
            const currentDirection = this.dataset.sortDirection || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            
            SeaSystem.tables.sort(table.id, columnIndex, newDirection);
            this.dataset.sortDirection = newDirection;
            
            // تحديث الأيقونة
            const icon = this.querySelector('i');
            if (icon) {
                icon.className = newDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
            }
        });
    });
    
    // إضافة وظيفة البحث
    const searchInputs = document.querySelectorAll('input[data-table-search]');
    searchInputs.forEach(input => {
        input.addEventListener('input', function() {
            const tableId = this.dataset.tableSearch;
            SeaSystem.tables.search(tableId, this.value);
        });
    });
};

// تهيئة الأحداث العامة
SeaSystem.initEvents = function() {
    // أزرار الحذف
    const deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const message = this.dataset.confirmMessage || 'هل أنت متأكد من الحذف؟';
            if (!SeaSystem.ui.confirmDelete(message)) {
                e.preventDefault();
            }
        });
    });
    
    // أزرار الطباعة
    const printButtons = document.querySelectorAll('.btn-print');
    printButtons.forEach(button => {
        button.addEventListener('click', function() {
            window.print();
        });
    });
    
    // إغلاق التنبيهات تلقائياً
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(alert => {
            if (alert.classList.contains('alert-success')) {
                setTimeout(() => {
                    alert.style.transition = 'opacity 0.5s';
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 500);
                }, 3000);
            }
        });
    }, 100);
};

// تصدير الكائن للاستخدام العام
window.SeaSystem = SeaSystem;
