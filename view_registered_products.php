<?php
/**
 * SeaSystem - عرض المنتجات المسجلة
 * View Registered Products
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/Inventory.php';

// التأكد من تسجيل الدخول
requireLogin();

// إنشاء كائن المخزون
$inventory = new Inventory();

// الحصول على بيانات المستخدم الحالي
$current_user = getCurrentUser();

// الحصول على جميع المنتجات
$products = $inventory->getAllProducts();

// الحصول على التصنيفات
$categories = $inventory->getProductCategories();

// إحصائيات المنتجات
$total_products = count($products);
$total_stock_value = 0;
$low_stock_count = 0;
$granite_count = 0;
$marble_count = 0;

foreach ($products as $product) {
    $total_stock_value += $product['stock_value'];
    if ($product['current_stock'] <= $product['min_stock_level']) {
        $low_stock_count++;
    }
    if ($product['category_id'] == 10) { // جرانيت
        $granite_count++;
    } elseif ($product['category_id'] == 11) { // رخام
        $marble_count++;
    }
}

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المنتجات المسجلة - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    
    <style>
        body { padding-top: 80px !important; }
        .products-card { background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.08); padding: 2rem; margin-bottom: 2rem; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px; padding: 1.5rem; text-align: center; }
        .granite-card { background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%); }
        .marble-card { background: linear-gradient(135deg, #E6E6FA 0%, #D8BFD8 100%); color: #333; }
        .value-card { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }
        .low-stock-card { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); }
        .product-table { font-size: 0.9rem; }
        .stock-indicator { width: 10px; height: 10px; border-radius: 50%; display: inline-block; margin-left: 5px; }
        .stock-good { background-color: #28a745; }
        .stock-low { background-color: #ffc107; }
        .stock-critical { background-color: #dc3545; }
        .category-granite { background: #8B4513; color: white; }
        .category-marble { background: #E6E6FA; color: #333; }
        .category-other { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12 p-4">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="page-title">
                                <i class="fas fa-boxes me-2"></i>المنتجات المسجلة في النظام
                            </h1>
                            <p class="page-subtitle">عرض شامل لجميع المنتجات المسجلة مع التفاصيل والإحصائيات</p>
                        </div>
                        <div class="col-auto">
                            <div class="d-flex gap-2">
                                <a href="inventory.php" class="btn btn-primary">
                                    <i class="fas fa-warehouse me-2"></i>إدارة المخزون
                                </a>
                                <a href="inventory.php#add-product-tab" class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>إضافة منتج
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <i class="fas fa-boxes fa-2x mb-2"></i>
                            <h3><?php echo $total_products; ?></h3>
                            <p class="mb-0">إجمالي المنتجات</p>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="stat-card granite-card">
                            <i class="fas fa-mountain fa-2x mb-2"></i>
                            <h3><?php echo $granite_count; ?></h3>
                            <p class="mb-0">منتجات الجرانيت</p>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="stat-card marble-card">
                            <i class="fas fa-gem fa-2x mb-2"></i>
                            <h3><?php echo $marble_count; ?></h3>
                            <p class="mb-0">منتجات الرخام</p>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="stat-card value-card">
                            <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                            <h3><?php echo number_format($total_stock_value, 0); ?></h3>
                            <p class="mb-0">قيمة المخزون (ج.م)</p>
                        </div>
                    </div>
                </div>

                <?php if ($low_stock_count > 0): ?>
                <!-- تنبيه المخزون المنخفض -->
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه:</strong> يوجد <?php echo $low_stock_count; ?> منتج بمخزون منخفض يحتاج إعادة تموين.
                </div>
                <?php endif; ?>

                <!-- قائمة المنتجات -->
                <div class="products-card">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>
                            <i class="fas fa-list me-2"></i>قائمة المنتجات المسجلة
                        </h3>
                        <div>
                            <span class="badge bg-primary"><?php echo $total_products; ?> منتج</span>
                        </div>
                    </div>
                    
                    <?php if (empty($products)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد منتجات مسجلة</h4>
                        <p class="text-muted">لم يتم تسجيل أي منتجات في النظام حتى الآن</p>
                        <a href="inventory.php#add-product-tab" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إضافة أول منتج
                        </a>
                    </div>
                    <?php else: ?>
                    
                    <div class="table-responsive">
                        <table class="table table-hover product-table">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>رمز المنتج</th>
                                    <th>اسم المنتج</th>
                                    <th>التصنيف</th>
                                    <th>الوحدة</th>
                                    <th>المخزون الحالي</th>
                                    <th>مؤشر المخزون</th>
                                    <th>سعر التكلفة</th>
                                    <th>سعر البيع</th>
                                    <th>قيمة المخزون</th>
                                    <th>الباركود</th>
                                    <th>تاريخ الإضافة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($products as $index => $product): ?>
                                <tr>
                                    <td><?php echo $index + 1; ?></td>
                                    <td>
                                        <code><?php echo htmlspecialchars($product['product_code']); ?></code>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($product['product_name']); ?></strong>
                                        <?php if (!empty($product['description'])): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars(substr($product['description'], 0, 50)) . '...'; ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $category_class = 'category-other';
                                        if ($product['category_id'] == 10) $category_class = 'category-granite';
                                        elseif ($product['category_id'] == 11) $category_class = 'category-marble';
                                        ?>
                                        <span class="badge <?php echo $category_class; ?>">
                                            <?php echo htmlspecialchars($product['category_name'] ?? 'غير محدد'); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($product['unit_name'] ?? 'قطعة'); ?></td>
                                    <td>
                                        <strong><?php echo number_format($product['current_stock'], 2); ?></strong>
                                    </td>
                                    <td>
                                        <?php
                                        $stock_status = 'stock-good';
                                        $stock_text = 'جيد';
                                        if ($product['current_stock'] <= $product['min_stock_level']) {
                                            $stock_status = 'stock-critical';
                                            $stock_text = 'منخفض';
                                        } elseif ($product['current_stock'] <= ($product['min_stock_level'] * 1.5)) {
                                            $stock_status = 'stock-low';
                                            $stock_text = 'تحذير';
                                        }
                                        ?>
                                        <span class="stock-indicator <?php echo $stock_status; ?>"></span>
                                        <small><?php echo $stock_text; ?></small>
                                    </td>
                                    <td><?php echo number_format($product['cost_price'], 2); ?> ج.م</td>
                                    <td><?php echo number_format($product['selling_price'], 2); ?> ج.م</td>
                                    <td>
                                        <strong><?php echo number_format($product['stock_value'], 2); ?> ج.م</strong>
                                    </td>
                                    <td>
                                        <?php if (!empty($product['barcode'])): ?>
                                        <code><?php echo htmlspecialchars($product['barcode']); ?></code>
                                        <?php else: ?>
                                        <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small><?php echo date('Y-m-d', strtotime($product['created_at'])); ?></small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <?php endif; ?>
                </div>

                <!-- تفاصيل المنتجات حسب التصنيف -->
                <?php if (!empty($products)): ?>
                <div class="row">
                    <!-- منتجات الجرانيت -->
                    <div class="col-md-6">
                        <div class="products-card">
                            <h4 class="text-brown mb-3">
                                <i class="fas fa-mountain me-2"></i>منتجات الجرانيت (<?php echo $granite_count; ?>)
                            </h4>
                            
                            <?php 
                            $granite_products = array_filter($products, function($p) { return $p['category_id'] == 10; });
                            if (!empty($granite_products)): 
                            ?>
                            <div class="list-group">
                                <?php foreach ($granite_products as $product): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($product['product_name']); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars($product['product_code']); ?></small>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-secondary"><?php echo number_format($product['current_stock'], 1); ?> م²</span>
                                            <br><small><?php echo number_format($product['selling_price'], 0); ?> ج.م/م²</small>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php else: ?>
                            <p class="text-muted">لا توجد منتجات جرانيت مسجلة</p>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- منتجات الرخام -->
                    <div class="col-md-6">
                        <div class="products-card">
                            <h4 class="text-purple mb-3">
                                <i class="fas fa-gem me-2"></i>منتجات الرخام (<?php echo $marble_count; ?>)
                            </h4>
                            
                            <?php 
                            $marble_products = array_filter($products, function($p) { return $p['category_id'] == 11; });
                            if (!empty($marble_products)): 
                            ?>
                            <div class="list-group">
                                <?php foreach ($marble_products as $product): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($product['product_name']); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars($product['product_code']); ?></small>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-secondary"><?php echo number_format($product['current_stock'], 1); ?> م²</span>
                                            <br><small><?php echo number_format($product['selling_price'], 0); ?> ج.م/م²</small>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php else: ?>
                            <p class="text-muted">لا توجد منتجات رخام مسجلة</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- ملخص الإحصائيات -->
                <?php if (!empty($products)): ?>
                <div class="products-card bg-light">
                    <h4 class="text-center mb-4">
                        <i class="fas fa-chart-pie me-2"></i>ملخص إحصائيات المنتجات
                    </h4>
                    
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h5 class="text-primary"><?php echo $total_products; ?></h5>
                            <p class="mb-0">إجمالي المنتجات</p>
                        </div>
                        
                        <div class="col-md-3">
                            <h5 class="text-success"><?php echo number_format($total_stock_value, 0); ?> ج.م</h5>
                            <p class="mb-0">قيمة المخزون الإجمالية</p>
                        </div>
                        
                        <div class="col-md-3">
                            <h5 class="text-info"><?php echo number_format(array_sum(array_column($products, 'current_stock')), 1); ?> م²</h5>
                            <p class="mb-0">إجمالي المخزون</p>
                        </div>
                        
                        <div class="col-md-3">
                            <h5 class="<?php echo $low_stock_count > 0 ? 'text-danger' : 'text-success'; ?>">
                                <?php echo $low_stock_count; ?>
                            </h5>
                            <p class="mb-0">منتجات بمخزون منخفض</p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
