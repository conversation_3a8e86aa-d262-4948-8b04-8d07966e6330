<?php
/**
 * SeaSystem - فئة إدارة القيود المحاسبية
 * Journal Entry Management Class
 */

require_once __DIR__ . '/../config/database.php';

class JournalEntry {
    private $db;
    private $table_name = "journal_entries";
    private $details_table = "journal_entry_details";

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }

    /**
     * إنشاء قيد محاسبي جديد
     */
    public function create($entry_data, $details_data) {
        try {
            $this->db->beginTransaction();

            // التحقق من توازن القيد
            if (!$this->validateBalance($details_data)) {
                throw new Exception('القيد غير متوازن - مجموع المدين لا يساوي مجموع الدائن');
            }

            // توليد رقم القيد التلقائي
            if (empty($entry_data['entry_number'])) {
                $entry_data['entry_number'] = $this->generateEntryNumber();
            }

            // حساب المجاميع
            $totals = $this->calculateTotals($details_data);
            $entry_data['total_debit'] = $totals['debit'];
            $entry_data['total_credit'] = $totals['credit'];

            // إدراج القيد الرئيسي
            $sql = "INSERT INTO " . $this->table_name . "
                    (entry_number, entry_date, reference_type, reference_id, description,
                     total_debit, total_credit, status, notes, created_by)
                    VALUES (:entry_number, :entry_date, :reference_type, :reference_id, :description,
                            :total_debit, :total_credit, :status, :notes, :created_by)";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':entry_number', $entry_data['entry_number']);
            $stmt->bindParam(':entry_date', $entry_data['entry_date']);
            $stmt->bindParam(':reference_type', $entry_data['reference_type']);
            $stmt->bindParam(':reference_id', $entry_data['reference_id']);
            $stmt->bindParam(':description', $entry_data['description']);
            $stmt->bindParam(':total_debit', $entry_data['total_debit']);
            $stmt->bindParam(':total_credit', $entry_data['total_credit']);
            $stmt->bindParam(':status', $entry_data['status']);
            $stmt->bindParam(':notes', $entry_data['notes']);
            $stmt->bindParam(':created_by', $entry_data['created_by']);

            if (!$stmt->execute()) {
                throw new Exception('فشل في إنشاء القيد المحاسبي');
            }

            $entry_id = $this->db->lastInsertId();

            // إدراج تفاصيل القيد
            foreach ($details_data as $detail) {
                $this->addEntryDetail($entry_id, $detail);
            }

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'تم إنشاء القيد المحاسبي بنجاح',
                'entry_id' => $entry_id,
                'entry_number' => $entry_data['entry_number']
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'message' => 'خطأ في إنشاء القيد: ' . $e->getMessage()
            ];
        }
    }

    /**
     * تحديث القيد المحاسبي
     */
    public function update($entry_id, $entry_data, $details_data) {
        try {
            $this->db->beginTransaction();

            // التحقق من حالة القيد
            $entry = $this->getById($entry_id);
            if (!$entry) {
                throw new Exception('القيد غير موجود');
            }

            if ($entry['status'] == 'posted') {
                throw new Exception('لا يمكن تعديل قيد مرحل');
            }

            // التحقق من توازن القيد
            if (!$this->validateBalance($details_data)) {
                throw new Exception('القيد غير متوازن - مجموع المدين لا يساوي مجموع الدائن');
            }

            // حساب المجاميع
            $totals = $this->calculateTotals($details_data);
            $entry_data['total_debit'] = $totals['debit'];
            $entry_data['total_credit'] = $totals['credit'];

            // تحديث القيد الرئيسي
            $sql = "UPDATE " . $this->table_name . "
                    SET entry_date = :entry_date,
                        reference_type = :reference_type,
                        reference_id = :reference_id,
                        description = :description,
                        total_debit = :total_debit,
                        total_credit = :total_credit,
                        notes = :notes,
                        updated_at = NOW()
                    WHERE id = :entry_id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':entry_date', $entry_data['entry_date']);
            $stmt->bindParam(':reference_type', $entry_data['reference_type']);
            $stmt->bindParam(':reference_id', $entry_data['reference_id']);
            $stmt->bindParam(':description', $entry_data['description']);
            $stmt->bindParam(':total_debit', $entry_data['total_debit']);
            $stmt->bindParam(':total_credit', $entry_data['total_credit']);
            $stmt->bindParam(':notes', $entry_data['notes']);
            $stmt->bindParam(':entry_id', $entry_id);

            if (!$stmt->execute()) {
                throw new Exception('فشل في تحديث القيد');
            }

            // حذف التفاصيل القديمة
            $this->deleteEntryDetails($entry_id);

            // إضافة التفاصيل الجديدة
            foreach ($details_data as $detail) {
                $this->addEntryDetail($entry_id, $detail);
            }

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'تم تحديث القيد المحاسبي بنجاح'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'message' => 'خطأ في تحديث القيد: ' . $e->getMessage()
            ];
        }
    }

    /**
     * ترحيل القيد المحاسبي
     */
    public function postEntry($entry_id, $user_id) {
        try {
            $this->db->beginTransaction();

            $entry = $this->getById($entry_id);
            if (!$entry) {
                throw new Exception('القيد غير موجود');
            }

            if ($entry['status'] == 'posted') {
                throw new Exception('القيد مرحل مسبقاً');
            }

            // التحقق من توازن القيد
            if ($entry['total_debit'] != $entry['total_credit']) {
                throw new Exception('القيد غير متوازن');
            }

            // ترحيل القيد
            $sql = "UPDATE " . $this->table_name . "
                    SET status = 'posted',
                        approved_by = :user_id,
                        approved_at = NOW(),
                        updated_at = NOW()
                    WHERE id = :entry_id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':entry_id', $entry_id);

            if (!$stmt->execute()) {
                throw new Exception('فشل في ترحيل القيد');
            }

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'تم ترحيل القيد بنجاح'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'message' => 'خطأ في ترحيل القيد: ' . $e->getMessage()
            ];
        }
    }

    /**
     * حذف القيد المحاسبي
     */
    public function delete($entry_id) {
        try {
            $this->db->beginTransaction();

            $entry = $this->getById($entry_id);
            if (!$entry) {
                throw new Exception('القيد غير موجود');
            }

            if ($entry['status'] == 'posted') {
                throw new Exception('لا يمكن حذف قيد مرحل');
            }

            // حذف تفاصيل القيد
            $this->deleteEntryDetails($entry_id);

            // حذف القيد الرئيسي
            $sql = "DELETE FROM " . $this->table_name . " WHERE id = :entry_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':entry_id', $entry_id);

            if (!$stmt->execute()) {
                throw new Exception('فشل في حذف القيد');
            }

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'تم حذف القيد بنجاح'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'message' => 'خطأ في حذف القيد: ' . $e->getMessage()
            ];
        }
    }

    /**
     * الحصول على قيد واحد
     */
    public function getById($entry_id) {
        try {
            $sql = "SELECT je.*,
                           u1.full_name as created_by_name,
                           u2.full_name as approved_by_name
                    FROM " . $this->table_name . " je
                    LEFT JOIN users u1 ON je.created_by = u1.id
                    LEFT JOIN users u2 ON je.approved_by = u2.id
                    WHERE je.id = :entry_id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':entry_id', $entry_id);
            $stmt->execute();

            return $stmt->fetch();
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * الحصول على تفاصيل القيد
     */
    public function getEntryDetails($entry_id) {
        try {
            $sql = "SELECT jed.*, a.account_code, a.account_name
                    FROM " . $this->details_table . " jed
                    JOIN chart_of_accounts a ON jed.account_id = a.id
                    WHERE jed.journal_entry_id = :entry_id
                    ORDER BY jed.id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':entry_id', $entry_id);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على جميع القيود
     */
    public function getAll($filters = []) {
        try {
            $sql = "SELECT je.*,
                           je.total_debit as debit_amount,
                           je.total_credit as credit_amount,
                           u1.full_name as created_by_name,
                           u2.full_name as approved_by_name
                    FROM " . $this->table_name . " je
                    LEFT JOIN users u1 ON je.created_by = u1.id
                    LEFT JOIN users u2 ON je.approved_by = u2.id
                    WHERE 1=1";

            $params = [];

            // تطبيق المرشحات
            if (!empty($filters['status'])) {
                $sql .= " AND je.status = :status";
                $params[':status'] = $filters['status'];
            }

            if (!empty($filters['reference_type'])) {
                $sql .= " AND je.reference_type = :reference_type";
                $params[':reference_type'] = $filters['reference_type'];
            }

            if (!empty($filters['date_from'])) {
                $sql .= " AND je.entry_date >= :date_from";
                $params[':date_from'] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $sql .= " AND je.entry_date <= :date_to";
                $params[':date_to'] = $filters['date_to'];
            }

            $sql .= " ORDER BY je.entry_date DESC, je.id DESC";

            // تطبيق الحد الأقصى للنتائج
            if (!empty($filters['limit'])) {
                $sql .= " LIMIT " . intval($filters['limit']);
            }

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * البحث في القيود
     */
    public function search($keyword, $filters = []) {
        try {
            $sql = "SELECT je.*,
                           je.total_debit as debit_amount,
                           je.total_credit as credit_amount,
                           u1.full_name as created_by_name,
                           u2.full_name as approved_by_name
                    FROM " . $this->table_name . " je
                    LEFT JOIN users u1 ON je.created_by = u1.id
                    LEFT JOIN users u2 ON je.approved_by = u2.id
                    WHERE (je.entry_number LIKE :keyword
                           OR je.description LIKE :keyword
                           OR je.notes LIKE :keyword)";

            $params = [':keyword' => "%$keyword%"];

            // تطبيق المرشحات الإضافية
            if (!empty($filters['status'])) {
                $sql .= " AND je.status = :status";
                $params[':status'] = $filters['status'];
            }

            $sql .= " ORDER BY je.entry_date DESC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * إحصائيات القيود
     */
    public function getStatistics($filters = []) {
        try {
            $sql = "SELECT
                        COUNT(*) as total_entries,
                        COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_entries,
                        COUNT(CASE WHEN status = 'posted' THEN 1 END) as posted_entries,
                        SUM(CASE WHEN status = 'posted' THEN total_debit ELSE 0 END) as total_posted_amount,
                        AVG(CASE WHEN status = 'posted' THEN total_debit ELSE NULL END) as average_amount
                    FROM " . $this->table_name . "
                    WHERE 1=1";

            $params = [];

            // تطبيق المرشحات
            if (!empty($filters['date_from'])) {
                $sql .= " AND entry_date >= :date_from";
                $params[':date_from'] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $sql .= " AND entry_date <= :date_to";
                $params[':date_to'] = $filters['date_to'];
            }

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetch();
        } catch (Exception $e) {
            return [
                'total_entries' => 0,
                'draft_entries' => 0,
                'posted_entries' => 0,
                'total_posted_amount' => 0,
                'average_amount' => 0
            ];
        }
    }

    /**
     * إضافة تفصيل قيد
     */
    private function addEntryDetail($entry_id, $detail_data) {
        $sql = "INSERT INTO " . $this->details_table . "
                (journal_entry_id, account_id, debit_amount, credit_amount, description)
                VALUES (:entry_id, :account_id, :debit_amount, :credit_amount, :description)";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':entry_id', $entry_id);
        $stmt->bindParam(':account_id', $detail_data['account_id']);
        $stmt->bindParam(':debit_amount', $detail_data['debit_amount']);
        $stmt->bindParam(':credit_amount', $detail_data['credit_amount']);
        $stmt->bindParam(':description', $detail_data['description']);

        return $stmt->execute();
    }

    /**
     * حذف تفاصيل القيد
     */
    private function deleteEntryDetails($entry_id) {
        $sql = "DELETE FROM " . $this->details_table . " WHERE journal_entry_id = :entry_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':entry_id', $entry_id);

        return $stmt->execute();
    }

    /**
     * التحقق من توازن القيد
     */
    private function validateBalance($details_data) {
        $total_debit = 0;
        $total_credit = 0;

        foreach ($details_data as $detail) {
            $total_debit += floatval($detail['debit_amount']);
            $total_credit += floatval($detail['credit_amount']);
        }

        return abs($total_debit - $total_credit) < 0.01; // السماح بفرق صغير للأرقام العشرية
    }

    /**
     * حساب مجاميع القيد
     */
    private function calculateTotals($details_data) {
        $total_debit = 0;
        $total_credit = 0;

        foreach ($details_data as $detail) {
            $total_debit += floatval($detail['debit_amount']);
            $total_credit += floatval($detail['credit_amount']);
        }

        return [
            'debit' => $total_debit,
            'credit' => $total_credit
        ];
    }

    /**
     * توليد رقم قيد تلقائي
     */
    public function generateEntryNumber() {
        try {
            $year = date('Y');
            $month = date('m');

            // الحصول على آخر رقم أو إنشاء سجل جديد
            $sql = "INSERT INTO journal_numbering (year, month, last_number)
                    VALUES (:year, :month, 1)
                    ON DUPLICATE KEY UPDATE last_number = last_number + 1";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':year', $year);
            $stmt->bindParam(':month', $month);
            $stmt->execute();

            // الحصول على الرقم الحالي
            $sql = "SELECT last_number, prefix FROM journal_numbering
                    WHERE year = :year AND month = :month";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':year', $year);
            $stmt->bindParam(':month', $month);
            $stmt->execute();

            $result = $stmt->fetch();
            $number = str_pad($result['last_number'], 3, '0', STR_PAD_LEFT);

            return $result['prefix'] . $year . $month . $number;

        } catch (Exception $e) {
            return 'JE' . date('Ymd') . '001';
        }
    }

    /**
     * إنشاء قيد من فاتورة
     */
    public function createFromInvoice($invoice_id, $user_id) {
        try {
            // الحصول على بيانات الفاتورة
            require_once __DIR__ . '/Invoice.php';
            $invoice_class = new Invoice();
            $invoice = $invoice_class->getById($invoice_id);
            $invoice_items = $invoice_class->getInvoiceItems($invoice_id);

            if (!$invoice) {
                throw new Exception('الفاتورة غير موجودة');
            }

            // بيانات القيد الرئيسي
            $entry_data = [
                'entry_date' => $invoice['invoice_date'],
                'reference_type' => 'invoice',
                'reference_id' => $invoice_id,
                'description' => 'قيد فاتورة ' . ($invoice['invoice_type'] == 'sales' ? 'مبيعات' : 'مشتريات') . ' رقم ' . $invoice['invoice_number'],
                'status' => 'draft',
                'notes' => 'قيد تلقائي من الفاتورة',
                'created_by' => $user_id
            ];

            // تفاصيل القيد
            $details_data = [];

            if ($invoice['invoice_type'] == 'sales') {
                // فاتورة مبيعات
                // مدين: العملاء
                $details_data[] = [
                    'account_id' => 5, // حساب العملاء
                    'debit_amount' => $invoice['total_amount'],
                    'credit_amount' => 0,
                    'description' => 'مستحقات عميل - ' . $invoice['customer_name']
                ];

                // دائن: المبيعات
                $details_data[] = [
                    'account_id' => 11, // حساب المبيعات
                    'debit_amount' => 0,
                    'credit_amount' => $invoice['subtotal'],
                    'description' => 'إيرادات مبيعات'
                ];

                // دائن: الضرائب (إن وجدت)
                if ($invoice['tax_amount'] > 0) {
                    $details_data[] = [
                        'account_id' => 8, // حساب الضرائب
                        'debit_amount' => 0,
                        'credit_amount' => $invoice['tax_amount'],
                        'description' => 'ضريبة قيمة مضافة'
                    ];
                }
            } else {
                // فاتورة مشتريات
                // مدين: المشتريات
                $details_data[] = [
                    'account_id' => 15, // حساب المشتريات
                    'debit_amount' => $invoice['subtotal'],
                    'credit_amount' => 0,
                    'description' => 'مشتريات'
                ];

                // مدين: الضرائب (إن وجدت)
                if ($invoice['tax_amount'] > 0) {
                    $details_data[] = [
                        'account_id' => 8, // حساب الضرائب
                        'debit_amount' => $invoice['tax_amount'],
                        'credit_amount' => 0,
                        'description' => 'ضريبة قيمة مضافة'
                    ];
                }

                // دائن: الموردين
                $details_data[] = [
                    'account_id' => 7, // حساب الموردين
                    'debit_amount' => 0,
                    'credit_amount' => $invoice['total_amount'],
                    'description' => 'مستحقات مورد - ' . $invoice['supplier_name']
                ];
            }

            return $this->create($entry_data, $details_data);

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في إنشاء القيد من الفاتورة: ' . $e->getMessage()
            ];
        }
    }
}
?>
