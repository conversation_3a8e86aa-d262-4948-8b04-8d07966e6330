<?php
/**
 * SeaSystem - الشريط الجانبي الموحد
 * Unified Sidebar Component
 */

function renderSidebar($current_page = '') {
    $menu_items = [
        // القسم الرئيسي
        'dashboard.php' => [
            'icon' => 'fas fa-tachometer-alt',
            'title' => 'لوحة التحكم',
            'url' => 'dashboard.php',
            'group' => 'main'
        ],

        // إدارة الأطراف
        'customers.php' => [
            'icon' => 'fas fa-users',
            'title' => 'العملاء',
            'url' => 'customers.php',
            'group' => 'parties'
        ],
        'suppliers.php' => [
            'icon' => 'fas fa-truck',
            'title' => 'الموردين',
            'url' => 'suppliers.php',
            'group' => 'parties'
        ],

        // إدارة المخزون
        'inventory.php' => [
            'icon' => 'fas fa-boxes',
            'title' => 'المخزون والمنتجات',
            'url' => 'inventory.php',
            'group' => 'inventory'
        ],

        // العمليات المالية
        'invoices.php' => [
            'icon' => 'fas fa-file-invoice',
            'title' => 'الفواتير',
            'url' => 'invoices.php',
            'group' => 'financial'
        ],
        'payments.php' => [
            'icon' => 'fas fa-credit-card',
            'title' => 'المدفوعات',
            'url' => 'payments.php',
            'group' => 'financial'
        ],
        'journal.php' => [
            'icon' => 'fas fa-book',
            'title' => 'دفتر اليومية',
            'url' => 'journal.php',
            'group' => 'financial'
        ],

        // النظام المحاسبي
        'accounts.php' => [
            'icon' => 'fas fa-list',
            'title' => 'دليل الحسابات',
            'url' => 'accounts.php',
            'group' => 'accounting'
        ],
        'reports.php' => [
            'icon' => 'fas fa-chart-bar',
            'title' => 'التقارير',
            'url' => 'reports.php',
            'group' => 'accounting',
            'submenu' => [
                'report_sales.php' => [
                    'icon' => 'fas fa-chart-line',
                    'title' => 'تقرير المبيعات'
                ],
                'report_purchases.php' => [
                    'icon' => 'fas fa-shopping-cart',
                    'title' => 'تقرير المشتريات'
                ],
                'report_inventory.php' => [
                    'icon' => 'fas fa-boxes',
                    'title' => 'تقرير المخزون'
                ],
                'report_customer_statement.php' => [
                    'icon' => 'fas fa-users',
                    'title' => 'تقرير العملاء'
                ],
                'report_suppliers.php' => [
                    'icon' => 'fas fa-truck',
                    'title' => 'تقرير الموردين'
                ]
            ]
        ],

        // إدارة النظام
        'settings.php' => [
            'icon' => 'fas fa-cogs',
            'title' => 'الإعدادات',
            'url' => '#',
            'group' => 'system',
            'admin_only' => true,
            'submenu' => [
                'users.php' => [
                    'icon' => 'fas fa-user-cog',
                    'title' => 'إدارة المستخدمين',
                    'url' => 'users.php'
                ],
                'system_settings.php' => [
                    'icon' => 'fas fa-sliders-h',
                    'title' => 'إعدادات النظام',
                    'url' => 'system_settings.php'
                ],
                'backup.php' => [
                    'icon' => 'fas fa-database',
                    'title' => 'النسخ الاحتياطي',
                    'url' => 'backup.php'
                ],
                'security.php' => [
                    'icon' => 'fas fa-shield-alt',
                    'title' => 'الأمان',
                    'url' => 'security.php'
                ]
            ]
        ]
    ];

    echo '<div class="col-md-3 col-lg-2 sidebar">';
    echo '<nav class="nav flex-column">';

    $current_group = '';
    $group_titles = [
        'main' => 'لوحة التحكم',
        'parties' => 'إدارة الأطراف',
        'inventory' => 'إدارة المخزون',
        'financial' => 'العمليات المالية',
        'accounting' => 'النظام المحاسبي',
        'system' => 'إدارة النظام'
    ];

    foreach ($menu_items as $page => $item) {
        // فحص الصلاحيات للعناصر التي تتطلب صلاحيات إدارية
        if (isset($item['admin_only']) && $item['admin_only'] && !hasPermission('admin')) {
            continue; // تخطي هذا العنصر إذا لم تكن هناك صلاحيات
        }

        // إضافة عنوان المجموعة إذا تغيرت
        if ($current_group != $item['group']) {
            if ($current_group != '') {
                echo '<hr class="sidebar-divider my-2" style="border-color: #dee2e6; opacity: 0.5;">';
            }

            if ($item['group'] != 'main') {
                echo '<div class="sidebar-group-title px-3 py-2">';
                echo '<small class="text-muted fw-bold text-uppercase" style="font-size: 0.75rem; letter-spacing: 0.5px;">' . $group_titles[$item['group']] . '</small>';
                echo '</div>';
            }

            $current_group = $item['group'];
        }

        $active_class = '';
        // تحديد الصفحة النشطة
        if ($current_page == $page ||
            (empty($current_page) && $page == 'dashboard.php') ||
            (strpos($_SERVER['PHP_SELF'], $page) !== false)) {
            $active_class = 'active';
        }

        // عرض الرابط الرئيسي
        if (isset($item['submenu'])) {
            // رابط مع قائمة فرعية
            echo '<a class="nav-link ' . $active_class . ' dropdown-toggle" href="' . $item['url'] . '" data-bs-toggle="collapse" data-bs-target="#submenu-' . str_replace('.php', '', $page) . '" aria-expanded="false">';
            echo '<i class="' . $item['icon'] . ' me-2"></i>' . $item['title'];
            echo '<i class="fas fa-chevron-down ms-auto submenu-arrow"></i>';
            echo '</a>';

            // القائمة الفرعية
            echo '<div class="collapse" id="submenu-' . str_replace('.php', '', $page) . '">';
            echo '<div class="submenu">';

            foreach ($item['submenu'] as $sub_page => $sub_item) {
                $sub_active = '';
                if (strpos($_SERVER['PHP_SELF'], $sub_page) !== false) {
                    $sub_active = 'active';
                }

                echo '<a class="nav-link submenu-link ' . $sub_active . '" href="' . $sub_page . '">';
                echo '<i class="' . $sub_item['icon'] . ' me-2"></i>' . $sub_item['title'];
                echo '</a>';
            }

            echo '</div>';
            echo '</div>';
        } else {
            // رابط عادي
            echo '<a class="nav-link ' . $active_class . '" href="' . $item['url'] . '">';
            echo '<i class="' . $item['icon'] . ' me-2"></i>' . $item['title'];
            echo '</a>';
        }
    }

    echo '</nav>';
    echo '</div>';
}

function renderTopNavbar($current_user, $page_title = '') {
    echo '<nav class="navbar navbar-expand-lg navbar-dark">';
    echo '<div class="container-fluid">';
    echo '<a class="navbar-brand" href="dashboard.php">';
    echo '<i class="fas fa-calculator me-2"></i>' . SITE_NAME;
    echo '</a>';

    echo '<button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">';
    echo '<span class="navbar-toggler-icon"></span>';
    echo '</button>';

    echo '<div class="collapse navbar-collapse" id="navbarNav">';
    echo '<ul class="navbar-nav ms-auto">';
    echo '<li class="nav-item dropdown">';
    echo '<a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">';
    echo '<i class="fas fa-user-circle me-1"></i>' . htmlspecialchars($current_user['full_name']);
    echo '</a>';
    echo '<ul class="dropdown-menu">';
    echo '<li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>';
    echo '<li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>';
    echo '<li><hr class="dropdown-divider"></li>';
    echo '<li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>';
    echo '</ul>';
    echo '</li>';
    echo '</ul>';
    echo '</div>';
    echo '</div>';
    echo '</nav>';
}

function renderPageHeader($title, $subtitle = '', $icon = 'fas fa-home') {
    echo '<div class="page-header">';
    echo '<h1 class="mb-2">';
    echo '<i class="' . $icon . ' me-3"></i>' . $title;
    echo '</h1>';
    if (!empty($subtitle)) {
        echo '<p class="mb-0 opacity-75">' . $subtitle . '</p>';
    }
    echo '<small class="opacity-75">تم التحديث في: ' . date('Y-m-d H:i') . '</small>';
    echo '</div>';
}
?>

<!-- الشريط الجانبي الموحد المحسن -->
<style>
.sidebar {
    background: #f8f9fa;
    min-height: calc(100vh - 76px);
    padding: 1rem 0;
    border-left: 1px solid #dee2e6;
}

/* فواصل المجموعات */
.sidebar-divider {
    border-color: #dee2e6 !important;
    opacity: 0.5;
    margin: 0.75rem 1rem;
}

/* عناوين المجموعات */
.sidebar-group-title {
    padding: 0.5rem 1rem 0.25rem 1rem;
}

.sidebar-group-title small {
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    color: #6c757d !important;
    font-weight: 600 !important;
    text-transform: uppercase;
}

.sidebar .nav-link {
    color: #495057;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    margin: 0.2rem 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.sidebar .nav-link:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: bold;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* القوائم الفرعية */
.submenu {
    background: rgba(0,0,0,0.05);
    border-radius: 8px;
    margin: 0.25rem 0.5rem;
    padding: 0.25rem 0;
}

.submenu-link {
    padding: 0.5rem 1rem 0.5rem 2.5rem !important;
    font-size: 0.9rem;
    color: #6c757d !important;
    border-radius: 6px;
    margin: 0.1rem 0.5rem;
}

.submenu-link:hover {
    background: rgba(102, 126, 234, 0.1) !important;
    color: #667eea !important;
    transform: translateX(-3px);
}

.submenu-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 600;
}

.submenu-arrow {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.dropdown-toggle[aria-expanded="true"] .submenu-arrow {
    transform: rotate(180deg);
}
</style>
