<?php
/**
 * SeaSystem - فئة إدارة العملاء
 * Customer Management Class
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/NumberGenerator.php';

class Customer {
    private $db;
    private $table_name = "customers";

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }

    /**
     * الحصول على رمز عميل ذكي (مع إعادة التدوير)
     */
    public function getSmartCustomerCode() {
        require_once __DIR__ . '/SmartNumberManager.php';
        $smartManager = new SmartNumberManager();
        return $smartManager->getNextNumber('customer', 'CUS', 3, $_SESSION['user_id'] ?? null);
    }

    /**
     * تأكيد استخدام رمز العميل
     */
    public function confirmCustomerCode($customer_id = null) {
        require_once __DIR__ . '/SmartNumberManager.php';
        $smartManager = new SmartNumberManager();
        return $smartManager->confirmNumber('customer', $customer_id, $_SESSION['user_id'] ?? null);
    }

    /**
     * حذف رمز عميل وإضافته لإعادة التدوير
     */
    public function recycleCustomerCode($customer_code, $customer_id = null, $reason = '') {
        require_once __DIR__ . '/SmartNumberManager.php';
        $smartManager = new SmartNumberManager();
        return $smartManager->deleteNumber('customer', $customer_code, $customer_id, $_SESSION['user_id'] ?? null, $reason);
    }

    /**
     * إضافة عميل جديد
     */
    public function create($data) {
        try {
            // التحقق من عدم تكرار رمز العميل
            if ($this->isCustomerCodeExists($data['customer_code'])) {
                return [
                    'success' => false,
                    'message' => 'رمز العميل "' . $data['customer_code'] . '" موجود مسبقاً'
                ];
            }

            // التحقق من عدم تكرار البريد الإلكتروني
            if (!empty($data['email']) && $this->isEmailExists($data['email'])) {
                return [
                    'success' => false,
                    'message' => 'البريد الإلكتروني "' . $data['email'] . '" موجود مسبقاً'
                ];
            }

            // التحقق من عدم تكرار اسم العميل
            if ($this->isCustomerNameExists($data['name'])) {
                return [
                    'success' => false,
                    'message' => 'اسم العميل "' . $data['name'] . '" موجود مسبقاً'
                ];
            }

            // التحقق من عدم تكرار رقم الهاتف
            if (!empty($data['phone']) && $this->isPhoneExists($data['phone'])) {
                return [
                    'success' => false,
                    'message' => 'رقم الهاتف "' . $data['phone'] . '" موجود مسبقاً'
                ];
            }

            // التحقق من عدم تكرار الرقم الضريبي
            if (!empty($data['tax_number']) && $this->isTaxNumberExists($data['tax_number'])) {
                return [
                    'success' => false,
                    'message' => 'الرقم الضريبي "' . $data['tax_number'] . '" موجود مسبقاً'
                ];
            }

            // تأكيد استخدام الرقم الذكي
            $confirmed_result = $this->confirmCustomerCode();
            $final_customer_code = $confirmed_result['success'] ? $confirmed_result['number'] : $data['customer_code'];

            $sql = "INSERT INTO " . $this->table_name . "
                    (customer_code, name, email, phone, address, tax_number, credit_limit, opening_balance, current_balance)
                    VALUES (:customer_code, :name, :email, :phone, :address, :tax_number, :credit_limit, :opening_balance, :opening_balance)";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':customer_code', $final_customer_code);
            $stmt->bindParam(':name', $data['name']);
            $stmt->bindParam(':email', $data['email']);
            $stmt->bindParam(':phone', $data['phone']);
            $stmt->bindParam(':address', $data['address']);
            $stmt->bindParam(':tax_number', $data['tax_number']);
            $stmt->bindParam(':credit_limit', $data['credit_limit']);
            $stmt->bindParam(':opening_balance', $data['opening_balance']);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم إضافة العميل بنجاح',
                    'customer_id' => $this->db->lastInsertId(),
                    'customer_code' => $final_customer_code
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في إضافة العميل'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * تحديث بيانات العميل
     */
    public function update($id, $data) {
        try {
            // التحقق من وجود العميل
            if (!$this->exists($id)) {
                return [
                    'success' => false,
                    'message' => 'العميل غير موجود'
                ];
            }

            // التحقق من عدم تكرار رمز العميل
            if ($this->isCustomerCodeExists($data['customer_code'], $id)) {
                return [
                    'success' => false,
                    'message' => 'رمز العميل موجود مسبقاً'
                ];
            }

            // التحقق من عدم تكرار البريد الإلكتروني
            if (!empty($data['email']) && $this->isEmailExists($data['email'], $id)) {
                return [
                    'success' => false,
                    'message' => 'البريد الإلكتروني موجود مسبقاً'
                ];
            }

            $sql = "UPDATE " . $this->table_name . "
                    SET customer_code = :customer_code,
                        name = :name,
                        email = :email,
                        phone = :phone,
                        address = :address,
                        tax_number = :tax_number,
                        credit_limit = :credit_limit,
                        opening_balance = :opening_balance,
                        updated_at = NOW()
                    WHERE id = :id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':customer_code', $data['customer_code']);
            $stmt->bindParam(':name', $data['name']);
            $stmt->bindParam(':email', $data['email']);
            $stmt->bindParam(':phone', $data['phone']);
            $stmt->bindParam(':address', $data['address']);
            $stmt->bindParam(':tax_number', $data['tax_number']);
            $stmt->bindParam(':credit_limit', $data['credit_limit']);
            $stmt->bindParam(':opening_balance', $data['opening_balance']);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم تحديث بيانات العميل بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في تحديث بيانات العميل'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * حذف العميل
     */
    public function delete($id) {
        try {
            // التحقق من وجود العميل
            if (!$this->exists($id)) {
                return [
                    'success' => false,
                    'message' => 'العميل غير موجود'
                ];
            }

            // التحقق من عدم وجود فواتير للعميل
            if ($this->hasInvoices($id)) {
                // إلغاء تفعيل العميل بدلاً من حذفه
                return $this->deactivate($id);
            }

            // الحصول على بيانات العميل قبل الحذف
            $customer = $this->getById($id);
            if (!$customer) {
                return [
                    'success' => false,
                    'message' => 'لا يمكن الحصول على بيانات العميل'
                ];
            }

            $this->db->beginTransaction();

            $sql = "DELETE FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                // إضافة رقم العميل لإعادة التدوير
                $recycle_result = $this->recycleCustomerCode($customer['customer_code'], $id, 'حذف عميل');

                if (!$recycle_result['success']) {
                    error_log('فشل في إعادة تدوير رقم العميل: ' . ($recycle_result['error'] ?? 'خطأ غير محدد'));
                }

                $this->db->commit();

                return [
                    'success' => true,
                    'message' => 'تم حذف العميل وإضافة رقمه لإعادة الاستخدام',
                    'recycled_number' => $customer['customer_code']
                ];
            } else {
                $this->db->rollBack();
                return [
                    'success' => false,
                    'message' => 'فشل في حذف العميل'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * إلغاء تفعيل العميل
     */
    public function deactivate($id) {
        try {
            $sql = "UPDATE " . $this->table_name . " SET is_active = 0, updated_at = NOW() WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم إلغاء تفعيل العميل بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في إلغاء تفعيل العميل'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * تفعيل العميل
     */
    public function activate($id) {
        try {
            $sql = "UPDATE " . $this->table_name . " SET is_active = 1, updated_at = NOW() WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم تفعيل العميل بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في تفعيل العميل'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * الحصول على بيانات عميل واحد
     */
    public function getById($id) {
        try {
            $sql = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();

            return $stmt->fetch();
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * الحصول على جميع العملاء
     */
    public function getAll($active_only = true) {
        try {
            $sql = "SELECT * FROM " . $this->table_name;

            if ($active_only) {
                $sql .= " WHERE is_active = 1";
            }

            $sql .= " ORDER BY name";

            $stmt = $this->db->prepare($sql);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * البحث في العملاء
     */
    public function search($keyword, $active_only = true) {
        try {
            $sql = "SELECT * FROM " . $this->table_name . "
                    WHERE (customer_code LIKE :keyword OR name LIKE :keyword OR email LIKE :keyword OR phone LIKE :keyword)";

            if ($active_only) {
                $sql .= " AND is_active = 1";
            }

            $sql .= " ORDER BY name";

            $stmt = $this->db->prepare($sql);
            $keyword = "%$keyword%";
            $stmt->bindParam(':keyword', $keyword);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * تحديث رصيد العميل
     */
    public function updateBalance($customer_id, $amount, $operation = 'add') {
        try {
            if ($operation == 'add') {
                $sql = "UPDATE " . $this->table_name . "
                        SET current_balance = current_balance + :amount, updated_at = NOW()
                        WHERE id = :customer_id";
            } else {
                $sql = "UPDATE " . $this->table_name . "
                        SET current_balance = current_balance - :amount, updated_at = NOW()
                        WHERE id = :customer_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':amount', $amount);
            $stmt->bindParam(':customer_id', $customer_id);

            return $stmt->execute();
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * الحصول على رصيد العميل
     */
    public function getBalance($customer_id) {
        try {
            $sql = "SELECT current_balance FROM " . $this->table_name . " WHERE id = :customer_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->execute();

            $result = $stmt->fetch();
            return $result ? $result['current_balance'] : 0;
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * الحصول على فواتير العميل
     */
    public function getInvoices($customer_id, $status = null) {
        try {
            $sql = "SELECT * FROM invoices WHERE customer_id = :customer_id";

            if ($status) {
                $sql .= " AND status = :status";
            }

            $sql .= " ORDER BY invoice_date DESC";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':customer_id', $customer_id);

            if ($status) {
                $stmt->bindParam(':status', $status);
            }

            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على مدفوعات العميل
     */
    public function getPayments($customer_id) {
        try {
            $sql = "SELECT * FROM payments WHERE customer_id = :customer_id ORDER BY payment_date DESC";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * إحصائيات العميل
     */
    public function getStatistics($customer_id) {
        try {
            $sql = "SELECT
                        COUNT(*) as total_invoices,
                        SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as total_paid,
                        SUM(CASE WHEN status != 'paid' THEN total_amount ELSE 0 END) as total_outstanding,
                        SUM(total_amount) as total_sales
                    FROM invoices
                    WHERE customer_id = :customer_id AND invoice_type = 'sales'";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->execute();

            return $stmt->fetch();
        } catch (Exception $e) {
            return [
                'total_invoices' => 0,
                'total_paid' => 0,
                'total_outstanding' => 0,
                'total_sales' => 0
            ];
        }
    }

    /**
     * التحقق من وجود العميل
     */
    private function exists($id) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من تكرار رمز العميل
     */
    private function isCustomerCodeExists($customer_code, $exclude_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE customer_code = :customer_code";

            if ($exclude_id) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':customer_code', $customer_code);

            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }

            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من تكرار البريد الإلكتروني
     */
    private function isEmailExists($email, $exclude_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE email = :email";

            if ($exclude_id) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':email', $email);

            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }

            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من تكرار اسم العميل
     */
    private function isCustomerNameExists($name, $exclude_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE LOWER(TRIM(name)) = LOWER(TRIM(:name))";

            if ($exclude_id) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':name', $name);

            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }

            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من تكرار رقم الهاتف
     */
    private function isPhoneExists($phone, $exclude_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE phone = :phone";

            if ($exclude_id) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':phone', $phone);

            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }

            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من تكرار الرقم الضريبي
     */
    private function isTaxNumberExists($tax_number, $exclude_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE tax_number = :tax_number";

            if ($exclude_id) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':tax_number', $tax_number);

            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }

            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    // دوال عامة للتحقق من التكرار (للاستخدام مع AJAX)
    public function checkCustomerCodeExists($customer_code, $exclude_id = null) {
        return $this->isCustomerCodeExists($customer_code, $exclude_id);
    }

    public function checkCustomerNameExists($name, $exclude_id = null) {
        return $this->isCustomerNameExists($name, $exclude_id);
    }

    public function checkEmailExists($email, $exclude_id = null) {
        return $this->isEmailExists($email, $exclude_id);
    }

    public function checkPhoneExists($phone, $exclude_id = null) {
        return $this->isPhoneExists($phone, $exclude_id);
    }

    public function checkTaxNumberExists($tax_number, $exclude_id = null) {
        return $this->isTaxNumberExists($tax_number, $exclude_id);
    }

    /**
     * التحقق من وجود فواتير للعميل
     */
    private function hasInvoices($id) {
        try {
            $sql = "SELECT COUNT(*) FROM invoices WHERE customer_id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    // دوال الإحصائيات المالية
    public function getTotalSalesAmount() {
        try {
            $sql = "SELECT COALESCE(SUM(total_amount), 0) FROM invoices WHERE customer_id IN (SELECT id FROM " . $this->table_name . ") AND invoice_type = 'sales'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (Exception $e) {
            return 0;
        }
    }

    public function getTotalOutstandingAmount() {
        try {
            $sql = "SELECT COALESCE(SUM(outstanding_amount), 0) FROM invoices WHERE customer_id IN (SELECT id FROM " . $this->table_name . ") AND status != 'paid'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (Exception $e) {
            return 0;
        }
    }

    public function getTotalPaidAmount() {
        try {
            $sql = "SELECT COALESCE(SUM(paid_amount), 0) FROM invoices WHERE customer_id IN (SELECT id FROM " . $this->table_name . ")";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (Exception $e) {
            return 0;
        }
    }

    public function getAverageOrderValue() {
        try {
            $sql = "SELECT COALESCE(AVG(total_amount), 0) FROM invoices WHERE customer_id IN (SELECT id FROM " . $this->table_name . ") AND invoice_type = 'sales'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (Exception $e) {
            return 0;
        }
    }
}
?>
