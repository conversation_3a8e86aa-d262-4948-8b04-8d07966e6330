/**
 * SeaSystem - ملف التنسيقات الرئيسي
 * Main CSS Stylesheet
 */

/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 10px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* إعدادات عامة */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: var(--light-color);
}

/* تحسين الخطوط العربية */
body, .form-control, .btn, .nav-link {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

/* الأزرار المخصصة */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    padding: 0.5rem 1.5rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    border-radius: 15px 15px 0 0 !important;
    padding: 1rem 1.5rem;
}

/* الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 1rem;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-color: #e9ecef;
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* حقول الإدخال */
.form-control {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    font-size: 0.95rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* الشارات */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* شريط التنقل */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

/* الشريط الجانبي */
.sidebar {
    background: white;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 76px);
}

.sidebar .nav-link {
    color: #495057;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.sidebar .nav-link:hover {
    background-color: #f8f9fa;
    color: var(--primary-color);
    transform: translateX(-5px);
}

.sidebar .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* بطاقات الإحصائيات */
.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: none;
    height: 100%;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-left: 1rem;
}

.stat-icon.primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.stat-icon.success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.stat-icon.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
}

/* بطاقة الترحيب */
.welcome-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
}

/* تحسينات للجداول */
.table-card {
    background: white;
    border-radius: 15px;
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.table-responsive {
    border-radius: 0 0 15px 15px;
}

/* أزرار الإجراءات */
.action-buttons .btn {
    margin: 0 0.25rem;
    padding: 0.375rem 0.75rem;
}

/* مسافات موحدة للأزرار */
.btn-group .btn {
    margin-left: 0.25rem;
}

.btn-group .btn:first-child {
    margin-left: 0;
}

/* مسافات للأزرار المتجاورة */
.d-flex.gap-2 .btn {
    margin: 0;
}

/* تحسين مظهر الأزرار الصغيرة */
.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    min-width: 36px; /* حد أدنى لعرض الزر */
}

/* تحسين مظهر الأزرار في الجداول */
.table td .d-flex {
    flex-wrap: nowrap;
    justify-content: flex-start;
}

/* تحسين مظهر الأزرار في الهواتف */
@media (max-width: 768px) {
    .d-flex.gap-2 {
        flex-wrap: wrap;
    }

    .btn-sm {
        margin-bottom: 0.25rem;
    }
}

/* تنسيق النصوص التوضيحية تحت الأزرار */
.btn + small {
    font-size: 0.7rem;
    line-height: 1;
    white-space: nowrap;
}

/* تحسين مظهر الأزرار مع النصوص التوضيحية */
.d-flex .text-center {
    min-width: 50px;
}

/* تحسين المسافات في الجداول */
.table td .d-flex .text-center {
    margin-bottom: 0.5rem;
}

/* تحسين النصوص التوضيحية في الهواتف */
@media (max-width: 576px) {
    .btn + small {
        font-size: 0.65rem;
    }

    .d-flex .text-center {
        min-width: 45px;
    }
}

/* تحسينات للنماذج */
.form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    margin-bottom: 1rem;
}

/* تحسينات للقوائم المنسدلة */
.dropdown-menu {
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
}

/* تحسينات للصفحات */
.page-header {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.page-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    color: #6c757d;
    font-size: 0.95rem;
}

/* تحسينات للتنقل */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: #6c757d;
}

/* تحسينات للحالات الفارغة */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* تحسينات للتحميل */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.spinner-border {
    color: var(--primary-color);
}

/* تحسينات للطباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .action-buttons {
        display: none !important;
    }

    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 76px;
        left: -100%;
        width: 280px;
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .sidebar.show {
        left: 0;
    }

    .stat-card {
        margin-bottom: 1rem;
    }

    .welcome-card {
        text-align: center;
    }

    .table-responsive {
        font-size: 0.9rem;
    }

    .action-buttons .btn {
        margin-bottom: 0.5rem;
        width: 100%;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1200px) {
    .container-fluid {
        max-width: 1400px;
        margin: 0 auto;
    }
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* تحسينات للإمكانية */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* تحسينات للتركيز */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* تحسينات للألوان في الوضع المظلم */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #1a1a1a;
        --dark-color: #ffffff;
    }

    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }

    .card,
    .sidebar {
        background-color: #2d2d2d;
        color: #ffffff;
    }

    .table {
        color: #ffffff;
    }

    .table thead th {
        background-color: #3d3d3d;
        color: #ffffff;
    }

    .form-control {
        background-color: #2d2d2d;
        border-color: #4d4d4d;
        color: #ffffff;
    }
}
