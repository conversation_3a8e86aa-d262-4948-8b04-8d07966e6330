<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأزرار</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>اختبار الأزرار</h2>
        
        <div class="d-flex gap-2 mt-4">
            <!-- زر العرض -->
            <div class="text-center">
                <button type="button" class="btn btn-outline-info btn-sm"
                        onclick="viewCustomer(123)">
                    <i class="fas fa-eye"></i>
                </button>
                <small class="d-block text-muted mt-1">عرض</small>
            </div>
            
            <!-- زر التعديل -->
            <div class="text-center">
                <button type="button" class="btn btn-outline-primary btn-sm"
                        onclick="editCustomer({id: 123, name: 'اختبار'})">
                    <i class="fas fa-edit"></i>
                </button>
                <small class="d-block text-muted mt-1">تعديل</small>
            </div>
            
            <!-- زر الحذف -->
            <div class="text-center">
                <button type="button" class="btn btn-outline-danger btn-sm"
                        onclick="deleteCustomer(123)">
                    <i class="fas fa-trash"></i>
                </button>
                <small class="d-block text-muted mt-1">حذف</small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // دوال الاختبار
        function viewCustomer(customerId) {
            alert('تم النقر على زر العرض - رقم العميل: ' + customerId);
        }
        
        function editCustomer(customer) {
            alert('تم النقر على زر التعديل - اسم العميل: ' + customer.name);
        }
        
        function deleteCustomer(customerId) {
            if (confirm('هل أنت متأكد من حذف العميل رقم ' + customerId + '؟')) {
                alert('تم تأكيد الحذف');
            }
        }
    </script>
</body>
</html>
