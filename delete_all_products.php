<?php
/**
 * SeaSystem - حذف جميع المنتجات
 * Delete All Products Script
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/auth.php';

// التأكد من تسجيل الدخول
requireLogin();

// الحصول على بيانات المستخدم الحالي
$current_user = getCurrentUser();

// التحقق من صلاحيات المدير
if ($current_user['role'] !== 'admin') {
    die('غير مسموح لك بتنفيذ هذا الإجراء');
}

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حذف جميع المنتجات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    
    <style>
        body { padding-top: 80px !important; }
        .delete-card { background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.08); padding: 2rem; margin-bottom: 2rem; }
        .danger-zone { border: 2px solid #dc3545; border-radius: 10px; padding: 2rem; background: #fff5f5; }
        .step-card { background: #f8f9fa; border-radius: 10px; padding: 1.5rem; margin-bottom: 1rem; }
        .progress-step { background: #e9ecef; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; margin-left: 1rem; }
        .progress-step.active { background: #dc3545; color: white; }
        .progress-step.completed { background: #28a745; color: white; }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12 p-4">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="page-title text-danger">
                                <i class="fas fa-trash-alt me-2"></i>حذف جميع المنتجات
                            </h1>
                            <p class="page-subtitle">حذف جميع المنتجات من قاعدة البيانات وتنظيف النظام</p>
                        </div>
                        <div class="col-auto">
                            <a href="dashboard.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-2"></i>العودة للوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>

                <!-- تحذير -->
                <div class="alert alert-danger">
                    <h4><i class="fas fa-exclamation-triangle me-2"></i>تحذير مهم!</h4>
                    <p class="mb-0">
                        هذا الإجراء سيحذف <strong>جميع المنتجات</strong> من قاعدة البيانات بشكل نهائي. 
                        تأكد من أنك تريد المتابعة قبل النقر على زر الحذف.
                    </p>
                </div>

                <?php
                // تنفيذ الحذف إذا تم الطلب
                if (isset($_POST['confirm_delete']) && $_POST['confirm_delete'] === 'DELETE_ALL_PRODUCTS') {
                    try {
                        $database = new Database();
                        $pdo = $database->getConnection();
                        
                        echo '<div class="delete-card">';
                        echo '<h3 class="text-danger mb-4"><i class="fas fa-cog fa-spin me-2"></i>جاري تنفيذ عملية الحذف...</h3>';
                        
                        // 1. حذف جميع المنتجات
                        echo '<div class="step-card">';
                        echo '<div class="d-flex align-items-center">';
                        echo '<div class="progress-step active">1</div>';
                        echo '<div>';
                        echo '<h5>حذف جميع المنتجات</h5>';
                        
                        $delete_products = $pdo->exec("DELETE FROM products");
                        echo "<p class='text-success'>✅ تم حذف {$delete_products} منتج</p>";
                        echo '</div></div></div>';
                        
                        // 2. إعادة تعيين AUTO_INCREMENT
                        echo '<div class="step-card">';
                        echo '<div class="d-flex align-items-center">';
                        echo '<div class="progress-step active">2</div>';
                        echo '<div>';
                        echo '<h5>إعادة تعيين معرفات المنتجات</h5>';
                        
                        $pdo->exec("ALTER TABLE products AUTO_INCREMENT = 1");
                        echo "<p class='text-success'>✅ تم إعادة تعيين AUTO_INCREMENT</p>";
                        echo '</div></div></div>';
                        
                        // 3. حذف حركات المخزون المرتبطة (إن وجدت)
                        echo '<div class="step-card">';
                        echo '<div class="d-flex align-items-center">';
                        echo '<div class="progress-step active">3</div>';
                        echo '<div>';
                        echo '<h5>تنظيف حركات المخزون</h5>';
                        
                        try {
                            $delete_movements = $pdo->exec("DELETE FROM inventory_movements WHERE product_id NOT IN (SELECT id FROM products)");
                            echo "<p class='text-success'>✅ تم حذف {$delete_movements} حركة مخزون</p>";
                        } catch (Exception $e) {
                            echo "<p class='text-warning'>⚠️ جدول حركات المخزون غير موجود أو فارغ</p>";
                        }
                        echo '</div></div></div>';
                        
                        // 4. حذف عناصر الفواتير المرتبطة (إن وجدت)
                        echo '<div class="step-card">';
                        echo '<div class="d-flex align-items-center">';
                        echo '<div class="progress-step active">4</div>';
                        echo '<div>';
                        echo '<h5>تنظيف عناصر الفواتير</h5>';
                        
                        try {
                            $delete_invoice_items = $pdo->exec("DELETE FROM invoice_items WHERE product_id NOT IN (SELECT id FROM products)");
                            echo "<p class='text-success'>✅ تم حذف {$delete_invoice_items} عنصر فاتورة</p>";
                        } catch (Exception $e) {
                            echo "<p class='text-warning'>⚠️ جدول عناصر الفواتير غير موجود أو فارغ</p>";
                        }
                        echo '</div></div></div>';
                        
                        // 5. التحقق من النتيجة
                        echo '<div class="step-card">';
                        echo '<div class="d-flex align-items-center">';
                        echo '<div class="progress-step completed">✓</div>';
                        echo '<div>';
                        echo '<h5>التحقق من النتيجة</h5>';
                        
                        $count_check = $pdo->query("SELECT COUNT(*) FROM products")->fetchColumn();
                        if ($count_check == 0) {
                            echo "<p class='text-success'>✅ تم حذف جميع المنتجات بنجاح</p>";
                            echo "<p class='text-success'>✅ قاعدة البيانات نظيفة ومستعدة لإدخال منتجات جديدة</p>";
                        } else {
                            echo "<p class='text-danger'>❌ ما زال يوجد {$count_check} منتج في قاعدة البيانات</p>";
                        }
                        echo '</div></div></div>';
                        
                        echo '<div class="alert alert-success mt-4">';
                        echo '<h4><i class="fas fa-check-circle me-2"></i>تمت العملية بنجاح!</h4>';
                        echo '<p class="mb-0">تم حذف جميع المنتجات وتنظيف قاعدة البيانات. يمكنك الآن إضافة منتجات جديدة.</p>';
                        echo '</div>';
                        
                        echo '<div class="text-center mt-4">';
                        echo '<a href="inventory.php#add-product-tab" class="btn btn-success btn-lg me-3">';
                        echo '<i class="fas fa-plus me-2"></i>إضافة منتجات جديدة';
                        echo '</a>';
                        echo '<a href="dashboard.php" class="btn btn-primary btn-lg">';
                        echo '<i class="fas fa-tachometer-alt me-2"></i>العودة للوحة التحكم';
                        echo '</a>';
                        echo '</div>';
                        
                        echo '</div>';
                        
                    } catch (Exception $e) {
                        echo '<div class="alert alert-danger">';
                        echo '<h4><i class="fas fa-exclamation-triangle me-2"></i>خطأ في عملية الحذف!</h4>';
                        echo '<p class="mb-0">حدث خطأ: ' . htmlspecialchars($e->getMessage()) . '</p>';
                        echo '</div>';
                    }
                } else {
                    // عرض نموذج التأكيد
                ?>
                
                <!-- معلومات قبل الحذف -->
                <div class="delete-card">
                    <h3 class="text-info mb-4">
                        <i class="fas fa-info-circle me-2"></i>معلومات قبل الحذف
                    </h3>
                    
                    <?php
                    try {
                        $database = new Database();
                        $pdo = $database->getConnection();
                        
                        // عدد المنتجات الحالي
                        $products_count = $pdo->query("SELECT COUNT(*) FROM products")->fetchColumn();
                        
                        // عدد التصنيفات
                        $categories_count = $pdo->query("SELECT COUNT(*) FROM product_categories")->fetchColumn();
                        
                        // عدد وحدات القياس
                        $units_count = $pdo->query("SELECT COUNT(*) FROM units_of_measure")->fetchColumn();
                        
                        echo '<div class="row">';
                        echo '<div class="col-md-4 text-center">';
                        echo '<i class="fas fa-boxes fa-3x text-primary mb-2"></i>';
                        echo '<h4>' . $products_count . '</h4>';
                        echo '<p>منتج سيتم حذفه</p>';
                        echo '</div>';
                        
                        echo '<div class="col-md-4 text-center">';
                        echo '<i class="fas fa-tags fa-3x text-info mb-2"></i>';
                        echo '<h4>' . $categories_count . '</h4>';
                        echo '<p>تصنيف (سيبقى)</p>';
                        echo '</div>';
                        
                        echo '<div class="col-md-4 text-center">';
                        echo '<i class="fas fa-ruler fa-3x text-warning mb-2"></i>';
                        echo '<h4>' . $units_count . '</h4>';
                        echo '<p>وحدة قياس (ستبقى)</p>';
                        echo '</div>';
                        echo '</div>';
                        
                        if ($products_count == 0) {
                            echo '<div class="alert alert-info mt-3">';
                            echo '<i class="fas fa-info-circle me-2"></i>';
                            echo 'لا توجد منتجات في قاعدة البيانات حالياً.';
                            echo '</div>';
                        }
                        
                    } catch (Exception $e) {
                        echo '<div class="alert alert-warning">';
                        echo '<i class="fas fa-exclamation-triangle me-2"></i>';
                        echo 'لا يمكن الوصول لقاعدة البيانات: ' . htmlspecialchars($e->getMessage());
                        echo '</div>';
                    }
                    ?>
                </div>

                <!-- منطقة الخطر -->
                <div class="delete-card">
                    <div class="danger-zone">
                        <h3 class="text-danger mb-4">
                            <i class="fas fa-skull-crossbones me-2"></i>منطقة الخطر
                        </h3>
                        
                        <p class="mb-4">
                            <strong>ما سيتم حذفه:</strong>
                        </p>
                        <ul class="text-danger">
                            <li>جميع المنتجات المسجلة</li>
                            <li>جميع حركات المخزون المرتبطة</li>
                            <li>جميع عناصر الفواتير المرتبطة</li>
                            <li>جميع البيانات المرتبطة بالمنتجات</li>
                        </ul>
                        
                        <p class="mb-4">
                            <strong>ما سيبقى:</strong>
                        </p>
                        <ul class="text-success">
                            <li>تصنيفات المنتجات</li>
                            <li>وحدات القياس</li>
                            <li>الفواتير (بدون عناصر)</li>
                            <li>العملاء والموردين</li>
                        </ul>
                        
                        <form method="POST" onsubmit="return confirmDelete()">
                            <div class="mb-3">
                                <label for="confirm_text" class="form-label">
                                    <strong>للتأكيد، اكتب النص التالي بالضبط:</strong>
                                </label>
                                <code class="d-block mb-2 p-2 bg-light">DELETE_ALL_PRODUCTS</code>
                                <input type="text" class="form-control" id="confirm_text" name="confirm_delete" 
                                       placeholder="اكتب النص هنا للتأكيد" required>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-danger btn-lg">
                                    <i class="fas fa-trash-alt me-2"></i>حذف جميع المنتجات نهائياً
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <?php } ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
    
    <script>
        function confirmDelete() {
            const confirmText = document.getElementById('confirm_text').value;
            if (confirmText !== 'DELETE_ALL_PRODUCTS') {
                alert('يجب كتابة النص بالضبط كما هو مطلوب');
                return false;
            }
            
            return confirm('هل أنت متأكد من حذف جميع المنتجات؟ هذا الإجراء لا يمكن التراجع عنه!');
        }
    </script>
</body>
</html>
