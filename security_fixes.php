<?php
/**
 * SeaSystem - إصلاح المشاكل الأمنية الحرجة
 * Critical Security Fixes
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/auth.php';

// التأكد من صلاحيات المدير
if (!isLoggedIn() || !hasPermission('admin')) {
    die('❌ غير مصرح لك بتشغيل هذا السكريبت');
}

// منع التشغيل بدون تأكيد
if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'security_fix') {
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>إصلاح المشاكل الأمنية</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body class="bg-light">
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h3><i class="fas fa-shield-alt me-2"></i>إصلاح المشاكل الأمنية الحرجة</h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger">
                                <h5><i class="fas fa-exclamation-triangle me-2"></i>مشاكل أمنية حرجة!</h5>
                                <p>تم اكتشاف مشاكل أمنية حرجة تحتاج إصلاح فوري!</p>
                            </div>
                            
                            <h5>المشاكل التي سيتم إصلاحها:</h5>
                            <ul class="text-danger">
                                <li>🚨 تعطيل وضع التطوير في auth.php</li>
                                <li>🚨 تغيير APP_ENV إلى production</li>
                                <li>🚨 تعطيل DEBUG_MODE</li>
                                <li>🚨 تفعيل كلمات المرور الإجبارية</li>
                                <li>🚨 تحديث كلمة مرور المدير</li>
                                <li>🚨 إنشاء جدول user_activities</li>
                                <li>🚨 تفعيل حماية CSRF</li>
                            </ul>
                            
                            <div class="alert alert-warning">
                                <strong>تحذير:</strong> بعد تطبيق هذه الإصلاحات، ستحتاج لاستخدام كلمة مرور قوية لتسجيل الدخول!
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="?confirm=security_fix" class="btn btn-danger btn-lg">
                                    <i class="fas fa-shield-alt me-2"></i>تطبيق الإصلاحات الأمنية
                                </a>
                                <a href="dashboard.php" class="btn btn-secondary">إلغاء والعودة</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit();
}

// بدء عملية الإصلاح
echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>إصلاح المشاكل الأمنية - جاري التنفيذ</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        .fix-step { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .step-success { background: #d4edda; border: 1px solid #c3e6cb; }
        .step-warning { background: #fff3cd; border: 1px solid #ffeaa7; }
        .step-danger { background: #f8d7da; border: 1px solid #f5c6cb; }
        .step-info { background: #d1ecf1; border: 1px solid #bee5eb; }
    </style>
</head>
<body class='bg-light'>
<div class='container mt-4'>
    <h2 class='text-center mb-4'><i class='fas fa-shield-alt me-2'></i>إصلاح المشاكل الأمنية</h2>";

echo "<div class='fix-step step-info'>";
echo "<h4><i class='fas fa-play me-2'></i>بدء عملية الإصلاح...</h4>";
echo "</div>";

// 1. إصلاح ملف constants.php
echo "<div class='fix-step step-warning'>";
echo "<h4><i class='fas fa-cog me-2'></i>إصلاح إعدادات النظام...</h4>";

$constantsFile = 'config/constants.php';
if (file_exists($constantsFile)) {
    $content = file_get_contents($constantsFile);
    
    // تغيير APP_ENV إلى production
    $content = preg_replace(
        "/define\('APP_ENV',\s*'development'\)/",
        "define('APP_ENV', 'production')",
        $content
    );
    
    // تعطيل DEBUG_MODE
    $content = preg_replace(
        "/define\('DEBUG_MODE',\s*true\)/",
        "define('DEBUG_MODE', false)",
        $content
    );
    
    if (file_put_contents($constantsFile, $content)) {
        echo "<p class='text-success'>✅ تم تحديث إعدادات النظام</p>";
    } else {
        echo "<p class='text-danger'>❌ فشل في تحديث إعدادات النظام</p>";
    }
} else {
    echo "<p class='text-warning'>⚠️ ملف constants.php غير موجود</p>";
}
echo "</div>";

// 2. إصلاح ملف auth.php
echo "<div class='fix-step step-warning'>";
echo "<h4><i class='fas fa-key me-2'></i>إصلاح نظام المصادقة...</h4>";

$authFile = 'includes/auth.php';
if (file_exists($authFile)) {
    $content = file_get_contents($authFile);
    
    // البحث عن التعليق المؤقت وإزالته
    $tempLoginComment = '// تسجيل دخول مؤقت بدون كلمة مرور (للتطوير فقط)';
    $tempLoginComment2 = '// TODO: إعادة تفعيل التحقق من كلمة المرور عند الانتهاء من التطوير';
    
    if (strpos($content, $tempLoginComment) !== false) {
        // استبدال الكود المؤقت بالكود الصحيح
        $oldCode = $tempLoginComment . "\n                // TODO: إعادة تفعيل التحقق من كلمة المرور عند الانتهاء من التطوير";
        $newCode = "if (!password_verify(\$password, \$user['password'])) {\n                    \$this->rateLimiter->recordFailedAttempt(\$username);\n                    Security::logSecurityEvent('login_failed', \"Username: \$username, Reason: Invalid password\");\n                    return [\n                        'success' => false,\n                        'message' => 'كلمة المرور غير صحيحة'\n                    ];\n                }";
        
        $content = str_replace($oldCode, $newCode, $content);
        
        if (file_put_contents($authFile, $content)) {
            echo "<p class='text-success'>✅ تم تفعيل التحقق من كلمة المرور</p>";
        } else {
            echo "<p class='text-danger'>❌ فشل في تحديث نظام المصادقة</p>";
        }
    } else {
        echo "<p class='text-info'>ℹ️ نظام المصادقة محدث مسبقاً</p>";
    }
} else {
    echo "<p class='text-danger'>❌ ملف auth.php غير موجود</p>";
}
echo "</div>";

// 3. إنشاء جدول user_activities
echo "<div class='fix-step step-warning'>";
echo "<h4><i class='fas fa-database me-2'></i>إنشاء جداول الأمان...</h4>";

try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    // إنشاء جدول user_activities
    $sql = "CREATE TABLE IF NOT EXISTS user_activities (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        action VARCHAR(100) NOT NULL,
        description TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_action (action),
        INDEX idx_created_at (created_at)
    )";
    
    $db->exec($sql);
    echo "<p class='text-success'>✅ تم إنشاء جدول user_activities</p>";
    
    // إنشاء جدول security_logs إذا لم يكن موجوداً
    $sql = "CREATE TABLE IF NOT EXISTS security_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        action VARCHAR(100) NOT NULL,
        ip_address VARCHAR(45) NOT NULL,
        user_agent TEXT,
        details TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_action (action),
        INDEX idx_ip (ip_address),
        INDEX idx_created_at (created_at)
    )";
    
    $db->exec($sql);
    echo "<p class='text-success'>✅ تم إنشاء جدول security_logs</p>";
    
} catch (Exception $e) {
    echo "<p class='text-danger'>❌ خطأ في إنشاء الجداول: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 4. تحديث كلمة مرور المدير
echo "<div class='fix-step step-warning'>";
echo "<h4><i class='fas fa-user-shield me-2'></i>تحديث كلمة مرور المدير...</h4>";

try {
    // كلمة مرور قوية جديدة
    $newPassword = 'SeaAdmin@2025!';
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    $sql = "UPDATE users SET password = :password WHERE username = 'admin'";
    $stmt = $db->prepare($sql);
    $stmt->bindParam(':password', $hashedPassword);
    
    if ($stmt->execute()) {
        echo "<p class='text-success'>✅ تم تحديث كلمة مرور المدير</p>";
        echo "<div class='alert alert-warning'>";
        echo "<strong>كلمة المرور الجديدة:</strong> <code>$newPassword</code>";
        echo "<br><small>احفظ هذه كلمة المرور في مكان آمن!</small>";
        echo "</div>";
    } else {
        echo "<p class='text-danger'>❌ فشل في تحديث كلمة مرور المدير</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='text-danger'>❌ خطأ في تحديث كلمة المرور: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 5. إضافة إعدادات أمان إضافية
echo "<div class='fix-step step-warning'>";
echo "<h4><i class='fas fa-cogs me-2'></i>إضافة إعدادات الأمان...</h4>";

try {
    // إنشاء جدول system_settings إذا لم يكن موجوداً
    $sql = "CREATE TABLE IF NOT EXISTS system_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
        description TEXT,
        is_public BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    $db->exec($sql);
    echo "<p class='text-success'>✅ تم إنشاء جدول system_settings</p>";
    
    // إضافة إعدادات أمان افتراضية
    $securitySettings = [
        ['max_login_attempts', '5', 'number', 'الحد الأقصى لمحاولات تسجيل الدخول'],
        ['session_timeout', '3600', 'number', 'مهلة انتهاء الجلسة بالثواني'],
        ['password_min_length', '8', 'number', 'الحد الأدنى لطول كلمة المرور'],
        ['enable_2fa', 'false', 'boolean', 'تفعيل المصادقة الثنائية'],
        ['force_https', 'true', 'boolean', 'إجبار استخدام HTTPS'],
        ['enable_csrf', 'true', 'boolean', 'تفعيل حماية CSRF']
    ];
    
    foreach ($securitySettings as $setting) {
        $sql = "INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, description) 
                VALUES (?, ?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute($setting);
    }
    
    echo "<p class='text-success'>✅ تم إضافة إعدادات الأمان الافتراضية</p>";
    
} catch (Exception $e) {
    echo "<p class='text-danger'>❌ خطأ في إضافة إعدادات الأمان: " . $e->getMessage() . "</p>";
}
echo "</div>";

// النتائج النهائية
echo "<div class='fix-step step-success'>";
echo "<h4><i class='fas fa-check-circle me-2'></i>تم الانتهاء من الإصلاحات الأمنية!</h4>";
echo "<ul>";
echo "<li>✅ تم تعطيل وضع التطوير</li>";
echo "<li>✅ تم تفعيل التحقق من كلمة المرور</li>";
echo "<li>✅ تم تحديث كلمة مرور المدير</li>";
echo "<li>✅ تم إنشاء جداول الأمان</li>";
echo "<li>✅ تم إضافة إعدادات الأمان</li>";
echo "</ul>";
echo "</div>";

echo "<div class='fix-step step-danger'>";
echo "<h4><i class='fas fa-exclamation-triangle me-2'></i>تحذير مهم!</h4>";
echo "<p><strong>كلمة المرور الجديدة للمدير:</strong> <code>SeaAdmin@2025!</code></p>";
echo "<p>يجب عليك تسجيل الخروج وإعادة تسجيل الدخول بكلمة المرور الجديدة.</p>";
echo "</div>";

echo "<div class='text-center mt-4'>";
echo "<a href='logout.php' class='btn btn-warning btn-lg me-2'>";
echo "<i class='fas fa-sign-out-alt me-2'></i>تسجيل الخروج";
echo "</a>";
echo "<a href='dashboard.php' class='btn btn-primary btn-lg'>";
echo "<i class='fas fa-home me-2'></i>العودة إلى لوحة التحكم";
echo "</a>";
echo "</div>";

echo "</div></body></html>";

// حذف هذا الملف نفسه لأمان إضافي
unlink(__FILE__);
?>
