<?php
/**
 * SeaSystem - إصلاح مشكلة عرض المنتجات
 * Fix Products Display Issue
 */

session_start();

// محاكاة تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'admin';
    $_SESSION['full_name'] = 'مدير النظام';
    $_SESSION['role'] = 'admin';
}

echo "<h1>🔧 إصلاح مشكلة عرض المنتجات</h1>";
echo "<hr>";

try {
    // الاتصال بقاعدة البيانات
    $host = 'localhost';
    $dbname = 'seasystem';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح<br>";
    
    // 1. التحقق من وجود الجداول المطلوبة
    echo "<h2>📊 فحص الجداول</h2>";
    
    $required_tables = ['products', 'product_categories', 'units_of_measure'];
    
    foreach ($required_tables as $table) {
        $check_sql = "SHOW TABLES LIKE '$table'";
        $check_stmt = $pdo->prepare($check_sql);
        $check_stmt->execute();
        
        if ($check_stmt->rowCount() > 0) {
            echo "✅ جدول $table موجود<br>";
        } else {
            echo "❌ جدول $table غير موجود - سيتم إنشاؤه<br>";
            
            // إنشاء الجداول المفقودة
            if ($table == 'product_categories') {
                $create_sql = "CREATE TABLE product_categories (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    category_name VARCHAR(100) NOT NULL,
                    description TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )";
                $pdo->exec($create_sql);
                echo "✅ تم إنشاء جدول product_categories<br>";
            }
            
            if ($table == 'units_of_measure') {
                $create_sql = "CREATE TABLE units_of_measure (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    unit_name VARCHAR(50) NOT NULL,
                    unit_symbol VARCHAR(10) NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )";
                $pdo->exec($create_sql);
                echo "✅ تم إنشاء جدول units_of_measure<br>";
            }
            
            if ($table == 'products') {
                $create_sql = "CREATE TABLE products (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    product_code VARCHAR(50) UNIQUE NOT NULL,
                    barcode VARCHAR(100),
                    product_name VARCHAR(255) NOT NULL,
                    description TEXT,
                    category_id INT,
                    base_unit_id INT,
                    cost_price DECIMAL(15,2) DEFAULT 0.00,
                    selling_price DECIMAL(15,2) DEFAULT 0.00,
                    current_stock DECIMAL(15,3) DEFAULT 0.000,
                    min_stock_level DECIMAL(15,3) DEFAULT 0.000,
                    max_stock_level DECIMAL(15,3) DEFAULT 0.000,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_by INT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )";
                $pdo->exec($create_sql);
                echo "✅ تم إنشاء جدول products<br>";
            }
        }
    }
    
    echo "<hr>";
    
    // 2. إضافة البيانات الأساسية
    echo "<h2>📂 إضافة البيانات الأساسية</h2>";
    
    // إضافة التصنيفات
    $categories_sql = "INSERT IGNORE INTO product_categories (id, category_name, description) VALUES 
                      (1, 'إلكترونيات', 'أجهزة إلكترونية ومعدات تقنية'),
                      (2, 'مكتبية', 'مستلزمات مكتبية وقرطاسية'),
                      (3, 'أثاث', 'أثاث مكتبي ومنزلي'),
                      (4, 'أخرى', 'منتجات متنوعة'),
                      (10, 'جرانيت', 'منتجات الجرانيت الطبيعي والصناعي'),
                      (11, 'رخام', 'منتجات الرخام الطبيعي والصناعي')";
    $pdo->exec($categories_sql);
    echo "✅ تم إضافة التصنيفات<br>";
    
    // إضافة وحدات القياس
    $units_sql = "INSERT IGNORE INTO units_of_measure (id, unit_name, unit_symbol) VALUES 
                  (1, 'قطعة', 'قطعة'),
                  (2, 'كيلوجرام', 'كجم'),
                  (3, 'متر', 'م'),
                  (4, 'لتر', 'لتر'),
                  (5, 'صندوق', 'صندوق'),
                  (6, 'متر مربع', 'م²')";
    $pdo->exec($units_sql);
    echo "✅ تم إضافة وحدات القياس<br>";
    
    echo "<hr>";
    
    // 3. التحقق من وجود المنتجات
    echo "<h2>📦 فحص المنتجات</h2>";
    
    $count_sql = "SELECT COUNT(*) as count FROM products WHERE is_active = 1";
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute();
    $products_count = $count_stmt->fetch()['count'];
    
    echo "📊 عدد المنتجات النشطة: $products_count<br>";
    
    if ($products_count == 0) {
        echo "<h3>➕ إضافة منتجات تجريبية</h3>";
        
        // إضافة منتجات تجريبية
        $sample_products_sql = "INSERT IGNORE INTO products (id, product_code, product_name, description, category_id, base_unit_id, cost_price, selling_price, current_stock, min_stock_level, max_stock_level, is_active, created_by) VALUES
                    (1, 'PROD001', 'لابتوب ديل Inspiron 15', 'لابتوب للاستخدام المكتبي والشخصي', 1, 1, 8000.00, 12000.00, 10.000, 2.000, 50.000, 1, 1),
                    (2, 'PROD002', 'طابعة HP LaserJet Pro', 'طابعة ليزر عالية الجودة', 1, 1, 2000.00, 3000.00, 15.000, 3.000, 30.000, 1, 1),
                    (3, 'PROD003', 'ورق A4 - 500 ورقة', 'ورق طباعة عالي الجودة', 2, 5, 25.00, 40.00, 100.000, 20.000, 500.000, 1, 1)";
        $pdo->exec($sample_products_sql);
        echo "✅ تم إضافة منتجات تجريبية<br>";
        
        // إضافة منتجات الجرانيت والرخام إذا لم تكن موجودة
        $granite_marble_sql = "INSERT IGNORE INTO products (product_code, barcode, product_name, description, category_id, base_unit_id, cost_price, selling_price, current_stock, min_stock_level, max_stock_level, is_active, created_by) VALUES
                    ('GRN001', '1234567890001', 'جرانيت نيو حلايب', 'جرانيت نيو حلايب طبيعي عالي الجودة', 10, 6, 150.00, 220.00, 50.000, 10.000, 200.000, 1, 1),
                    ('GRN002', '1234567890002', 'جرانيت جندولا', 'جرانيت جندولا بألوان متدرجة جميلة', 10, 6, 180.00, 260.00, 40.000, 8.000, 150.000, 1, 1),
                    ('GRN003', '1234567890003', 'جرانيت رمادي', 'جرانيت رمادي كلاسيكي محايد', 10, 6, 120.00, 180.00, 60.000, 15.000, 250.000, 1, 1),
                    ('GRN004', '1234567890004', 'جرانيت أحمر أصواني', 'جرانيت أحمر أصواني فاخر', 10, 6, 200.00, 300.00, 30.000, 5.000, 100.000, 1, 1),
                    ('GRN005', '1234567890005', 'جرانيت أسود أصواني', 'جرانيت أسود أصواني أنيق', 10, 6, 220.00, 320.00, 25.000, 5.000, 80.000, 1, 1),
                    ('MRB001', '1234567890011', 'رخام تريستا', 'رخام تريستا إيطالي فاخر', 11, 6, 300.00, 450.00, 35.000, 8.000, 120.000, 1, 1),
                    ('MRB002', '1234567890012', 'رخام صني مينا', 'رخام صني مينا بلون ذهبي دافئ', 11, 6, 250.00, 380.00, 40.000, 10.000, 150.000, 1, 1),
                    ('MRB003', '1234567890013', 'رخام سيلفيا', 'رخام سيلفيا بدرجات الرمادي الفضي', 11, 6, 280.00, 420.00, 30.000, 6.000, 100.000, 1, 1),
                    ('MRB004', '1234567890014', 'رخام جلالة', 'رخام جلالة مصري أصيل', 11, 6, 180.00, 270.00, 55.000, 12.000, 200.000, 1, 1),
                    ('MRB005', '1234567890015', 'رخام ميللي جراي', 'رخام ميللي جراي بعروق بيضاء رقيقة', 11, 6, 320.00, 480.00, 20.000, 4.000, 80.000, 1, 1),
                    ('MRB006', '1234567890016', 'رخام سماحة', 'رخام سماحة أبيض ناصع', 11, 6, 200.00, 300.00, 45.000, 10.000, 160.000, 1, 1)";
        $pdo->exec($granite_marble_sql);
        echo "✅ تم إضافة منتجات الجرانيت والرخام<br>";
    }
    
    echo "<hr>";
    
    // 4. اختبار استعلام getAllProducts
    echo "<h2>🧪 اختبار استعلام getAllProducts</h2>";
    
    $test_sql = "SELECT p.*,
                       pc.category_name,
                       u.unit_name,
                       (p.current_stock * p.cost_price) as stock_value
                FROM products p
                LEFT JOIN product_categories pc ON p.category_id = pc.id
                LEFT JOIN units_of_measure u ON p.base_unit_id = u.id
                WHERE p.is_active = 1
                ORDER BY p.product_name";
    
    $test_stmt = $pdo->prepare($test_sql);
    $test_stmt->execute();
    $test_products = $test_stmt->fetchAll();
    
    echo "📊 عدد المنتجات المسترجعة: " . count($test_products) . "<br>";
    
    if (!empty($test_products)) {
        echo "<h3>📋 المنتجات المسترجعة</h3>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 0.9rem;'>";
        echo "<tr style='background: #f8f9fa; font-weight: bold;'>";
        echo "<th style='padding: 8px;'>الرمز</th>";
        echo "<th style='padding: 8px;'>اسم المنتج</th>";
        echo "<th style='padding: 8px;'>التصنيف</th>";
        echo "<th style='padding: 8px;'>الوحدة</th>";
        echo "<th style='padding: 8px;'>المخزون</th>";
        echo "<th style='padding: 8px;'>سعر التكلفة</th>";
        echo "<th style='padding: 8px;'>سعر البيع</th>";
        echo "</tr>";
        
        foreach (array_slice($test_products, 0, 10) as $product) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($product['product_code']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($product['product_name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($product['category_name'] ?? 'غير محدد') . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($product['unit_name'] ?? 'غير محدد') . "</td>";
            echo "<td style='padding: 8px;'>" . number_format($product['current_stock'], 2) . "</td>";
            echo "<td style='padding: 8px;'>" . number_format($product['cost_price'], 2) . "</td>";
            echo "<td style='padding: 8px;'>" . number_format($product['selling_price'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        if (count($test_products) > 10) {
            echo "<p><em>عرض أول 10 منتجات من أصل " . count($test_products) . " منتج</em></p>";
        }
    }
    
    echo "<hr>";
    
    // 5. إحصائيات نهائية
    echo "<h2>📈 الإحصائيات النهائية</h2>";
    
    $final_count_sql = "SELECT COUNT(*) as count FROM products WHERE is_active = 1";
    $final_count_stmt = $pdo->prepare($final_count_sql);
    $final_count_stmt->execute();
    $final_count = $final_count_stmt->fetch()['count'];
    
    $categories_count_sql = "SELECT COUNT(*) as count FROM product_categories WHERE is_active = 1";
    $categories_count_stmt = $pdo->prepare($categories_count_sql);
    $categories_count_stmt->execute();
    $categories_count = $categories_count_stmt->fetch()['count'];
    
    $units_count_sql = "SELECT COUNT(*) as count FROM units_of_measure WHERE is_active = 1";
    $units_count_stmt = $pdo->prepare($units_count_sql);
    $units_count_stmt->execute();
    $units_count = $units_count_stmt->fetch()['count'];
    
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>";
    echo "<h4>📊 الإحصائيات:</h4>";
    echo "<ul>";
    echo "<li><strong>المنتجات النشطة:</strong> $final_count</li>";
    echo "<li><strong>التصنيفات:</strong> $categories_count</li>";
    echo "<li><strong>وحدات القياس:</strong> $units_count</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>🎉 تم إصلاح مشكلة عرض المنتجات!</h4>";
    echo "<p style='color: #155724; margin: 0;'>الآن يجب أن تظهر المنتجات بشكل صحيح في صفحة المخزون.</p>";
    echo "</div>";
    
    echo "<br><h3>🔗 اختبار النتائج</h3>";
    echo "<a href='inventory.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة المخزون</a>";
    echo "<a href='test_products_display.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار العرض</a>";
    echo "<a href='dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة التحكم</a>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h4 style='color: #721c24; margin: 0 0 10px 0;'>❌ خطأ في الإصلاح</h4>";
    echo "<p style='color: #721c24; margin: 0;'>حدث خطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 30px auto;
    padding: 20px;
    background: #f8f9fa;
    direction: rtl;
}

h1 { color: #343a40; }
h2 { color: #495057; }
h3 { color: #6c757d; }
h4 { color: #6c757d; }

table { margin: 10px 0; }
th { background: #e9ecef !important; }
tr:nth-child(even) { background: #f8f9fa; }
</style>
