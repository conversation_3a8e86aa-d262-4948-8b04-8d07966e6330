<?php
/**
 * SeaSystem - إلغاء حجز الرقم
 * Cancel Reserved Number
 */

// تعريف الثابت للوصول
define('SEASYSTEM_ACCESS', true);

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/NumberGenerator.php';
require_once 'includes/auth.php';

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'غير مصرح']);
    exit();
}

// التحقق من وجود نوع الكيان
if (!isset($_POST['type'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'نوع الكيان مطلوب']);
    exit();
}

$type = $_POST['type'];
$numberGenerator = new NumberGenerator();

try {
    $cancelled = $numberGenerator->cancelReservedNumber($type);
    
    if ($cancelled) {
        echo json_encode([
            'success' => true,
            'message' => 'تم إلغاء حجز الرقم بنجاح',
            'type' => $type
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'لا يوجد رقم محجوز لإلغائه',
            'type' => $type
        ]);
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في النظام: ' . $e->getMessage()
    ]);
}
?>
