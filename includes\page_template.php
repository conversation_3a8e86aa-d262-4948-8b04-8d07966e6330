<?php
/**
 * SeaSystem - قالب الصفحة الموحد
 * Unified Page Template
 */

function startPage($title, $current_page = '', $additional_css = [], $additional_js = []) {
    global $current_user;

    if (!isset($current_user)) {
        require_once __DIR__ . '/auth.php';
        requireLogin();
        $current_user = getCurrentUser();
    }

    require_once __DIR__ . '/sidebar.php';

    echo '<!DOCTYPE html>';
    echo '<html lang="ar" dir="rtl">';
    echo '<head>';
    echo '<meta charset="UTF-8">';
    echo '<meta name="viewport" content="width=device-width, initial-scale=1.0">';
    echo '<title>' . $title . ' - ' . SITE_NAME . '</title>';

    // CSS الأساسي
    echo '<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">';
    echo '<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">';
    echo '<link href="assets/css/style.css" rel="stylesheet">';
    echo '<link href="assets/css/sidebar-style.css" rel="stylesheet">';

    // CSS إضافي
    foreach ($additional_css as $css) {
        echo '<link href="' . $css . '" rel="stylesheet">';
    }

    echo '</head>';
    echo '<body>';

    // الشريط العلوي
    renderTopNavbar($current_user);

    echo '<div class="container-fluid">';
    echo '<div class="row">';

    // الشريط الجانبي
    renderSidebar($current_page);

    echo '<div class="col-md-9 col-lg-10 p-4">';
}

function endPage($additional_js = []) {
    echo '</div>'; // إغلاق المحتوى الرئيسي
    echo '</div>'; // إغلاق الصف
    echo '</div>'; // إغلاق الحاوية

    // JavaScript الأساسي
    echo '<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>';

    // JavaScript إضافي
    foreach ($additional_js as $js) {
        echo '<script src="' . $js . '"></script>';
    }

    echo '</body>';
    echo '</html>';
}

function renderQuickActions() {
    echo '<div class="content-card">';
    echo '<div class="card-header">';
    echo '<h5 class="mb-0">';
    echo '<i class="fas fa-bolt me-2"></i>الإجراءات السريعة';
    echo '</h5>';
    echo '</div>';
    echo '<div class="card-body pt-4">';
    echo '<div class="d-grid gap-3">';

    $quick_actions = [
        [
            'url' => 'invoice_create.php',
            'class' => 'btn-primary',
            'icon' => 'fas fa-plus',
            'text' => 'إنشاء فاتورة جديدة'
        ],
        [
            'url' => 'customer_create.php',
            'class' => 'btn-success',
            'icon' => 'fas fa-user-plus',
            'text' => 'إضافة عميل جديد'
        ],
        [
            'url' => 'supplier_create.php',
            'class' => 'btn-warning',
            'icon' => 'fas fa-truck',
            'text' => 'إضافة مورد'
        ],
        [
            'url' => 'inventory.php#add-product-tab',
            'class' => 'btn-secondary',
            'icon' => 'fas fa-box',
            'text' => 'إضافة منتج',
            'onclick' => 'openAddProductTab()'
        ],
        [
            'url' => 'journal_entry_create.php',
            'class' => 'btn-dark',
            'icon' => 'fas fa-book',
            'text' => 'إنشاء قيد محاسبي'
        ],
        [
            'url' => 'payment_create.php',
            'class' => 'btn-info',
            'icon' => 'fas fa-credit-card',
            'text' => 'تسجيل دفعة'
        ],
        [
            'url' => 'reports.php',
            'class' => 'btn-outline-primary',
            'icon' => 'fas fa-chart-bar',
            'text' => 'عرض التقارير'
        ]
    ];

    foreach ($quick_actions as $action) {
        echo '<a href="' . $action['url'] . '" class="btn ' . $action['class'] . ' py-3 quick-action-btn">';
        echo '<i class="' . $action['icon'] . ' me-2"></i>' . $action['text'];
        echo '</a>';
    }

    echo '</div>';
    echo '</div>';
    echo '</div>';
}

function renderStatsCards($stats) {
    echo '<div class="row mb-4">';

    $colors = ['primary', 'success', 'warning', 'info'];
    $color_index = 0;

    foreach ($stats as $stat) {
        $color = $colors[$color_index % count($colors)];

        echo '<div class="col-lg-3 col-md-6 mb-3">';
        echo '<div class="stat-card">';
        echo '<div class="d-flex align-items-center">';
        echo '<div class="stat-icon ' . $color . ' me-3">';
        echo '<i class="' . $stat['icon'] . '"></i>';
        echo '</div>';
        echo '<div>';
        echo '<h3 class="mb-1">' . $stat['value'] . '</h3>';
        echo '<p class="text-muted mb-0">' . $stat['title'] . '</p>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';

        $color_index++;
    }

    echo '</div>';
}

function renderDataTable($title, $headers, $data, $actions = []) {
    echo '<div class="table-card">';
    echo '<div class="card-header">';
    echo '<h5 class="mb-0">';
    echo '<i class="fas fa-table me-2"></i>' . $title;
    echo '</h5>';
    echo '</div>';
    echo '<div class="card-body p-0">';
    echo '<div class="table-responsive">';
    echo '<table class="table table-hover mb-0">';

    // رأس الجدول
    echo '<thead>';
    echo '<tr>';
    foreach ($headers as $header) {
        echo '<th>' . $header . '</th>';
    }
    if (!empty($actions)) {
        echo '<th>الإجراءات</th>';
    }
    echo '</tr>';
    echo '</thead>';

    // بيانات الجدول
    echo '<tbody>';
    if (empty($data)) {
        echo '<tr>';
        echo '<td colspan="' . (count($headers) + (empty($actions) ? 0 : 1)) . '" class="text-center py-4">';
        echo '<i class="fas fa-inbox fa-2x text-muted mb-2"></i>';
        echo '<p class="text-muted mb-0">لا توجد بيانات للعرض</p>';
        echo '</td>';
        echo '</tr>';
    } else {
        foreach ($data as $row) {
            echo '<tr>';
            foreach ($row as $cell) {
                echo '<td>' . $cell . '</td>';
            }

            if (!empty($actions)) {
                echo '<td>';
                echo '<div class="action-buttons">';
                foreach ($actions as $action) {
                    echo '<a href="' . $action['url'] . '" class="btn btn-sm ' . $action['class'] . '" title="' . $action['title'] . '">';
                    echo '<i class="' . $action['icon'] . '"></i>';
                    echo '</a>';
                }
                echo '</div>';
                echo '</td>';
            }
            echo '</tr>';
        }
    }
    echo '</tbody>';

    echo '</table>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
}
?>
