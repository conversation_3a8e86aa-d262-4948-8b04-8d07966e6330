/**
 * SeaSystem - نظام الهيدر الثابت المحسن
 * Enhanced Fixed Header System
 */

/* إعادة تعيين ألوان Bootstrap الافتراضية */
.navbar-dark {
    background-color: transparent !important;
    background-image: none !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* إجبار الألوان على جميع عناصر الهيدر */
nav.navbar,
nav.navbar-dark,
nav.navbar-expand-lg,
.navbar-fixed,
.navbar-default {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    background-color: #667eea !important;
    border: none !important;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.6) !important;
}

/* إعدادات الهيدر الثابت - ثابت دائماً في الأعلى */
.navbar-fixed {
    position: fixed !important;
    top: 0 !important;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 1030;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* الهيدر الثابت - نفس المظهر دائماً */
.navbar-default,
.navbar-scrolled-up,
.navbar-scrolled-down,
.navbar-enhanced {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.6) !important;
    padding: 1rem 0 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
    transform: translateY(0) !important;
    position: fixed !important;
    top: 0 !important;
    width: 100% !important;
    z-index: 1030 !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* إزالة خاصية الإخفاء */
.navbar-hidden {
    transform: translateY(0) !important;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* تأثيرات إضافية للهيدر */
.navbar-enhanced {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.6) !important;
}

/* ضمان ظهور الألوان */
.navbar,
.navbar-expand-lg,
.navbar-dark {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.6) !important;
}

/* ألوان النصوص */
.navbar-dark .navbar-brand,
.navbar-dark .navbar-nav .nav-link,
.navbar-dark .navbar-text {
    color: rgba(255, 255, 255, 0.95) !important;
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link:focus {
    color: rgba(255, 255, 255, 1) !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-radius: 8px;
    transition: all 0.3s ease;
}

/* ضمان ظهور جميع عناصر الهيدر */
.navbar * {
    color: white !important;
}

.navbar .navbar-brand {
    color: white !important;
    font-weight: bold !important;
}

.navbar .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
}

.navbar .nav-link:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-radius: 6px;
}

/* أزرار الهيدر */
.navbar .btn {
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: white !important;
}

.navbar .btn:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
}

/* تحسين العلامة التجارية */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    transition: all 0.3s ease;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.navbar-brand:hover {
    transform: scale(1.05);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.navbar-brand i {
    transition: all 0.3s ease;
}

.navbar-brand:hover i {
    transform: rotate(360deg);
    color: #ffd700;
}

/* تحسين روابط التنقل */
.navbar-nav .nav-link {
    position: relative;
    transition: all 0.3s ease;
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    border-radius: 8px;
    margin: 0 0.25rem;
}

.navbar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: #ffd700;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::before {
    width: 80%;
}

/* تحسين القائمة المنسدلة */
.dropdown-menu {
    border: none;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    animation: dropdownFadeIn 0.3s ease;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 0.25rem 0.5rem;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateX(5px);
}

.dropdown-item i {
    width: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.dropdown-item:hover i {
    transform: scale(1.2);
}

/* تحسين زر التبديل للموبايل */
.navbar-toggler {
    border: none;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.navbar-toggler:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.3);
}

/* تأثير النبضة للإشعارات */
.notification-badge {
    position: relative;
}

.notification-badge::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #ff4757;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7);
    }
    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(255, 71, 87, 0);
    }
    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(255, 71, 87, 0);
    }
}

/* تعديل المحتوى ليتناسب مع الهيدر الثابت */
body {
    padding-top: 80px;
    transition: padding-top 0.3s ease;
}

body.navbar-compact {
    padding-top: 70px;
}

body.navbar-minimal {
    padding-top: 60px;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 991.98px) {
    .navbar-nav {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 1rem;
        margin-top: 1rem;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    }

    .navbar-nav .nav-link {
        color: #495057 !important;
        margin: 0.25rem 0;
    }

    .navbar-nav .nav-link:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white !important;
    }

    .dropdown-menu {
        background: transparent;
        box-shadow: none;
        border: none;
        padding: 0;
    }

    .dropdown-item {
        background: rgba(255, 255, 255, 0.1);
        margin: 0.25rem 0;
        color: #495057;
    }
}

/* تأثيرات خاصة للحالات المختلفة */
.navbar-loading {
    background: linear-gradient(90deg,
        rgba(102, 126, 234, 0.8) 0%,
        rgba(118, 75, 162, 0.8) 25%,
        rgba(102, 126, 234, 0.8) 50%,
        rgba(118, 75, 162, 0.8) 75%,
        rgba(102, 126, 234, 0.8) 100%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* تحسين الأداء */
.navbar-fixed * {
    will-change: transform;
}

/* تأثير الزجاج المصقول */
.navbar-glass {
    background: rgba(102, 126, 234, 0.1) !important;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* وضع الليل */
@media (prefers-color-scheme: dark) {
    .navbar-fixed {
        background: linear-gradient(135deg,
            rgba(52, 58, 64, 0.95) 0%,
            rgba(33, 37, 41, 0.95) 100%) !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .dropdown-menu {
        background: rgba(52, 58, 64, 0.95);
        color: #fff;
    }

    .dropdown-item {
        color: #fff;
    }

    .dropdown-item:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
}

/* تحسينات إضافية للأداء */
.navbar-optimized {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* تأثير التمرير السلس */
html {
    scroll-behavior: smooth;
}

/* إخفاء شريط التمرير في بعض المتصفحات */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}
