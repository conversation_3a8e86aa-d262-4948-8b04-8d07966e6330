/**
 * SeaSystem - ألوان الهيدر المحسنة
 * Enhanced Header Colors
 */

/* إجبار ألوان الهيدر بأولوية عالية */
nav.navbar,
.navbar,
.navbar-dark,
.navbar-expand-lg,
.navbar-fixed,
.navbar-default,
.navbar-enhanced {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    background-color: #667eea !important;
    background-image: none !important;
    border: none !important;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.6) !important;
    position: fixed !important;
    top: 0 !important;
    width: 100% !important;
    z-index: 1030 !important;
}

/* ألوان النصوص والروابط */
.navbar .navbar-brand,
.navbar .nav-link,
.navbar .navbar-text,
.navbar a,
.navbar span,
.navbar i {
    color: #ffffff !important;
    opacity: 1 !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
    font-weight: 500 !important;
}

/* تأثيرات التفاعل */
.navbar .nav-link:hover,
.navbar .nav-link:focus,
.navbar .navbar-brand:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.15) !important;
    border-radius: 8px;
    transition: all 0.3s ease;
}

/* أزرار الهيدر */
.navbar .btn {
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    background-color: transparent !important;
}

.navbar .btn:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: white !important;
}

/* القوائم المنسدلة */
.navbar .dropdown-menu {
    background-color: rgba(102, 126, 234, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(10px);
}

.navbar .dropdown-item {
    color: white !important;
}

.navbar .dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

/* العلامة التجارية */
.navbar-brand {
    font-weight: 700 !important;
    font-size: 1.5rem !important;
}

.navbar-brand .version-badge {
    background-color: rgba(255, 255, 255, 0.9) !important;
    color: #667eea !important;
    font-size: 0.7rem !important;
}

/* تحسينات إضافية */
.navbar-toggler {
    border-color: rgba(255, 255, 255, 0.3) !important;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}

/* إزالة أي خلفيات متضاربة */
.navbar::before,
.navbar::after {
    display: none !important;
}

/* ضمان الشفافية الصحيحة */
.navbar {
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
}

/* تأثيرات الحركة */
.navbar {
    transition: all 0.3s ease !important;
}

.navbar .nav-link,
.navbar .navbar-brand {
    transition: all 0.3s ease !important;
}
