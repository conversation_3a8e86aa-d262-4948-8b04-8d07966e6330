<?php
/**
 * البحث الديناميكي في المخزون
 * Dynamic Inventory Search
 */

// تعريف الثابت للوصول
define('SEASYSTEM_ACCESS', true);

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/Inventory.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    exit('غير مصرح');
}

// التحقق من وجود مصطلح البحث
$search_term = $_GET['search'] ?? '';

// إنشاء كائن المخزون
$inventory = new Inventory();

// البحث في المنتجات
$filters = [];
if (!empty($search_term)) {
    $filters['search'] = $search_term;
}

$products = $inventory->getAllProducts($filters);

// عرض النتائج
if (empty($products)) {
    echo '<tr>
            <td colspan="10" class="text-center py-4">
                <i class="fas fa-search fa-2x text-muted mb-2"></i>
                <p class="text-muted">لا توجد منتجات تطابق البحث</p>
            </td>
          </tr>';
} else {
    foreach ($products as $product) {
        // تحديد حالة المخزون
        $stock_status = '';
        $stock_class = '';
        $stock_icon = '';

        if ($product['current_stock'] <= 0) {
            $stock_status = 'نفد المخزون';
            $stock_class = 'text-danger';
            $stock_icon = 'fas fa-times-circle';
        } elseif ($product['current_stock'] <= $product['min_stock_level']) {
            $stock_status = 'مخزون منخفض';
            $stock_class = 'text-warning';
            $stock_icon = 'fas fa-exclamation-triangle';
        } else {
            $stock_status = 'متوفر';
            $stock_class = 'text-success';
            $stock_icon = 'fas fa-check-circle';
        }

        // حساب قيمة المخزون
        $stock_value = $product['current_stock'] * $product['unit_price'];

        echo '<tr>
                <td>' . htmlspecialchars($product['product_code']) . '</td>
                <td>' . htmlspecialchars($product['product_name']) . '</td>
                <td>' . htmlspecialchars($product['category_name'] ?? 'غير محدد') . '</td>
                <td>' . htmlspecialchars($product['unit_name'] ?? 'قطعة') . '</td>
                <td class="text-end">' . number_format($product['current_stock']) . '</td>
                <td>
                    <span class="' . $stock_class . '">
                        <i class="' . $stock_icon . ' me-1"></i>' . $stock_status . '
                    </span>
                </td>
                <td class="text-end">' . number_format($product['cost_price'] ?? 0, 2) . ' ' . CURRENCY_SYMBOL . '</td>
                <td class="text-end">' . number_format($product['unit_price'], 2) . ' ' . CURRENCY_SYMBOL . '</td>
                <td class="text-end">' . number_format($stock_value, 2) . ' ' . CURRENCY_SYMBOL . '</td>
                <td>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-info btn-sm"
                                onclick="viewProductMovements(' . $product['id'] . ')" title="عرض الحركات">
                            <i class="fas fa-history"></i>
                            <small class="d-block">الحركات</small>
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm"
                                onclick="adjustStock(' . $product['id'] . ')" title="تسوية المخزون">
                            <i class="fas fa-edit"></i>
                            <small class="d-block">تسوية</small>
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm"
                                onclick="addStock(' . $product['id'] . ')" title="إضافة مخزون">
                            <i class="fas fa-plus"></i>
                            <small class="d-block">إضافة</small>
                        </button>
                    </div>
                </td>
              </tr>';
    }
}
?>
