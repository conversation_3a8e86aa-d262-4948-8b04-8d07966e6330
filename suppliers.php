<?php
/**
 * SeaSystem - صفحة إدارة الموردين
 * Suppliers Management Page
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/includes/sidebar.php';
require_once __DIR__ . '/classes/NumberGenerator.php';

// التأكد من تسجيل الدخول
requireLogin();

// إنشاء فئة الموردين (مشابهة للعملاء)
class Supplier {
    private $db;
    private $table_name = "suppliers";

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }

    public function create($data) {
        try {
            if ($this->isSupplierCodeExists($data['supplier_code'])) {
                return ['success' => false, 'message' => 'رمز المورد موجود مسبقاً'];
            }

            $sql = "INSERT INTO " . $this->table_name . "
                    (supplier_code, name, email, phone, address, tax_number, opening_balance, current_balance)
                    VALUES (:supplier_code, :name, :email, :phone, :address, :tax_number, :opening_balance, :opening_balance)";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':supplier_code', $data['supplier_code']);
            $stmt->bindParam(':name', $data['name']);
            $stmt->bindParam(':email', $data['email']);
            $stmt->bindParam(':phone', $data['phone']);
            $stmt->bindParam(':address', $data['address']);
            $stmt->bindParam(':tax_number', $data['tax_number']);
            $stmt->bindParam(':opening_balance', $data['opening_balance']);

            if ($stmt->execute()) {
                return ['success' => true, 'message' => 'تم إضافة المورد بنجاح'];
            } else {
                return ['success' => false, 'message' => 'فشل في إضافة المورد'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()];
        }
    }

    public function getAll() {
        try {
            $sql = "SELECT * FROM " . $this->table_name . " ORDER BY name";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    public function search($keyword) {
        try {
            $sql = "SELECT * FROM " . $this->table_name . "
                    WHERE (supplier_code LIKE :keyword OR name LIKE :keyword OR email LIKE :keyword)
                    ORDER BY name";
            $stmt = $this->db->prepare($sql);
            $keyword = "%$keyword%";
            $stmt->bindParam(':keyword', $keyword);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    public function update($id, $data) {
        try {
            if ($this->isSupplierCodeExists($data['supplier_code'], $id)) {
                return ['success' => false, 'message' => 'رمز المورد موجود مسبقاً'];
            }

            $sql = "UPDATE " . $this->table_name . "
                    SET supplier_code = :supplier_code, name = :name, email = :email,
                        phone = :phone, address = :address, tax_number = :tax_number,
                        opening_balance = :opening_balance, updated_at = NOW()
                    WHERE id = :id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':supplier_code', $data['supplier_code']);
            $stmt->bindParam(':name', $data['name']);
            $stmt->bindParam(':email', $data['email']);
            $stmt->bindParam(':phone', $data['phone']);
            $stmt->bindParam(':address', $data['address']);
            $stmt->bindParam(':tax_number', $data['tax_number']);
            $stmt->bindParam(':opening_balance', $data['opening_balance']);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                return ['success' => true, 'message' => 'تم تحديث بيانات المورد بنجاح'];
            } else {
                return ['success' => false, 'message' => 'فشل في تحديث بيانات المورد'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()];
        }
    }

    public function delete($id) {
        try {
            $sql = "DELETE FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                return ['success' => true, 'message' => 'تم حذف المورد بنجاح'];
            } else {
                return ['success' => false, 'message' => 'فشل في حذف المورد'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()];
        }
    }

    private function isSupplierCodeExists($supplier_code, $exclude_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE supplier_code = :supplier_code";
            if ($exclude_id) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':supplier_code', $supplier_code);
            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }

            $stmt->execute();
            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    // دوال الإحصائيات
    public function getTotalSuppliers() {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name;
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (Exception $e) {
            return 0;
        }
    }

    public function getActiveSuppliers() {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE is_active = 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (Exception $e) {
            return 0;
        }
    }

    public function getTotalPurchases() {
        try {
            // افتراض وجود جدول المشتريات
            $sql = "SELECT COALESCE(SUM(total_amount), 0) FROM purchases WHERE supplier_id IN (SELECT id FROM " . $this->table_name . ")";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (Exception $e) {
            return 0;
        }
    }

    public function getTopSuppliers($limit = 5) {
        try {
            $sql = "SELECT s.name, COALESCE(SUM(p.total_amount), 0) as total_purchases
                    FROM " . $this->table_name . " s
                    LEFT JOIN purchases p ON s.id = p.supplier_id
                    GROUP BY s.id, s.name
                    ORDER BY total_purchases DESC
                    LIMIT :limit";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return [];
        }
    }

    // دوال الإحصائيات المالية
    public function getTotalPurchasesAmount() {
        try {
            $sql = "SELECT COALESCE(SUM(total_amount), 0) FROM purchases WHERE supplier_id IN (SELECT id FROM " . $this->table_name . ")";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (Exception $e) {
            return 0;
        }
    }

    public function getTotalOutstandingAmount() {
        try {
            $sql = "SELECT COALESCE(SUM(outstanding_amount), 0) FROM purchases WHERE supplier_id IN (SELECT id FROM " . $this->table_name . ") AND status != 'paid'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (Exception $e) {
            return 0;
        }
    }

    public function getTotalPaidAmount() {
        try {
            $sql = "SELECT COALESCE(SUM(paid_amount), 0) FROM purchases WHERE supplier_id IN (SELECT id FROM " . $this->table_name . ")";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (Exception $e) {
            return 0;
        }
    }

    public function getAverageOrderValue() {
        try {
            $sql = "SELECT COALESCE(AVG(total_amount), 0) FROM purchases WHERE supplier_id IN (SELECT id FROM " . $this->table_name . ")";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (Exception $e) {
            return 0;
        }
    }
}

$supplier = new Supplier();
$current_user = getCurrentUser();

// معالجة العمليات
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $data = [
                    'supplier_code' => $_POST['supplier_code'],
                    'name' => $_POST['name'],
                    'email' => $_POST['email'],
                    'phone' => $_POST['phone'],
                    'address' => $_POST['address'],
                    'tax_number' => $_POST['tax_number'],
                    'opening_balance' => !empty($_POST['opening_balance']) ? $_POST['opening_balance'] : 0.00
                ];

                $result = $supplier->create($data);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;

            case 'edit':
                $data = [
                    'supplier_code' => $_POST['supplier_code'],
                    'name' => $_POST['name'],
                    'email' => $_POST['email'],
                    'phone' => $_POST['phone'],
                    'address' => $_POST['address'],
                    'tax_number' => $_POST['tax_number'],
                    'opening_balance' => !empty($_POST['opening_balance']) ? $_POST['opening_balance'] : 0.00
                ];

                $result = $supplier->update($_POST['supplier_id'], $data);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;

            case 'delete':
                $result = $supplier->delete($_POST['supplier_id']);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;
        }
    }
}

// الحصول على قائمة الموردين
$suppliers = $supplier->getAll();

// البحث
$search_term = $_GET['search'] ?? '';
if (!empty($search_term)) {
    $suppliers = $supplier->search($search_term);
}

// الحصول على الإحصائيات المالية
$total_purchases_amount = $supplier->getTotalPurchasesAmount();
$total_outstanding = $supplier->getTotalOutstandingAmount();
$total_paid = $supplier->getTotalPaidAmount();
$average_order_value = $supplier->getAverageOrderValue();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموردين - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <link href="assets/css/sidebar-only.css" rel="stylesheet">
    <style>
        .stat-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            min-height: 120px;
            display: flex;
            align-items: center;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            flex-shrink: 0;
        }

        .stat-icon.primary { background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); }
        .stat-icon.success { background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); }
        .stat-icon.warning { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); }
        .stat-icon.info { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); }

        .stat-card h3 {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .stat-card .stat-content {
            flex: 1;
        }

        .stat-card p {
            margin-bottom: 0;
            font-size: 0.9rem;
            line-height: 1.2;
        }

        /* تخصيص رأس بطاقة الجدول باللون الجميل */
        .table-card .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            border-bottom: 1px solid #dee2e6;
            border-radius: 10px 10px 0 0;
            padding: 1rem 1.5rem;
        }

        /* تنسيقات الهيدر الثابت */
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }

        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }

        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
        </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي الموحد -->
            <?php renderSidebar('suppliers.php'); ?>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="page-title">
                                <i class="fas fa-truck me-2"></i>إدارة الموردين
                            </h1>
                            <p class="page-subtitle">إضافة وإدارة بيانات الموردين</p>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSupplierModal">
                                <i class="fas fa-plus me-2"></i>إضافة مورد جديد
                            </button>
                        </div>
                    </div>
                </div>

                <!-- بطاقات الإحصائيات المالية -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon primary me-3">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="stat-content">
                                <h3><?php echo number_format($total_purchases_amount, 2); ?></h3>
                                <p class="text-muted">إجمالي قيمة المشتريات</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon success me-3">
                                <i class="fas fa-check-double"></i>
                            </div>
                            <div class="stat-content">
                                <h3><?php echo number_format($total_paid, 2); ?></h3>
                                <p class="text-muted">إجمالي المدفوعات</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon warning me-3">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content">
                                <h3><?php echo number_format($total_outstanding, 2); ?></h3>
                                <p class="text-muted">إجمالي المبالغ المستحقة</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon info me-3">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-content">
                                <h3><?php echo number_format($average_order_value, 2); ?></h3>
                                <p class="text-muted">متوسط قيمة الطلب</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسائل -->
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- جدول الموردين -->
                <div class="table-card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-truck me-2"></i>قائمة الموردين (<?php echo count($suppliers); ?>)
                                </h5>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex">
                                    <input type="text" class="form-control me-2" id="supplierSearch"
                                           placeholder="البحث في الموردين..."
                                           value="<?php echo htmlspecialchars($search_term); ?>">
                                    <button type="button" class="btn btn-light" id="clearSearchSupplier">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>رمز المورد</th>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>الرقم الضريبي</th>
                                        <th>الرصيد الحالي</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="suppliersTableBody">
                                    <?php if (empty($suppliers)): ?>
                                        <tr>
                                            <td colspan="8" class="text-center py-4">
                                                <i class="fas fa-truck fa-2x text-muted mb-2"></i>
                                                <p class="text-muted mb-0">لا توجد موردين حتى الآن</p>
                                                <button type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#addSupplierModal">
                                                    إضافة أول مورد
                                                </button>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($suppliers as $supp): ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($supp['supplier_code']); ?></strong></td>
                                                <td><?php echo htmlspecialchars($supp['name']); ?></td>
                                                <td><?php echo htmlspecialchars($supp['email']); ?></td>
                                                <td><?php echo htmlspecialchars($supp['phone']); ?></td>
                                                <td><?php echo htmlspecialchars($supp['tax_number']); ?></td>
                                                <td>
                                                    <span class="<?php echo $supp['current_balance'] > 0 ? 'text-danger' : 'text-success'; ?>">
                                                        <?php echo number_format($supp['current_balance'], 2) . ' ' . CURRENCY_SYMBOL; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($supp['is_active']): ?>
                                                        <span class="badge bg-success">نشط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">غير نشط</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="d-flex gap-2">
                                                        <!-- زر العرض أولاً -->
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-info btn-sm"
                                                                    onclick="viewSupplier(<?php echo $supp['id']; ?>)">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">عرض</small>
                                                        </div>
                                                        <!-- زر التعديل ثانياً -->
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                                    onclick="editSupplier(<?php echo htmlspecialchars(json_encode($supp)); ?>)">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">تعديل</small>
                                                        </div>
                                                        <!-- زر الحذف أخيراً -->
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-danger btn-sm btn-delete"
                                                                    onclick="deleteSupplier(<?php echo $supp['id']; ?>)">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">حذف</small>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة مورد -->
    <div class="modal fade" id="addSupplierModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-truck me-2"></i>إضافة مورد جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="supplier_code" class="form-label">رمز المورد *</label>
                                <input type="text" class="form-control" id="supplier_code" name="supplier_code" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم المورد *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="tax_number" class="form-label">الرقم الضريبي</label>
                                <input type="text" class="form-control" id="tax_number" name="tax_number">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="opening_balance" class="form-label">الرصيد الافتتاحي</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="opening_balance" name="opening_balance" value="0.00"
                                           pattern="[0-9]+([.][0-9]+)?" inputmode="decimal"
                                           placeholder="ادخل الرصيد مثل: 2500.75">
                                    <span class="input-group-text">ريال</span>
                                </div>
                                <small class="form-text text-muted">اترك فارغاً إذا لم يكن هناك رصيد افتتاحي</small>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ المورد
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل مورد -->
    <div class="modal fade" id="editSupplierModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>تعديل بيانات المورد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="supplier_id" id="edit_supplier_id">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_supplier_code" class="form-label">رمز المورد *</label>
                                <input type="text" class="form-control" id="edit_supplier_code" name="supplier_code" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="edit_name" class="form-label">اسم المورد *</label>
                                <input type="text" class="form-control" id="edit_name" name="name" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="edit_email" name="email">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="edit_phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="edit_phone" name="phone">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_address" class="form-label">العنوان</label>
                            <textarea class="form-control" id="edit_address" name="address" rows="3"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_tax_number" class="form-label">الرقم الضريبي</label>
                                <input type="text" class="form-control" id="edit_tax_number" name="tax_number">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="edit_opening_balance" class="form-label">الرصيد الافتتاحي</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="edit_opening_balance" name="opening_balance"
                                           pattern="[0-9]+([.][0-9]+)?" inputmode="decimal"
                                           placeholder="ادخل الرصيد مثل: 2500.75">
                                    <span class="input-group-text">ريال</span>
                                </div>
                                <small class="form-text text-muted">لا يمكن تعديل الرصيد الافتتاحي بعد وجود حركات</small>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
    <script src="assets/js/number-input-enhancement.js"></script>
    <script src="assets/js/duplicate-check.js"></script>
    <script>
        // تعديل مورد
        function editSupplier(supplier) {
            document.getElementById('edit_supplier_id').value = supplier.id;
            document.getElementById('edit_supplier_code').value = supplier.supplier_code;
            document.getElementById('edit_name').value = supplier.name;
            document.getElementById('edit_email').value = supplier.email || '';
            document.getElementById('edit_phone').value = supplier.phone || '';
            document.getElementById('edit_address').value = supplier.address || '';
            document.getElementById('edit_tax_number').value = supplier.tax_number || '';
            document.getElementById('edit_opening_balance').value = supplier.opening_balance || '0.00';

            new bootstrap.Modal(document.getElementById('editSupplierModal')).show();
        }

        // عرض تفاصيل مورد
        function viewSupplier(supplierId) {
            window.open(`supplier_view.php?id=${supplierId}`, '_blank');
        }

        // حذف مورد
        function deleteSupplier(supplierId) {
            if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="supplier_id" value="${supplierId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // توليد رمز مورد تلقائي
        function generateNewSupplierCode() {
            fetch('get_next_number.php?type=supplier')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('supplier_code').value = data.number;
                    } else {
                        console.error('خطأ في توليد رمز المورد:', data.error);
                        document.getElementById('supplier_code').value = 'SUP001';
                    }
                })
                .catch(error => {
                    console.error('خطأ في توليد رمز المورد:', error);
                    document.getElementById('supplier_code').value = 'SUP001';
                });
        }

        // توليد رقم عند فتح النافذة
        document.addEventListener('DOMContentLoaded', function() {
            // توليد رقم عند فتح نافذة إضافة مورد
            const addSupplierModal = document.getElementById('addSupplierModal');
            if (addSupplierModal) {
                addSupplierModal.addEventListener('show.bs.modal', function() {
                    generateNewSupplierCode();
                });
            }
        });

        // البحث الديناميكي
        let searchTimeoutSupplier;
        const searchInputSupplier = document.getElementById('supplierSearch');
        const clearButtonSupplier = document.getElementById('clearSearchSupplier');
        const tableBodySupplier = document.getElementById('suppliersTableBody');

        if (searchInputSupplier) {
            searchInputSupplier.addEventListener('input', function() {
                clearTimeout(searchTimeoutSupplier);
                const searchTerm = this.value.trim();

                // تأخير البحث لمدة 300 ملي ثانية
                searchTimeoutSupplier = setTimeout(() => {
                    performSearchSupplier(searchTerm);
                }, 300);
            });
        }

        if (clearButtonSupplier) {
            clearButtonSupplier.addEventListener('click', function() {
                searchInputSupplier.value = '';
                performSearchSupplier('');
            });
        }

        function performSearchSupplier(searchTerm) {
            // إظهار مؤشر التحميل
            tableBodySupplier.innerHTML = '<tr><td colspan="8" class="text-center py-4"><i class="fas fa-spinner fa-spin fa-2x text-muted"></i><p class="text-muted mt-2">جاري البحث...</p></td></tr>';

            // إرسال طلب AJAX
            fetch(`ajax_search_suppliers.php?search=${encodeURIComponent(searchTerm)}`)
                .then(response => response.text())
                .then(data => {
                    tableBodySupplier.innerHTML = data;
                })
                .catch(error => {
                    console.error('خطأ في البحث:', error);
                    tableBodySupplier.innerHTML = '<tr><td colspan="8" class="text-center py-4 text-danger"><i class="fas fa-exclamation-triangle fa-2x mb-2"></i><p>حدث خطأ في البحث</p></td></tr>';
                });
        }

        // تحديث الوقت والتاريخ
        function updateDateTime() {
            const now = new Date();
            const timeElement = document.getElementById('current-time');
            const dateElement = document.getElementById('current-date');

            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString('ar-SA', {
                    hour: '2-digit', minute: '2-digit'
                });
            }
            if (dateElement) {
                dateElement.textContent = now.toLocaleDateString('ar-SA');
            }
        }
        setInterval(updateDateTime, 60000);
        updateDateTime();

        // فتح نافذة إضافة مورد إذا كان هناك معرف في الرابط
        document.addEventListener('DOMContentLoaded', function() {
            if (window.location.hash === '#add-supplier') {
                const addSupplierModal = new bootstrap.Modal(document.getElementById('addSupplierModal'));
                addSupplierModal.show();
            }
        });
    </script>
</body>
</html>
