<?php
/**
 * SeaSystem - تقرير المبيعات
 * Sales Report
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/Invoice.php';
require_once __DIR__ . '/classes/Customer.php';

// التأكد من تسجيل الدخول
requireLogin();

$invoice = new Invoice();
$customer = new Customer();
$current_user = getCurrentUser();

// الحصول على المرشحات
$filters = [
    'date_from' => $_GET['date_from'] ?? date('Y-01-01'),
    'date_to' => $_GET['date_to'] ?? date('Y-m-d'),
    'customer_id' => $_GET['customer_id'] ?? '',
    'status' => $_GET['status'] ?? ''
];

// الحصول على بيانات المبيعات
$sales_data = $invoice->getSalesReport($filters);
$customers = $customer->getAll();

// حساب الإحصائيات
$total_sales = 0;
$total_invoices = count($sales_data);
$paid_amount = 0;
$pending_amount = 0;

foreach ($sales_data as $sale) {
    $total_sales += $sale['total_amount'];
    if ($sale['status'] == 'paid') {
        $paid_amount += $sale['total_amount'];
    } else {
        $pending_amount += $sale['total_amount'];
    }
}

// حالات الفواتير
$invoice_statuses = [
    'draft' => 'مسودة',
    'sent' => 'مرسلة',
    'paid' => 'مدفوعة',
    'overdue' => 'متأخرة',
    'cancelled' => 'ملغية'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المبيعات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <style>
        .report-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #28a745;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #28a745;
        }
        
        .filter-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            .report-header {
                background: #f8f9fa !important;
                color: #333 !important;
                border: 2px solid #dee2e6;
            }
        }
    </style>

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }
        
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->
    
    <div class="container-fluid">
        <div class="row">
            <!-- المحتوى الرئيسي -->
            <div class="col-12 p-4">
                <!-- أزرار الإجراءات -->
                <div class="row mb-3 no-print">
                    <div class="col">
                        <a href="reports.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                        </a>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.print()">
                                <i class="fas fa-print me-2"></i>طباعة
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-2"></i>تصدير Excel
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                            </button>
                        </div>
                    </div>
                </div>

                <!-- رأس التقرير -->
                <div class="report-header">
                    <h1 class="mb-2">
                        <i class="fas fa-chart-line me-3"></i>تقرير المبيعات
                    </h1>
                    <p class="mb-0">
                        من <?php echo date('d/m/Y', strtotime($filters['date_from'])); ?> 
                        إلى <?php echo date('d/m/Y', strtotime($filters['date_to'])); ?>
                    </p>
                    <small class="opacity-75">تم إنشاؤه في: <?php echo date('Y-m-d H:i'); ?></small>
                </div>

                <!-- مرشحات البحث -->
                <div class="filter-card no-print">
                    <h5 class="mb-3">
                        <i class="fas fa-filter me-2"></i>مرشحات التقرير
                    </h5>
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" name="date_from" 
                                   value="<?php echo $filters['date_from']; ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" name="date_to" 
                                   value="<?php echo $filters['date_to']; ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">العميل</label>
                            <select class="form-select" name="customer_id">
                                <option value="">جميع العملاء</option>
                                <?php foreach ($customers as $cust): ?>
                                    <option value="<?php echo $cust['id']; ?>" 
                                            <?php echo $filters['customer_id'] == $cust['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($cust['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" name="status">
                                <option value="">جميع الحالات</option>
                                <?php foreach ($invoice_statuses as $status => $status_name): ?>
                                    <option value="<?php echo $status; ?>" 
                                            <?php echo $filters['status'] == $status ? 'selected' : ''; ?>>
                                        <?php echo $status_name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- إحصائيات المبيعات -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="text-muted mb-1">إجمالي المبيعات</h6>
                                    <div class="stats-number">
                                        <?php echo number_format($total_sales, 2); ?>
                                        <small class="text-muted"><?php echo CURRENCY_SYMBOL; ?></small>
                                    </div>
                                </div>
                                <div class="text-success">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="text-muted mb-1">عدد الفواتير</h6>
                                    <div class="stats-number">
                                        <?php echo $total_invoices; ?>
                                        <small class="text-muted">فاتورة</small>
                                    </div>
                                </div>
                                <div class="text-info">
                                    <i class="fas fa-file-invoice fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="text-muted mb-1">المبلغ المحصل</h6>
                                    <div class="stats-number text-success">
                                        <?php echo number_format($paid_amount, 2); ?>
                                        <small class="text-muted"><?php echo CURRENCY_SYMBOL; ?></small>
                                    </div>
                                </div>
                                <div class="text-success">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="text-muted mb-1">المبلغ المعلق</h6>
                                    <div class="stats-number text-warning">
                                        <?php echo number_format($pending_amount, 2); ?>
                                        <small class="text-muted"><?php echo CURRENCY_SYMBOL; ?></small>
                                    </div>
                                </div>
                                <div class="text-warning">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول المبيعات -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>تفاصيل المبيعات
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>التاريخ</th>
                                        <th>العميل</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($sales_data)): ?>
                                        <tr>
                                            <td colspan="6" class="text-center py-4">
                                                <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                                <p class="text-muted mb-0">لا توجد مبيعات في الفترة المحددة</p>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($sales_data as $sale): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($sale['invoice_number']); ?></strong>
                                                </td>
                                                <td><?php echo date('d/m/Y', strtotime($sale['invoice_date'])); ?></td>
                                                <td><?php echo htmlspecialchars($sale['customer_name']); ?></td>
                                                <td>
                                                    <strong class="text-success">
                                                        <?php echo number_format($sale['total_amount'], 2); ?> <?php echo CURRENCY_SYMBOL; ?>
                                                    </strong>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status_class = '';
                                                    switch($sale['status']) {
                                                        case 'paid': $status_class = 'bg-success'; break;
                                                        case 'sent': $status_class = 'bg-info'; break;
                                                        case 'draft': $status_class = 'bg-secondary'; break;
                                                        case 'overdue': $status_class = 'bg-danger'; break;
                                                        case 'cancelled': $status_class = 'bg-dark'; break;
                                                        default: $status_class = 'bg-warning';
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>">
                                                        <?php echo $invoice_statuses[$sale['status']]; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="d-flex gap-2">
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                                    onclick="viewInvoice(<?php echo $sale['id']; ?>)">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">عرض</small>
                                                        </div>
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-info btn-sm"
                                                                    onclick="printInvoice(<?php echo $sale['id']; ?>)">
                                                                <i class="fas fa-print"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">طباعة</small>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                                <?php if (!empty($sales_data)): ?>
                                    <tfoot class="table-light">
                                        <tr>
                                            <th colspan="3">الإجمالي</th>
                                            <th>
                                                <strong class="text-success">
                                                    <?php echo number_format($total_sales, 2); ?> <?php echo CURRENCY_SYMBOL; ?>
                                                </strong>
                                            </th>
                                            <th colspan="2"></th>
                                        </tr>
                                    </tfoot>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>ملاحظات
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>تم حساب جميع المبالغ بالعملة الأساسية</li>
                                    <li><i class="fas fa-check text-success me-2"></i>الأرقام تشمل الضرائب والرسوم</li>
                                    <li><i class="fas fa-check text-success me-2"></i>البيانات محدثة حتى تاريخ اليوم</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>توزيع الحالات
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php
                                $status_counts = [];
                                foreach ($sales_data as $sale) {
                                    $status_counts[$sale['status']] = ($status_counts[$sale['status']] ?? 0) + 1;
                                }
                                ?>
                                <?php foreach ($status_counts as $status => $count): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span><?php echo $invoice_statuses[$status]; ?></span>
                                        <span class="badge bg-secondary"><?php echo $count; ?></span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function exportToExcel() {
            alert('سيتم إضافة وظيفة تصدير Excel قريباً');
        }
        
        function exportToPDF() {
            alert('سيتم إضافة وظيفة تصدير PDF قريباً');
        }
        
        function viewInvoice(invoiceId) {
            window.open(`invoice_view.php?id=${invoiceId}`, '_blank');
        }
        
        function printInvoice(invoiceId) {
            window.open(`invoice_print.php?id=${invoiceId}`, '_blank');
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
