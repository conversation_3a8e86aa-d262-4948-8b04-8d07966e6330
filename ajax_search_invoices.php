<?php
/**
 * البحث الديناميكي في الفواتير
 * Dynamic Invoice Search
 */

// تعريف الثابت للوصول
define('SEASYSTEM_ACCESS', true);

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/Invoice.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    exit('غير مصرح');
}

// التحقق من وجود مصطلح البحث
$search_term = $_GET['search'] ?? '';

// إنشاء كائن الفاتورة
$invoice = new Invoice($pdo);

// البحث في الفواتير
if (!empty($search_term)) {
    $invoices = $invoice->search($search_term);
} else {
    $invoices = $invoice->getAll();
}

// أنواع وحالات الفواتير
$invoice_types = [
    'sales' => 'فاتورة مبيعات',
    'purchase' => 'فاتورة مشتريات'
];

$invoice_statuses = [
    'draft' => 'مسودة',
    'sent' => 'مرسلة',
    'paid' => 'مدفوعة',
    'overdue' => 'متأخرة',
    'cancelled' => 'ملغية'
];

// عرض النتائج
if (empty($invoices)) {
    echo '<tr>
            <td colspan="9" class="text-center py-4">
                <i class="fas fa-search fa-2x text-muted mb-2"></i>
                <p class="text-muted">لا توجد فواتير تطابق البحث</p>
            </td>
          </tr>';
} else {
    foreach ($invoices as $inv) {
        // تحديد لون الحالة
        $status_class = '';
        switch ($inv['status']) {
            case 'draft':
                $status_class = 'bg-secondary';
                break;
            case 'sent':
                $status_class = 'bg-warning';
                break;
            case 'paid':
                $status_class = 'bg-success';
                break;
            case 'overdue':
                $status_class = 'bg-danger';
                break;
            case 'cancelled':
                $status_class = 'bg-dark';
                break;
        }

        // حساب المتبقي
        $remaining = $inv['total_amount'] - $inv['paid_amount'];
        $remaining_class = $remaining > 0 ? 'text-danger' : 'text-success';

        echo '<tr>
                <td><strong>' . htmlspecialchars($inv['invoice_number']) . '</strong></td>
                <td>
                    <span class="badge ' . ($inv['invoice_type'] == 'sales' ? 'bg-success' : 'bg-info') . '">' .
                        ($invoice_types[$inv['invoice_type']] ?? $inv['invoice_type']) .
                    '</span>
                </td>
                <td>' . htmlspecialchars($inv['customer_name'] ?? $inv['supplier_name'] ?? '-') . '</td>
                <td>' . date('Y-m-d', strtotime($inv['invoice_date'])) . '</td>
                <td>' . number_format($inv['total_amount'], 2) . ' ' . CURRENCY_SYMBOL . '</td>
                <td>' . number_format($inv['paid_amount'], 2) . ' ' . CURRENCY_SYMBOL . '</td>
                <td>
                    <span class="' . $remaining_class . '">' .
                        number_format($remaining, 2) . ' ' . CURRENCY_SYMBOL .
                    '</span>
                </td>
                <td>
                    <span class="badge ' . $status_class . '">' .
                        ($invoice_statuses[$inv['status']] ?? $inv['status']) .
                    '</span>
                </td>
                <td>
                    <div class="d-flex gap-2">
                        <div class="text-center">
                            <button type="button" class="btn btn-outline-primary btn-sm"
                                    onclick="viewInvoice(' . $inv['id'] . ')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <small class="d-block text-muted mt-1">عرض</small>
                        </div>
                        <div class="text-center">
                            <button type="button" class="btn btn-outline-success btn-sm"
                                    onclick="editInvoice(' . $inv['id'] . ')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <small class="d-block text-muted mt-1">تعديل</small>
                        </div>
                        <div class="text-center">
                            <button type="button" class="btn btn-outline-info btn-sm"
                                    onclick="printInvoice(' . $inv['id'] . ')">
                                <i class="fas fa-print"></i>
                            </button>
                            <small class="d-block text-muted mt-1">طباعة</small>
                        </div>' .
                        ($inv['status'] != 'paid' ?
                            '<div class="text-center">
                                <button type="button" class="btn btn-outline-warning btn-sm"
                                        onclick="recordPayment(' . $inv['id'] . ')">
                                    <i class="fas fa-money-bill"></i>
                                </button>
                                <small class="d-block text-muted mt-1">دفعة</small>
                            </div>' : '') .
                    '</div>
                </td>
              </tr>';
    }
}
?>
