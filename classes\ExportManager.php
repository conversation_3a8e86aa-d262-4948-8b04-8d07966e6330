<?php
/**
 * SeaSystem - مدير التصدير
 * Export Manager Class
 */

class ExportManager {
    
    /**
     * تصدير البيانات إلى CSV
     */
    public static function exportToCSV($data, $filename, $headers = []) {
        try {
            // تعيين headers للتحميل
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Expires: 0');
            
            // إنشاء output stream
            $output = fopen('php://output', 'w');
            
            // إضافة BOM للدعم العربي
            fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
            
            // كتابة العناوين
            if (!empty($headers)) {
                fputcsv($output, $headers);
            } elseif (!empty($data)) {
                fputcsv($output, array_keys($data[0]));
            }
            
            // كتابة البيانات
            foreach ($data as $row) {
                fputcsv($output, $row);
            }
            
            fclose($output);
            exit();
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في التصدير: ' . $e->getMessage()];
        }
    }
    
    /**
     * تصدير البيانات إلى Excel (HTML format)
     */
    public static function exportToExcel($data, $filename, $headers = [], $title = '') {
        try {
            // تعيين headers للتحميل
            header('Content-Type: application/vnd.ms-excel; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Expires: 0');
            
            // بدء HTML
            echo '<!DOCTYPE html>';
            echo '<html dir="rtl">';
            echo '<head>';
            echo '<meta charset="utf-8">';
            echo '<style>';
            echo 'table { border-collapse: collapse; width: 100%; font-family: Arial; }';
            echo 'th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }';
            echo 'th { background-color: #f2f2f2; font-weight: bold; }';
            echo '.title { font-size: 18px; font-weight: bold; margin-bottom: 20px; text-align: center; }';
            echo '</style>';
            echo '</head>';
            echo '<body>';
            
            // العنوان
            if ($title) {
                echo '<div class="title">' . htmlspecialchars($title) . '</div>';
            }
            
            // الجدول
            echo '<table>';
            
            // العناوين
            if (!empty($headers)) {
                echo '<tr>';
                foreach ($headers as $header) {
                    echo '<th>' . htmlspecialchars($header) . '</th>';
                }
                echo '</tr>';
            } elseif (!empty($data)) {
                echo '<tr>';
                foreach (array_keys($data[0]) as $key) {
                    echo '<th>' . htmlspecialchars($key) . '</th>';
                }
                echo '</tr>';
            }
            
            // البيانات
            foreach ($data as $row) {
                echo '<tr>';
                foreach ($row as $cell) {
                    echo '<td>' . htmlspecialchars($cell) . '</td>';
                }
                echo '</tr>';
            }
            
            echo '</table>';
            echo '</body>';
            echo '</html>';
            
            exit();
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في التصدير: ' . $e->getMessage()];
        }
    }
    
    /**
     * تصدير البيانات إلى PDF (HTML format)
     */
    public static function exportToPDF($data, $filename, $headers = [], $title = '') {
        try {
            // تعيين headers للتحميل
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . $filename . '.pdf"');
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Expires: 0');
            
            // إنشاء HTML للطباعة
            $html = '<!DOCTYPE html>';
            $html .= '<html dir="rtl">';
            $html .= '<head>';
            $html .= '<meta charset="utf-8">';
            $html .= '<style>';
            $html .= 'body { font-family: Arial, sans-serif; margin: 20px; }';
            $html .= 'table { border-collapse: collapse; width: 100%; margin-top: 20px; }';
            $html .= 'th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }';
            $html .= 'th { background-color: #f2f2f2; font-weight: bold; }';
            $html .= '.title { font-size: 18px; font-weight: bold; margin-bottom: 20px; text-align: center; }';
            $html .= '.header { text-align: center; margin-bottom: 30px; }';
            $html .= '.footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }';
            $html .= '</style>';
            $html .= '</head>';
            $html .= '<body>';
            
            // الهيدر
            $html .= '<div class="header">';
            $html .= '<h1>SeaSystem</h1>';
            if ($title) {
                $html .= '<div class="title">' . htmlspecialchars($title) . '</div>';
            }
            $html .= '<p>تاريخ التصدير: ' . date('Y-m-d H:i:s') . '</p>';
            $html .= '</div>';
            
            // الجدول
            $html .= '<table>';
            
            // العناوين
            if (!empty($headers)) {
                $html .= '<tr>';
                foreach ($headers as $header) {
                    $html .= '<th>' . htmlspecialchars($header) . '</th>';
                }
                $html .= '</tr>';
            } elseif (!empty($data)) {
                $html .= '<tr>';
                foreach (array_keys($data[0]) as $key) {
                    $html .= '<th>' . htmlspecialchars($key) . '</th>';
                }
                $html .= '</tr>';
            }
            
            // البيانات
            foreach ($data as $row) {
                $html .= '<tr>';
                foreach ($row as $cell) {
                    $html .= '<td>' . htmlspecialchars($cell) . '</td>';
                }
                $html .= '</tr>';
            }
            
            $html .= '</table>';
            
            // الفوتر
            $html .= '<div class="footer">';
            $html .= '<p>تم إنشاء هذا التقرير بواسطة نظام SeaSystem المحاسبي</p>';
            $html .= '</div>';
            
            $html .= '</body>';
            $html .= '</html>';
            
            // في الوقت الحالي، سنرسل HTML للطباعة
            // يمكن لاحقاً استخدام مكتبة مثل TCPDF أو DomPDF
            echo $html;
            exit();
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في التصدير: ' . $e->getMessage()];
        }
    }
    
    /**
     * تحضير البيانات للتصدير
     */
    public static function prepareDataForExport($data, $fieldsMap = []) {
        $exportData = [];
        
        foreach ($data as $row) {
            $exportRow = [];
            
            if (!empty($fieldsMap)) {
                foreach ($fieldsMap as $field => $label) {
                    $exportRow[$label] = $row[$field] ?? '';
                }
            } else {
                $exportRow = $row;
            }
            
            $exportData[] = $exportRow;
        }
        
        return $exportData;
    }
    
    /**
     * الحصول على اسم ملف آمن
     */
    public static function getSafeFilename($filename) {
        // إزالة الأحرف غير المسموحة
        $filename = preg_replace('/[^a-zA-Z0-9\-_\.]/', '_', $filename);
        
        // إضافة التاريخ والوقت
        $timestamp = date('Y-m-d_H-i-s');
        
        return $filename . '_' . $timestamp;
    }
}
?>
