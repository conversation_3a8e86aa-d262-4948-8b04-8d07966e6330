<?php
require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "🔄 تحديث قاعدة البيانات للمدفوعات...\n";
    
    // إنشاء جدول المدفوعات
    $sql = "CREATE TABLE IF NOT EXISTS payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        payment_number VARCHAR(50) UNIQUE NOT NULL,
        payment_type ENUM('received', 'paid') NOT NULL,
        payment_method ENUM('cash', 'bank_transfer', 'check', 'credit_card', 'online') NOT NULL,
        customer_id INT NULL,
        supplier_id INT NULL,
        amount DECIMAL(15,2) NOT NULL,
        payment_date DATE NOT NULL,
        reference_number VARCHAR(100),
        status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
        notes TEXT,
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    $db->exec($sql);
    echo "✅ جدول المدفوعات تم إنشاؤه\n";
    
    // إنشاء جدول الحسابات البنكية
    $sql = "CREATE TABLE IF NOT EXISTS bank_accounts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        account_name VARCHAR(100) NOT NULL,
        account_number VARCHAR(50) NOT NULL,
        bank_name VARCHAR(100) NOT NULL,
        current_balance DECIMAL(15,2) DEFAULT 0.00,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    $db->exec($sql);
    echo "✅ جدول الحسابات البنكية تم إنشاؤه\n";
    
    // إدراج بيانات تجريبية
    $sql = "INSERT IGNORE INTO payments (payment_number, payment_type, payment_method, customer_id, amount, payment_date, reference_number, status, created_by) VALUES
    ('REC202406001', 'received', 'bank_transfer', 1, 15000.00, '2024-06-10', 'TRF123456', 'completed', 1),
    ('PAY202406001', 'paid', 'check', NULL, 8500.00, '2024-06-12', 'CHK789012', 'completed', 1)";
    
    $db->exec($sql);
    echo "✅ البيانات التجريبية تم إدراجها\n";
    
    echo "\n🎉 تم تحديث قاعدة البيانات بنجاح!\n";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}
?>
