<?php
/**
 * إنشاء فواتير متقدمة إضافية
 */

require_once __DIR__ . '/config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>🧾 إنشاء فواتير متقدمة إضافية</h2>";
    
    // فاتورة شراء 3: من شركة الدلتا التجارية
    $purchase_invoice_3 = [
        'invoice_number' => 'PUR003',
        'invoice_type' => 'purchase',
        'supplier_id' => 3, // شركة الدلتا التجارية
        'invoice_date' => date('Y-m-d'),
        'due_date' => date('Y-m-d', strtotime('+45 days')),
        'subtotal' => 21000.00,
        'tax_amount' => 2940.00,
        'total_amount' => 23940.00,
        'notes' => 'فاتورة شراء من شركة الدلتا التجارية - شاشات وملحقات',
        'items' => [
            [
                'product_id' => 5, // شاشة سامسونج
                'quantity' => 6,
                'unit_price' => 3000.00,
                'total_price' => 18000.00
            ],
            [
                'product_id' => 3, // ماوس لاسلكي
                'quantity' => 20,
                'unit_price' => 150.00,
                'total_price' => 3000.00
            ]
        ]
    ];
    
    // إدراج فاتورة الشراء 3
    $sql = "INSERT INTO invoices (invoice_number, invoice_type, supplier_id, invoice_date, due_date, subtotal, tax_amount, total_amount, notes, status) 
            VALUES (:invoice_number, :invoice_type, :supplier_id, :invoice_date, :due_date, :subtotal, :tax_amount, :total_amount, :notes, 'pending')";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([
        ':invoice_number' => $purchase_invoice_3['invoice_number'],
        ':invoice_type' => $purchase_invoice_3['invoice_type'],
        ':supplier_id' => $purchase_invoice_3['supplier_id'],
        ':invoice_date' => $purchase_invoice_3['invoice_date'],
        ':due_date' => $purchase_invoice_3['due_date'],
        ':subtotal' => $purchase_invoice_3['subtotal'],
        ':tax_amount' => $purchase_invoice_3['tax_amount'],
        ':total_amount' => $purchase_invoice_3['total_amount'],
        ':notes' => $purchase_invoice_3['notes']
    ]);
    
    $invoice_id_3 = $db->lastInsertId();
    
    // إدراج عناصر فاتورة الشراء 3
    foreach ($purchase_invoice_3['items'] as $item) {
        $sql = "INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, total_price) 
                VALUES (:invoice_id, :product_id, :quantity, :unit_price, :total_price)";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            ':invoice_id' => $invoice_id_3,
            ':product_id' => $item['product_id'],
            ':quantity' => $item['quantity'],
            ':unit_price' => $item['unit_price'],
            ':total_price' => $item['total_price']
        ]);
        
        // تحديث المخزون
        $sql = "UPDATE products SET current_stock = current_stock + :quantity WHERE id = :product_id";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            ':quantity' => $item['quantity'],
            ':product_id' => $item['product_id']
        ]);
    }
    
    echo "✅ تم إنشاء فاتورة الشراء PUR003: 23,940 ج.م<br>";
    
    // فاتورة بيع 3: لمحمد أحمد السيد
    $sales_invoice_3 = [
        'invoice_number' => 'SAL003',
        'invoice_type' => 'sale',
        'customer_id' => 3, // محمد أحمد السيد
        'invoice_date' => date('Y-m-d'),
        'due_date' => date('Y-m-d', strtotime('+20 days')),
        'subtotal' => 13700.00,
        'tax_amount' => 1918.00,
        'total_amount' => 15618.00,
        'notes' => 'فاتورة بيع لمحمد أحمد السيد - شاشة وملحقات',
        'items' => [
            [
                'product_id' => 5, // شاشة سامسونج
                'quantity' => 2,
                'unit_price' => 4500.00,
                'total_price' => 9000.00
            ],
            [
                'product_id' => 4, // كيبورد ميكانيكي
                'quantity' => 3,
                'unit_price' => 1200.00,
                'total_price' => 3600.00
            ],
            [
                'product_id' => 3, // ماوس لاسلكي
                'quantity' => 4,
                'unit_price' => 250.00,
                'total_price' => 1000.00
            ]
        ]
    ];
    
    // إدراج فاتورة البيع 3
    $sql = "INSERT INTO invoices (invoice_number, invoice_type, customer_id, invoice_date, due_date, subtotal, tax_amount, total_amount, notes, status) 
            VALUES (:invoice_number, :invoice_type, :customer_id, :invoice_date, :due_date, :subtotal, :tax_amount, :total_amount, :notes, 'pending')";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([
        ':invoice_number' => $sales_invoice_3['invoice_number'],
        ':invoice_type' => $sales_invoice_3['invoice_type'],
        ':customer_id' => $sales_invoice_3['customer_id'],
        ':invoice_date' => $sales_invoice_3['invoice_date'],
        ':due_date' => $sales_invoice_3['due_date'],
        ':subtotal' => $sales_invoice_3['subtotal'],
        ':tax_amount' => $sales_invoice_3['tax_amount'],
        ':total_amount' => $sales_invoice_3['total_amount'],
        ':notes' => $sales_invoice_3['notes']
    ]);
    
    $invoice_id_4 = $db->lastInsertId();
    
    // إدراج عناصر فاتورة البيع 3
    foreach ($sales_invoice_3['items'] as $item) {
        $sql = "INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, total_price) 
                VALUES (:invoice_id, :product_id, :quantity, :unit_price, :total_price)";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            ':invoice_id' => $invoice_id_4,
            ':product_id' => $item['product_id'],
            ':quantity' => $item['quantity'],
            ':unit_price' => $item['unit_price'],
            ':total_price' => $item['total_price']
        ]);
        
        // تحديث المخزون (خصم الكمية المباعة)
        $sql = "UPDATE products SET current_stock = current_stock - :quantity WHERE id = :product_id";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            ':quantity' => $item['quantity'],
            ':product_id' => $item['product_id']
        ]);
    }
    
    echo "✅ تم إنشاء فاتورة البيع SAL003: 15,618 ج.م<br>";
    
    // فاتورة بيع 4: لسارة عبد الرحمن
    $sales_invoice_4 = [
        'invoice_number' => 'SAL004',
        'invoice_type' => 'sale',
        'customer_id' => 4, // سارة عبد الرحمن
        'invoice_date' => date('Y-m-d'),
        'due_date' => date('Y-m-d', strtotime('+15 days')),
        'subtotal' => 15500.00,
        'tax_amount' => 2170.00,
        'total_amount' => 17670.00,
        'notes' => 'فاتورة بيع لسارة عبد الرحمن - لابتوب وطابعة',
        'items' => [
            [
                'product_id' => 1, // لابتوب ديل
                'quantity' => 1,
                'unit_price' => 12000.00,
                'total_price' => 12000.00
            ],
            [
                'product_id' => 2, // طابعة HP
                'quantity' => 1,
                'unit_price' => 3000.00,
                'total_price' => 3000.00
            ],
            [
                'product_id' => 3, // ماوس لاسلكي
                'quantity' => 2,
                'unit_price' => 250.00,
                'total_price' => 500.00
            ]
        ]
    ];
    
    // إدراج فاتورة البيع 4
    $stmt = $db->prepare($sql);
    $stmt->execute([
        ':invoice_number' => $sales_invoice_4['invoice_number'],
        ':invoice_type' => $sales_invoice_4['invoice_type'],
        ':customer_id' => $sales_invoice_4['customer_id'],
        ':invoice_date' => $sales_invoice_4['invoice_date'],
        ':due_date' => $sales_invoice_4['due_date'],
        ':subtotal' => $sales_invoice_4['subtotal'],
        ':tax_amount' => $sales_invoice_4['tax_amount'],
        ':total_amount' => $sales_invoice_4['total_amount'],
        ':notes' => $sales_invoice_4['notes']
    ]);
    
    $invoice_id_5 = $db->lastInsertId();
    
    // إدراج عناصر فاتورة البيع 4
    foreach ($sales_invoice_4['items'] as $item) {
        $sql = "INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, total_price) 
                VALUES (:invoice_id, :product_id, :quantity, :unit_price, :total_price)";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            ':invoice_id' => $invoice_id_5,
            ':product_id' => $item['product_id'],
            ':quantity' => $item['quantity'],
            ':unit_price' => $item['unit_price'],
            ':total_price' => $item['total_price']
        ]);
        
        // تحديث المخزون (خصم الكمية المباعة)
        $sql = "UPDATE products SET current_stock = current_stock - :quantity WHERE id = :product_id";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            ':quantity' => $item['quantity'],
            ':product_id' => $item['product_id']
        ]);
    }
    
    echo "✅ تم إنشاء فاتورة البيع SAL004: 17,670 ج.م<br>";
    
    echo "<br><h3>📊 ملخص الفواتير الجديدة:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f8f9fa;'><th>رقم الفاتورة</th><th>النوع</th><th>العميل/المورد</th><th>المبلغ</th></tr>";
    echo "<tr><td>PUR003</td><td>شراء</td><td>شركة الدلتا التجارية</td><td>23,940 ج.م</td></tr>";
    echo "<tr><td>SAL003</td><td>بيع</td><td>محمد أحمد السيد</td><td>15,618 ج.م</td></tr>";
    echo "<tr><td>SAL004</td><td>بيع</td><td>سارة عبد الرحمن</td><td>17,670 ج.م</td></tr>";
    echo "</table>";
    
    echo "<br><strong>إجمالي المشتريات الجديدة:</strong> 23,940 ج.م<br>";
    echo "<strong>إجمالي المبيعات الجديدة:</strong> 33,288 ج.م<br>";
    echo "<strong>صافي الربح من العمليات الجديدة:</strong> " . number_format(33288 - 23940, 2) . " ج.م<br>";
    
    echo '<br><a href="invoices.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">عرض جميع الفواتير</a>';
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
