<?php
/**
 * SeaSystem - الحصول على الرقم التالي
 * Get Next Number AJAX
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/NumberGenerator.php';

// التأكد من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'غير مصرح لك بالوصول']);
    exit();
}

// التحقق من وجود نوع الكيان
if (!isset($_GET['type'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'نوع الكيان مطلوب']);
    exit();
}

try {
    $numberGenerator = new NumberGenerator();
    $type = $_GET['type'];
    $number = '';

    switch ($type) {
        case 'customer':
            $number = $numberGenerator->generateCustomerCode();
            break;

        case 'supplier':
            $number = $numberGenerator->generateSupplierCode();
            break;

        case 'product':
            $number = $numberGenerator->generateProductCode();
            break;

        case 'warehouse':
            $number = $numberGenerator->generateWarehouseCode();
            break;

        case 'invoice_sales':
            $number = $numberGenerator->generateInvoiceNumber('sales');
            break;

        case 'invoice_purchase':
            $number = $numberGenerator->generateInvoiceNumber('purchase');
            break;

        case 'payment':
            $payment_type = $_GET['payment_type'] ?? 'received';
            $number = $numberGenerator->generatePaymentNumber($payment_type);
            break;

        case 'journal':
            $number = $numberGenerator->generateJournalNumber();
            break;

        default:
            throw new Exception('نوع كيان غير مدعوم');
    }

    // إرجاع النتيجة
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'number' => $number,
        'type' => $type
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
