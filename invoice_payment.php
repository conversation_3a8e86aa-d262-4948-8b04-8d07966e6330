<?php
/**
 * SeaSystem - معالجة مدفوعات الفاتورة
 * Invoice Payment Handler
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/Invoice.php';
require_once __DIR__ . '/classes/Payment.php';

// التأكد من تسجيل الدخول
requireLogin();

// التحقق من الطلب
if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    header('Location: invoices.php');
    exit();
}

// إنشاء كائنات الفئات
$invoice = new Invoice();
$payment = new Payment();

// الحصول على بيانات المستخدم الحالي
$current_user = getCurrentUser();

$message = '';
$message_type = 'danger';
$redirect_url = 'invoices.php';

try {
    if (isset($_POST['action']) && $_POST['action'] == 'mark_paid') {
        // تسجيل الفاتورة كمدفوعة
        $invoice_id = (int)$_POST['invoice_id'];
        
        // الحصول على بيانات الفاتورة
        $invoice_data = $invoice->getById($invoice_id);
        
        if (!$invoice_data) {
            throw new Exception('الفاتورة غير موجودة');
        }
        
        if ($invoice_data['status'] == 'paid') {
            throw new Exception('الفاتورة مدفوعة مسبقاً');
        }
        
        // حساب المبلغ المتبقي
        $remaining_amount = $invoice_data['total_amount'] - $invoice_data['paid_amount'];
        
        if ($remaining_amount <= 0) {
            throw new Exception('لا يوجد مبلغ متبقي للدفع');
        }
        
        // إنشاء مدفوع تلقائي
        $payment_data = [
            'payment_number' => $payment->generatePaymentNumber($invoice_data['invoice_type'] == 'sales' ? 'received' : 'paid'),
            'payment_type' => $invoice_data['invoice_type'] == 'sales' ? 'received' : 'paid',
            'customer_id' => $invoice_data['customer_id'],
            'supplier_id' => $invoice_data['supplier_id'],
            'amount' => $remaining_amount,
            'payment_method' => 'cash',
            'payment_date' => date('Y-m-d'),
            'reference_number' => 'AUTO-' . $invoice_data['invoice_number'],
            'notes' => 'دفعة تلقائية لتسوية الفاتورة رقم ' . $invoice_data['invoice_number'],
            'status' => 'completed',
            'created_by' => $current_user['id'],
            'invoice_allocations' => [
                [
                    'invoice_id' => $invoice_id,
                    'amount' => $remaining_amount
                ]
            ]
        ];
        
        $result = $payment->create($payment_data);
        
        if ($result['success']) {
            $message = 'تم تسجيل الفاتورة كمدفوعة بنجاح';
            $message_type = 'success';
            $redirect_url = 'invoice_view.php?id=' . $invoice_id;
        } else {
            throw new Exception($result['message']);
        }
        
    } elseif (isset($_POST['action']) && $_POST['action'] == 'record_payment') {
        // تسجيل دفعة جزئية
        $invoice_id = (int)$_POST['invoice_id'];
        $amount = (float)$_POST['amount'];
        $payment_method = $_POST['payment_method'];
        $reference_number = $_POST['reference_number'] ?? '';
        $notes = $_POST['notes'] ?? '';
        
        // الحصول على بيانات الفاتورة
        $invoice_data = $invoice->getById($invoice_id);
        
        if (!$invoice_data) {
            throw new Exception('الفاتورة غير موجودة');
        }
        
        if ($invoice_data['status'] == 'paid') {
            throw new Exception('الفاتورة مدفوعة مسبقاً');
        }
        
        // التحقق من المبلغ
        $remaining_amount = $invoice_data['total_amount'] - $invoice_data['paid_amount'];
        
        if ($amount <= 0) {
            throw new Exception('يجب أن يكون المبلغ أكبر من صفر');
        }
        
        if ($amount > $remaining_amount) {
            throw new Exception('المبلغ أكبر من المبلغ المتبقي للدفع');
        }
        
        // إنشاء المدفوع
        $payment_data = [
            'payment_number' => $payment->generatePaymentNumber($invoice_data['invoice_type'] == 'sales' ? 'received' : 'paid'),
            'payment_type' => $invoice_data['invoice_type'] == 'sales' ? 'received' : 'paid',
            'customer_id' => $invoice_data['customer_id'],
            'supplier_id' => $invoice_data['supplier_id'],
            'amount' => $amount,
            'payment_method' => $payment_method,
            'payment_date' => date('Y-m-d'),
            'reference_number' => $reference_number,
            'notes' => $notes,
            'status' => 'completed',
            'created_by' => $current_user['id'],
            'invoice_allocations' => [
                [
                    'invoice_id' => $invoice_id,
                    'amount' => $amount
                ]
            ]
        ];
        
        $result = $payment->create($payment_data);
        
        if ($result['success']) {
            $message = 'تم تسجيل الدفعة بنجاح';
            $message_type = 'success';
            $redirect_url = 'invoice_view.php?id=' . $invoice_id;
        } else {
            throw new Exception($result['message']);
        }
    } else {
        throw new Exception('إجراء غير صحيح');
    }
    
} catch (Exception $e) {
    $message = 'خطأ: ' . $e->getMessage();
    $message_type = 'danger';
}

// إعادة التوجيه مع الرسالة
$redirect_url .= (strpos($redirect_url, '?') !== false ? '&' : '?') . $message_type . '=' . urlencode($message);
header('Location: ' . $redirect_url);
exit();
?>
