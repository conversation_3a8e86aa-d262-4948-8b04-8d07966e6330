<?php
/**
 * SeaSystem - تقرير قائمة الدخل
 * Income Statement Report
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/FinancialReport.php';

// التأكد من تسجيل الدخول
requireLogin();

$financial_report = new FinancialReport();
$current_user = getCurrentUser();

// الحصول على التواريخ من المعاملات
$date_from = $_GET['date_from'] ?? date('Y-01-01');
$date_to = $_GET['date_to'] ?? date('Y-m-d');

// الحصول على قائمة الدخل
$income_statement = $financial_report->getIncomeStatement($date_from, $date_to);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة الدخل - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <style>
        .report-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .profit-summary {
            position: fixed;
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
            z-index: 1000;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            padding: 1rem;
            min-width: 250px;
        }

        .section-header {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid #17a2b8;
        }

        .revenue-section {
            border-left-color: #28a745;
        }

        .expense-section {
            border-left-color: #dc3545;
        }

        .total-row {
            background: #e9ecef;
            font-weight: bold;
        }

        .subtotal-row {
            background: #f8f9fa;
            font-weight: bold;
            border-top: 2px solid #dee2e6;
        }

        .net-income-row {
            background: #d4edda;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .profit {
            color: #28a745;
        }

        .loss {
            color: #dc3545;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            .profit-summary {
                position: static;
                transform: none;
                margin-bottom: 1rem;
            }

            .report-header {
                background: #f8f9fa !important;
                color: #333 !important;
                border: 2px solid #dee2e6;
            }
        }
    </style>

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }
        
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <!-- ملخص الربحية -->
    <div class="profit-summary no-print">
        <div class="text-center">
            <i class="fas fa-chart-line fa-2x <?php echo $income_statement['net_income'] >= 0 ? 'profit' : 'loss'; ?>"></i>
            <h6 class="mt-2 mb-1">ملخص الأداء</h6>

            <div class="small mb-2">
                <div class="d-flex justify-content-between">
                    <span>الإيرادات:</span>
                    <strong class="text-success"><?php echo number_format($income_statement['total_revenue'], 0); ?></strong>
                </div>
                <div class="d-flex justify-content-between">
                    <span>المصروفات:</span>
                    <strong class="text-danger"><?php echo number_format($income_statement['total_operating_expenses'] + $income_statement['total_other_expenses'], 0); ?></strong>
                </div>
                <hr class="my-1">
                <div class="d-flex justify-content-between">
                    <span>صافي الدخل:</span>
                    <strong class="<?php echo $income_statement['net_income'] >= 0 ? 'profit' : 'loss'; ?>">
                        <?php echo number_format($income_statement['net_income'], 0); ?>
                    </strong>
                </div>
            </div>

            <span class="badge <?php echo $income_statement['net_income'] >= 0 ? 'bg-success' : 'bg-danger'; ?>">
                <?php echo $income_statement['net_income'] >= 0 ? 'ربح' : 'خسارة'; ?>
            </span>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- المحتوى الرئيسي -->
            <div class="col-12 p-4">
                <!-- أزرار الإجراءات -->
                <div class="row mb-3 no-print">
                    <div class="col">
                        <a href="reports.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                        </a>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.print()">
                                <i class="fas fa-print me-2"></i>طباعة
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-2"></i>تصدير Excel
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                            </button>
                        </div>
                    </div>
                </div>

                <!-- رأس التقرير -->
                <div class="report-header">
                    <h1 class="mb-2">
                        <i class="fas fa-chart-line me-3"></i>قائمة الدخل
                    </h1>
                    <h4 class="mb-3">Income Statement</h4>
                    <p class="mb-0">
                        من <?php echo date('d/m/Y', strtotime($income_statement['date_from'])); ?>
                        إلى <?php echo date('d/m/Y', strtotime($income_statement['date_to'])); ?>
                    </p>
                    <small class="opacity-75">تاريخ الإنشاء: <?php echo date('d/m/Y H:i'); ?></small>
                </div>

                <!-- فلترة التواريخ -->
                <div class="card mb-4 no-print">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="date_from" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from"
                                       value="<?php echo $date_from; ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="date_to" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to"
                                       value="<?php echo $date_to; ?>">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>تحديث التقرير
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- قائمة الدخل -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <tbody>
                                    <!-- الإيرادات -->
                                    <tr>
                                        <td colspan="2" class="section-header revenue-section">
                                            <h5 class="mb-0">
                                                <i class="fas fa-arrow-up me-2"></i>الإيرادات
                                            </h5>
                                        </td>
                                    </tr>

                                    <?php if (empty($income_statement['revenues'])): ?>
                                        <tr>
                                            <td class="ps-4">لا توجد إيرادات</td>
                                            <td class="text-end text-muted">-</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($income_statement['revenues'] as $revenue): ?>
                                            <?php if ($revenue['balance'] != 0): ?>
                                                <tr>
                                                    <td class="ps-4"><?php echo htmlspecialchars($revenue['account_name']); ?></td>
                                                    <td class="text-end"><?php echo number_format($revenue['balance'], 2); ?></td>
                                                </tr>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    <?php endif; ?>

                                    <tr class="subtotal-row">
                                        <td class="ps-4"><strong>إجمالي الإيرادات</strong></td>
                                        <td class="text-end">
                                            <strong><?php echo number_format($income_statement['total_revenue'], 2); ?></strong>
                                        </td>
                                    </tr>

                                    <!-- المصروفات التشغيلية -->
                                    <tr>
                                        <td colspan="2" class="section-header expense-section">
                                            <h5 class="mb-0">
                                                <i class="fas fa-arrow-down me-2"></i>المصروفات التشغيلية
                                            </h5>
                                        </td>
                                    </tr>

                                    <?php if (empty($income_statement['operating_expenses'])): ?>
                                        <tr>
                                            <td class="ps-4">لا توجد مصروفات تشغيلية</td>
                                            <td class="text-end text-muted">-</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($income_statement['operating_expenses'] as $expense): ?>
                                            <?php if ($expense['balance'] != 0): ?>
                                                <tr>
                                                    <td class="ps-4"><?php echo htmlspecialchars($expense['account_name']); ?></td>
                                                    <td class="text-end"><?php echo number_format($expense['balance'], 2); ?></td>
                                                </tr>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    <?php endif; ?>

                                    <tr class="subtotal-row">
                                        <td class="ps-4"><strong>إجمالي المصروفات التشغيلية</strong></td>
                                        <td class="text-end">
                                            <strong><?php echo number_format($income_statement['total_operating_expenses'], 2); ?></strong>
                                        </td>
                                    </tr>

                                    <!-- الدخل التشغيلي -->
                                    <tr class="total-row">
                                        <td class="ps-4"><strong>الدخل التشغيلي</strong></td>
                                        <td class="text-end">
                                            <strong class="<?php echo $income_statement['operating_income'] >= 0 ? 'profit' : 'loss'; ?>">
                                                <?php echo number_format($income_statement['operating_income'], 2); ?>
                                            </strong>
                                        </td>
                                    </tr>

                                    <!-- المصروفات الأخرى -->
                                    <?php if (!empty($income_statement['other_expenses'])): ?>
                                        <tr>
                                            <td colspan="2" class="section-header expense-section">
                                                <h5 class="mb-0">
                                                    <i class="fas fa-minus me-2"></i>المصروفات الأخرى
                                                </h5>
                                            </td>
                                        </tr>

                                        <?php foreach ($income_statement['other_expenses'] as $expense): ?>
                                            <?php if ($expense['balance'] != 0): ?>
                                                <tr>
                                                    <td class="ps-4"><?php echo htmlspecialchars($expense['account_name']); ?></td>
                                                    <td class="text-end"><?php echo number_format($expense['balance'], 2); ?></td>
                                                </tr>
                                            <?php endif; ?>
                                        <?php endforeach; ?>

                                        <tr class="subtotal-row">
                                            <td class="ps-4"><strong>إجمالي المصروفات الأخرى</strong></td>
                                            <td class="text-end">
                                                <strong><?php echo number_format($income_statement['total_other_expenses'], 2); ?></strong>
                                            </td>
                                        </tr>
                                    <?php endif; ?>

                                    <!-- صافي الدخل -->
                                    <tr class="net-income-row">
                                        <td class="ps-4">
                                            <strong>
                                                <i class="fas fa-trophy me-2"></i>
                                                صافي <?php echo $income_statement['net_income'] >= 0 ? 'الدخل' : 'الخسارة'; ?>
                                            </strong>
                                        </td>
                                        <td class="text-end">
                                            <strong class="<?php echo $income_statement['net_income'] >= 0 ? 'profit' : 'loss'; ?>">
                                                <?php echo number_format($income_statement['net_income'], 2); ?>
                                            </strong>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- تحليل الأداء -->
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success">إجمالي الإيرادات</h5>
                                <h3 class="text-success"><?php echo number_format($income_statement['total_revenue'], 0); ?></h3>
                                <p class="card-text">العائدات من جميع المصادر</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger">إجمالي المصروفات</h5>
                                <h3 class="text-danger">
                                    <?php echo number_format($income_statement['total_operating_expenses'] + $income_statement['total_other_expenses'], 0); ?>
                                </h3>
                                <p class="card-text">جميع التكاليف والمصروفات</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title <?php echo $income_statement['net_income'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                    صافي <?php echo $income_statement['net_income'] >= 0 ? 'الدخل' : 'الخسارة'; ?>
                                </h5>
                                <h3 class="<?php echo $income_statement['net_income'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                    <?php echo number_format($income_statement['net_income'], 0); ?>
                                </h3>
                                <p class="card-text">النتيجة النهائية للفترة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملاحظات -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-info-circle me-2"></i>ملاحظات
                        </h6>
                        <ul class="mb-0">
                            <li>قائمة الدخل تعرض الأداء المالي للشركة خلال فترة زمنية محددة</li>
                            <li>الإيرادات تشمل جميع المبالغ المحققة من الأنشطة التجارية</li>
                            <li>المصروفات مقسمة إلى تشغيلية وأخرى لتوضيح مصادر التكلفة</li>
                            <li>صافي الدخل = الإيرادات - المصروفات</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function exportToExcel() {
            alert('سيتم إضافة وظيفة تصدير Excel قريباً');
        }

        function exportToPDF() {
            alert('سيتم إضافة وظيفة تصدير PDF قريباً');
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
