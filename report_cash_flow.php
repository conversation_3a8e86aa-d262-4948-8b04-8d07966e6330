<?php
/**
 * SeaSystem - تقرير التدفق النقدي
 * Cash Flow Report
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/Payment.php';
require_once __DIR__ . '/classes/Account.php';

// التأكد من تسجيل الدخول
requireLogin();

$payment = new Payment();
$account = new Account();
$current_user = getCurrentUser();

// الحصول على المرشحات
$filters = [
    'date_from' => $_GET['date_from'] ?? date('Y-m-01'),
    'date_to' => $_GET['date_to'] ?? date('Y-m-d'),
    'account_id' => $_GET['account_id'] ?? '',
    'payment_type' => $_GET['payment_type'] ?? ''
];

// الحصول على بيانات التدفق النقدي
$cash_flow_data = $payment->getCashFlowReport($filters);
$accounts = $account->getAll();

// حساب الإحصائيات
$total_inflows = 0;
$total_outflows = 0;
$net_cash_flow = 0;

foreach ($cash_flow_data as $flow) {
    if ($flow['payment_type'] == 'received') {
        $total_inflows += $flow['amount'];
    } else {
        $total_outflows += $flow['amount'];
    }
}
$net_cash_flow = $total_inflows - $total_outflows;

// أنواع المدفوعات
$payment_types = [
    'received' => 'سند قبض',
    'paid' => 'سند صرف'
];

// طرق الدفع
$payment_methods = [
    'cash' => 'نقدي',
    'bank_transfer' => 'تحويل بنكي',
    'check' => 'شيك',
    'credit_card' => 'بطاقة ائتمان',
    'other' => 'أخرى'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير التدفق النقدي - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <style>
        .report-header {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #17a2b8;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #17a2b8;
        }
        
        .filter-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .cash-inflow {
            color: #28a745;
            font-weight: bold;
        }
        
        .cash-outflow {
            color: #dc3545;
            font-weight: bold;
        }
        
        .flow-badge-in {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .flow-badge-out {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .summary-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            .report-header {
                background: #f8f9fa !important;
                color: #333 !important;
                border: 2px solid #dee2e6;
            }
        }
    </style>

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }
        
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->
    
    <div class="container-fluid">
        <div class="row">
            <!-- المحتوى الرئيسي -->
            <div class="col-12 p-4">
                <!-- أزرار الإجراءات -->
                <div class="row mb-3 no-print">
                    <div class="col">
                        <a href="reports.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                        </a>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.print()">
                                <i class="fas fa-print me-2"></i>طباعة
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-2"></i>تصدير Excel
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                            </button>
                        </div>
                    </div>
                </div>

                <!-- رأس التقرير -->
                <div class="report-header">
                    <h1 class="mb-2">
                        <i class="fas fa-water me-3"></i>تقرير التدفق النقدي
                    </h1>
                    <p class="mb-0">
                        من <?php echo date('d/m/Y', strtotime($filters['date_from'])); ?> 
                        إلى <?php echo date('d/m/Y', strtotime($filters['date_to'])); ?>
                    </p>
                    <small class="opacity-75">تم إنشاؤه في: <?php echo date('Y-m-d H:i'); ?></small>
                </div>

                <!-- مرشحات البحث -->
                <div class="filter-card no-print">
                    <h5 class="mb-3">
                        <i class="fas fa-filter me-2"></i>مرشحات التقرير
                    </h5>
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" name="date_from" 
                                   value="<?php echo $filters['date_from']; ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" name="date_to" 
                                   value="<?php echo $filters['date_to']; ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الحساب</label>
                            <select class="form-select" name="account_id">
                                <option value="">جميع الحسابات</option>
                                <?php foreach ($accounts as $acc): ?>
                                    <option value="<?php echo $acc['id']; ?>" 
                                            <?php echo $filters['account_id'] == $acc['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($acc['account_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">نوع التدفق</label>
                            <select class="form-select" name="payment_type">
                                <option value="">جميع الأنواع</option>
                                <?php foreach ($payment_types as $type => $type_name): ?>
                                    <option value="<?php echo $type; ?>" 
                                            <?php echo $filters['payment_type'] == $type ? 'selected' : ''; ?>>
                                        <?php echo $type_name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- ملخص التدفق النقدي -->
                <div class="summary-section">
                    <h4 class="text-center mb-4">
                        <i class="fas fa-chart-bar me-2"></i>ملخص التدفق النقدي
                    </h4>
                    <div class="row">
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <h6 class="text-muted mb-1">إجمالي التدفقات الداخلة</h6>
                                        <div class="stats-number text-success">
                                            <?php echo number_format($total_inflows, 2); ?>
                                            <small class="text-muted"><?php echo CURRENCY_SYMBOL; ?></small>
                                        </div>
                                    </div>
                                    <div class="text-success">
                                        <i class="fas fa-arrow-down fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <h6 class="text-muted mb-1">إجمالي التدفقات الخارجة</h6>
                                        <div class="stats-number text-danger">
                                            <?php echo number_format($total_outflows, 2); ?>
                                            <small class="text-muted"><?php echo CURRENCY_SYMBOL; ?></small>
                                        </div>
                                    </div>
                                    <div class="text-danger">
                                        <i class="fas fa-arrow-up fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <h6 class="text-muted mb-1">صافي التدفق النقدي</h6>
                                        <div class="stats-number <?php echo $net_cash_flow >= 0 ? 'text-success' : 'text-danger'; ?>">
                                            <?php echo $net_cash_flow >= 0 ? '+' : ''; ?><?php echo number_format($net_cash_flow, 2); ?>
                                            <small class="text-muted"><?php echo CURRENCY_SYMBOL; ?></small>
                                        </div>
                                    </div>
                                    <div class="<?php echo $net_cash_flow >= 0 ? 'text-success' : 'text-danger'; ?>">
                                        <i class="fas fa-<?php echo $net_cash_flow >= 0 ? 'plus' : 'minus'; ?>-circle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <h6 class="text-muted mb-1">عدد المعاملات</h6>
                                        <div class="stats-number text-info">
                                            <?php echo count($cash_flow_data); ?>
                                            <small class="text-muted">معاملة</small>
                                        </div>
                                    </div>
                                    <div class="text-info">
                                        <i class="fas fa-exchange-alt fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول التدفق النقدي -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>تفاصيل التدفق النقدي
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>رقم السند</th>
                                        <th>نوع التدفق</th>
                                        <th>المبلغ</th>
                                        <th>الحساب</th>
                                        <th>طريقة الدفع</th>
                                        <th>الوصف</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($cash_flow_data)): ?>
                                        <tr>
                                            <td colspan="8" class="text-center py-4">
                                                <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                                <p class="text-muted mb-0">لا توجد تدفقات نقدية في الفترة المحددة</p>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($cash_flow_data as $flow): ?>
                                            <tr>
                                                <td><?php echo date('d/m/Y', strtotime($flow['payment_date'])); ?></td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($flow['payment_number']); ?></strong>
                                                </td>
                                                <td>
                                                    <span class="badge <?php echo $flow['payment_type'] == 'received' ? 'flow-badge-in' : 'flow-badge-out'; ?>">
                                                        <i class="fas fa-arrow-<?php echo $flow['payment_type'] == 'received' ? 'down' : 'up'; ?> me-1"></i>
                                                        <?php echo $payment_types[$flow['payment_type']]; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="<?php echo $flow['payment_type'] == 'received' ? 'cash-inflow' : 'cash-outflow'; ?>">
                                                        <?php echo $flow['payment_type'] == 'received' ? '+' : '-'; ?><?php echo number_format($flow['amount'], 2); ?> <?php echo CURRENCY_SYMBOL; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($flow['account_name'] ?? 'غير محدد'); ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <?php echo $payment_methods[$flow['payment_method']] ?? $flow['payment_method']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if (!empty($flow['description'])): ?>
                                                        <small><?php echo htmlspecialchars($flow['description']); ?></small>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="d-flex gap-2">
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                                    onclick="viewPayment(<?php echo $flow['id']; ?>)">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">عرض</small>
                                                        </div>
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-info btn-sm"
                                                                    onclick="printPayment(<?php echo $flow['id']; ?>)">
                                                                <i class="fas fa-print"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">طباعة</small>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                                <?php if (!empty($cash_flow_data)): ?>
                                    <tfoot class="table-light">
                                        <tr>
                                            <th colspan="3">الإجمالي</th>
                                            <th>
                                                <span class="text-success">+<?php echo number_format($total_inflows, 2); ?></span> |
                                                <span class="text-danger">-<?php echo number_format($total_outflows, 2); ?></span>
                                            </th>
                                            <th colspan="4">
                                                <strong>صافي: 
                                                    <span class="<?php echo $net_cash_flow >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                        <?php echo $net_cash_flow >= 0 ? '+' : ''; ?><?php echo number_format($net_cash_flow, 2); ?> <?php echo CURRENCY_SYMBOL; ?>
                                                    </span>
                                                </strong>
                                            </th>
                                        </tr>
                                    </tfoot>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>ملاحظات
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>التدفقات الداخلة تمثل الأموال المستلمة</li>
                                    <li><i class="fas fa-check text-success me-2"></i>التدفقات الخارجة تمثل الأموال المدفوعة</li>
                                    <li><i class="fas fa-check text-success me-2"></i>صافي التدفق = الداخل - الخارج</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>توزيع التدفقات
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php
                                $type_counts = [];
                                foreach ($cash_flow_data as $flow) {
                                    $type_counts[$flow['payment_type']] = ($type_counts[$flow['payment_type']] ?? 0) + 1;
                                }
                                ?>
                                <?php foreach ($type_counts as $type => $count): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span><?php echo $payment_types[$type]; ?></span>
                                        <span class="badge <?php echo $type == 'received' ? 'bg-success' : 'bg-danger'; ?>"><?php echo $count; ?></span>
                                    </div>
                                <?php endforeach; ?>
                                <hr>
                                <div class="d-flex justify-content-between align-items-center">
                                    <strong>الإجمالي</strong>
                                    <span class="badge bg-primary"><?php echo count($cash_flow_data); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function exportToExcel() {
            alert('سيتم إضافة وظيفة تصدير Excel قريباً');
        }
        
        function exportToPDF() {
            alert('سيتم إضافة وظيفة تصدير PDF قريباً');
        }
        
        function viewPayment(paymentId) {
            window.open(`payment_view.php?id=${paymentId}`, '_blank');
        }
        
        function printPayment(paymentId) {
            window.open(`payment_print.php?id=${paymentId}`, '_blank');
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
