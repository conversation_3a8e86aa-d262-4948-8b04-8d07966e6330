<?php
/**
 * SeaSystem - فئة إدارة المنتجات
 * Product Management Class
 */

require_once __DIR__ . '/../config/database.php';

class Product {
    private $db;
    private $table_name = "products";

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }

    /**
     * الحصول على جميع المنتجات
     */
    public function getAll($filters = []) {
        try {
            $sql = "SELECT p.*,
                           pc.category_name,
                           u.unit_name,
                           (p.current_stock * p.cost_price) as stock_value
                    FROM " . $this->table_name . " p
                    LEFT JOIN product_categories pc ON p.category_id = pc.id
                    LEFT JOIN units_of_measure u ON p.base_unit_id = u.id
                    WHERE p.is_active = 1";

            $params = [];

            // تطبيق المرشحات
            if (!empty($filters['category_id'])) {
                $sql .= " AND p.category_id = :category_id";
                $params[':category_id'] = $filters['category_id'];
            }

            if (!empty($filters['search'])) {
                $sql .= " AND (p.product_name LIKE :search OR p.product_code LIKE :search OR p.barcode LIKE :search)";
                $params[':search'] = '%' . $filters['search'] . '%';
            }

            $sql .= " ORDER BY p.product_name ASC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على منتج واحد
     */
    public function getById($product_id) {
        try {
            $sql = "SELECT * FROM " . $this->table_name . " WHERE id = :product_id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':product_id', $product_id);
            $stmt->execute();

            return $stmt->fetch();
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * إضافة منتج جديد
     */
    public function create($data) {
        try {
            // التحقق من عدم تكرار رمز المنتج
            if ($this->isProductCodeExists($data['product_code'])) {
                return [
                    'success' => false,
                    'message' => 'رمز المنتج موجود مسبقاً'
                ];
            }

            $sql = "INSERT INTO " . $this->table_name . "
                    (product_code, product_name, description, unit_price, cost_price,
                     current_stock, min_stock_level, category_id, is_active)
                    VALUES (:product_code, :product_name, :description, :unit_price, :cost_price,
                            :current_stock, :min_stock_level, :category_id, 1)";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':product_code', $data['product_code']);
            $stmt->bindParam(':product_name', $data['product_name']);
            $stmt->bindParam(':description', $data['description']);
            $stmt->bindParam(':unit_price', $data['unit_price']);
            $stmt->bindParam(':cost_price', $data['cost_price']);
            $stmt->bindParam(':current_stock', $data['current_stock']);
            $stmt->bindParam(':min_stock_level', $data['min_stock_level']);
            $stmt->bindParam(':category_id', $data['category_id']);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم إضافة المنتج بنجاح',
                    'product_id' => $this->db->lastInsertId()
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في إضافة المنتج'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * تحديث المنتج
     */
    public function update($product_id, $data) {
        try {
            // التحقق من وجود المنتج
            if (!$this->exists($product_id)) {
                return [
                    'success' => false,
                    'message' => 'المنتج غير موجود'
                ];
            }

            // التحقق من عدم تكرار رمز المنتج
            if ($this->isProductCodeExists($data['product_code'], $product_id)) {
                return [
                    'success' => false,
                    'message' => 'رمز المنتج موجود مسبقاً'
                ];
            }

            $sql = "UPDATE " . $this->table_name . "
                    SET product_code = :product_code,
                        product_name = :product_name,
                        description = :description,
                        unit_price = :unit_price,
                        cost_price = :cost_price,
                        current_stock = :current_stock,
                        min_stock_level = :min_stock_level,
                        category_id = :category_id,
                        updated_at = NOW()
                    WHERE id = :product_id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':product_code', $data['product_code']);
            $stmt->bindParam(':product_name', $data['product_name']);
            $stmt->bindParam(':description', $data['description']);
            $stmt->bindParam(':unit_price', $data['unit_price']);
            $stmt->bindParam(':cost_price', $data['cost_price']);
            $stmt->bindParam(':current_stock', $data['current_stock']);
            $stmt->bindParam(':min_stock_level', $data['min_stock_level']);
            $stmt->bindParam(':category_id', $data['category_id']);
            $stmt->bindParam(':product_id', $product_id);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم تحديث المنتج بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في تحديث المنتج'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * حذف المنتج (إلغاء تفعيل)
     */
    public function delete($product_id) {
        try {
            // التحقق من وجود المنتج
            if (!$this->exists($product_id)) {
                return [
                    'success' => false,
                    'message' => 'المنتج غير موجود'
                ];
            }

            $sql = "UPDATE " . $this->table_name . "
                    SET is_active = 0, updated_at = NOW()
                    WHERE id = :product_id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':product_id', $product_id);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم حذف المنتج بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في حذف المنتج'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * التحقق من وجود المنتج
     */
    public function exists($product_id) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE id = :product_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':product_id', $product_id);
            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من تكرار رمز المنتج
     */
    private function isProductCodeExists($product_code, $exclude_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . "
                    WHERE product_code = :product_code AND is_active = 1";

            if ($exclude_id) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':product_code', $product_code);

            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }

            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * تحديث المخزون
     */
    public function updateStock($product_id, $quantity, $operation = 'add') {
        try {
            $product = $this->getById($product_id);
            if (!$product) {
                return [
                    'success' => false,
                    'message' => 'المنتج غير موجود'
                ];
            }

            $new_stock = $product['current_stock'];

            if ($operation == 'add') {
                $new_stock += $quantity;
            } else if ($operation == 'subtract') {
                $new_stock -= $quantity;
                if ($new_stock < 0) {
                    return [
                        'success' => false,
                        'message' => 'الكمية المطلوبة غير متوفرة في المخزون'
                    ];
                }
            }

            $sql = "UPDATE " . $this->table_name . "
                    SET current_stock = :current_stock, updated_at = NOW()
                    WHERE id = :product_id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':current_stock', $new_stock);
            $stmt->bindParam(':product_id', $product_id);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم تحديث المخزون بنجاح',
                    'new_stock' => $new_stock
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في تحديث المخزون'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }
}
?>
