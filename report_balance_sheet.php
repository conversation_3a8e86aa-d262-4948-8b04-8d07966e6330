<?php
/**
 * SeaSystem - تقرير الميزانية العمومية
 * Balance Sheet Report
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/FinancialReport.php';

// التأكد من تسجيل الدخول
requireLogin();

$financial_report = new FinancialReport();
$current_user = getCurrentUser();

// الحصول على التاريخ من المعاملات
$as_of_date = $_GET['as_of_date'] ?? date('Y-m-d');

// الحصول على الميزانية العمومية
$balance_sheet = $financial_report->getBalanceSheet($as_of_date);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الميزانية العمومية - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <style>
        .report-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .balance-summary {
            position: fixed;
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
            z-index: 1000;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            padding: 1rem;
            min-width: 250px;
        }

        .section-header {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid #28a745;
        }

        .assets-section {
            border-left-color: #007bff;
        }

        .liabilities-section {
            border-left-color: #dc3545;
        }

        .equity-section {
            border-left-color: #28a745;
        }

        .total-row {
            background: #e9ecef;
            font-weight: bold;
        }

        .balanced {
            color: #28a745;
        }

        .unbalanced {
            color: #dc3545;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            .balance-summary {
                position: static;
                transform: none;
                margin-bottom: 1rem;
            }

            .report-header {
                background: #f8f9fa !important;
                color: #333 !important;
                border: 2px solid #dee2e6;
            }
        }
    </style>

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }
        
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <!-- ملخص التوازن -->
    <div class="balance-summary no-print">
        <div class="text-center">
            <i class="fas fa-chart-pie fa-2x <?php echo $balance_sheet['is_balanced'] ? 'balanced' : 'unbalanced'; ?>"></i>
            <h6 class="mt-2 mb-1">ملخص الميزانية</h6>

            <div class="small mb-2">
                <div class="d-flex justify-content-between">
                    <span>الأصول:</span>
                    <strong><?php echo number_format($balance_sheet['total_assets'], 0); ?></strong>
                </div>
                <div class="d-flex justify-content-between">
                    <span>الخصوم:</span>
                    <strong><?php echo number_format($balance_sheet['total_liabilities'], 0); ?></strong>
                </div>
                <div class="d-flex justify-content-between">
                    <span>حقوق الملكية:</span>
                    <strong><?php echo number_format($balance_sheet['total_equity'], 0); ?></strong>
                </div>
                <hr class="my-1">
                <div class="d-flex justify-content-between">
                    <span>المجموع:</span>
                    <strong><?php echo number_format($balance_sheet['total_liabilities_equity'], 0); ?></strong>
                </div>
            </div>

            <span class="badge <?php echo $balance_sheet['is_balanced'] ? 'bg-success' : 'bg-danger'; ?>">
                <?php echo $balance_sheet['is_balanced'] ? 'متوازنة' : 'غير متوازنة'; ?>
            </span>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- المحتوى الرئيسي -->
            <div class="col-12 p-4">
                <!-- أزرار الإجراءات -->
                <div class="row mb-3 no-print">
                    <div class="col">
                        <a href="reports.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                        </a>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.print()">
                                <i class="fas fa-print me-2"></i>طباعة
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-2"></i>تصدير Excel
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                            </button>
                        </div>
                    </div>
                </div>

                <!-- رأس التقرير -->
                <div class="report-header">
                    <h1 class="mb-2">
                        <i class="fas fa-chart-pie me-3"></i>الميزانية العمومية
                    </h1>
                    <h4 class="mb-3">Balance Sheet</h4>
                    <p class="mb-0">
                        كما في <?php echo date('d/m/Y', strtotime($balance_sheet['as_of_date'])); ?>
                    </p>
                    <small class="opacity-75">تاريخ الإنشاء: <?php echo date('d/m/Y H:i'); ?></small>
                </div>

                <!-- فلترة التاريخ -->
                <div class="card mb-4 no-print">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-6">
                                <label for="as_of_date" class="form-label">كما في تاريخ</label>
                                <input type="date" class="form-control" id="as_of_date" name="as_of_date"
                                       value="<?php echo $as_of_date; ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>تحديث التقرير
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="row">
                    <!-- الأصول -->
                    <div class="col-lg-6 mb-4">
                        <div class="card h-100">
                            <div class="section-header assets-section">
                                <h5 class="mb-0">
                                    <i class="fas fa-building me-2"></i>الأصول
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <tbody>
                                            <?php if (empty($balance_sheet['assets'])): ?>
                                                <tr>
                                                    <td colspan="2" class="text-center text-muted">لا توجد أصول</td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($balance_sheet['assets'] as $asset): ?>
                                                    <?php if ($asset['balance'] != 0): ?>
                                                        <tr>
                                                            <td><?php echo htmlspecialchars($asset['account_name']); ?></td>
                                                            <td class="text-end">
                                                                <?php echo number_format($asset['balance'], 2); ?>
                                                            </td>
                                                        </tr>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                        <tfoot>
                                            <tr class="total-row">
                                                <td><strong>إجمالي الأصول</strong></td>
                                                <td class="text-end">
                                                    <strong><?php echo number_format($balance_sheet['total_assets'], 2); ?></strong>
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الخصوم وحقوق الملكية -->
                    <div class="col-lg-6 mb-4">
                        <!-- الخصوم -->
                        <div class="card mb-3">
                            <div class="section-header liabilities-section">
                                <h5 class="mb-0">
                                    <i class="fas fa-credit-card me-2"></i>الخصوم
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <tbody>
                                            <?php if (empty($balance_sheet['liabilities'])): ?>
                                                <tr>
                                                    <td colspan="2" class="text-center text-muted">لا توجد خصوم</td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($balance_sheet['liabilities'] as $liability): ?>
                                                    <?php if ($liability['balance'] != 0): ?>
                                                        <tr>
                                                            <td><?php echo htmlspecialchars($liability['account_name']); ?></td>
                                                            <td class="text-end">
                                                                <?php echo number_format($liability['balance'], 2); ?>
                                                            </td>
                                                        </tr>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                        <tfoot>
                                            <tr class="total-row">
                                                <td><strong>إجمالي الخصوم</strong></td>
                                                <td class="text-end">
                                                    <strong><?php echo number_format($balance_sheet['total_liabilities'], 2); ?></strong>
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- حقوق الملكية -->
                        <div class="card">
                            <div class="section-header equity-section">
                                <h5 class="mb-0">
                                    <i class="fas fa-user-tie me-2"></i>حقوق الملكية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <tbody>
                                            <?php if (empty($balance_sheet['equity'])): ?>
                                                <tr>
                                                    <td colspan="2" class="text-center text-muted">لا توجد حقوق ملكية</td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($balance_sheet['equity'] as $equity_item): ?>
                                                    <?php if ($equity_item['balance'] != 0): ?>
                                                        <tr>
                                                            <td><?php echo htmlspecialchars($equity_item['account_name']); ?></td>
                                                            <td class="text-end">
                                                                <?php echo number_format($equity_item['balance'], 2); ?>
                                                            </td>
                                                        </tr>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                        <tfoot>
                                            <tr class="total-row">
                                                <td><strong>إجمالي حقوق الملكية</strong></td>
                                                <td class="text-end">
                                                    <strong><?php echo number_format($balance_sheet['total_equity'], 2); ?></strong>
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملخص التوازن -->
                <div class="card">
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-4">
                                <div class="border-end">
                                    <h4 class="text-primary"><?php echo number_format($balance_sheet['total_assets'], 0); ?></h4>
                                    <p class="mb-0">إجمالي الأصول</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="border-end">
                                    <h4 class="text-danger"><?php echo number_format($balance_sheet['total_liabilities'], 0); ?></h4>
                                    <p class="mb-0">إجمالي الخصوم</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <h4 class="text-success"><?php echo number_format($balance_sheet['total_equity'], 0); ?></h4>
                                <p class="mb-0">إجمالي حقوق الملكية</p>
                            </div>
                        </div>

                        <hr>

                        <div class="text-center">
                            <h5 class="<?php echo $balance_sheet['is_balanced'] ? 'balanced' : 'unbalanced'; ?>">
                                <?php if ($balance_sheet['is_balanced']): ?>
                                    <i class="fas fa-check-circle me-2"></i>الميزانية متوازنة
                                <?php else: ?>
                                    <i class="fas fa-exclamation-triangle me-2"></i>الميزانية غير متوازنة
                                    <br>
                                    <small>
                                        الفرق: <?php echo number_format(abs($balance_sheet['total_assets'] - $balance_sheet['total_liabilities_equity']), 2); ?>
                                    </small>
                                <?php endif; ?>
                            </h5>
                        </div>
                    </div>
                </div>

                <!-- ملاحظات -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-info-circle me-2"></i>ملاحظات
                        </h6>
                        <ul class="mb-0">
                            <li>الميزانية العمومية تعرض المركز المالي للشركة في تاريخ محدد</li>
                            <li>يجب أن يكون إجمالي الأصول مساوياً لمجموع الخصوم وحقوق الملكية</li>
                            <li>الأرقام معروضة بالعملة الأساسية للنظام</li>
                            <li>يتم احتساب صافي الدخل تلقائياً وإضافته لحقوق الملكية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function exportToExcel() {
            alert('سيتم إضافة وظيفة تصدير Excel قريباً');
        }

        function exportToPDF() {
            alert('سيتم إضافة وظيفة تصدير PDF قريباً');
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
