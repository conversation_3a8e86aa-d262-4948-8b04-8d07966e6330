# 🌊 SeaSystem - نظام المحاسبة الشامل

نظام محاسبي شامل مطور بلغة PHP و MySQL لإدارة العمليات المحاسبية والمالية بكفاءة واحترافية.

## 🚀 المميزات

### ✅ المميزات المكتملة
- **نظام مصادقة آمن** - تسجيل دخول وخروج مع إدارة الجلسات
- **لوحة تحكم تفاعلية** - عرض الإحصائيات والبيانات المهمة
- **إدارة دليل الحسابات** - نظام شامل لإدارة الحسابات المالية
- **إدارة العملاء والموردين** - قاعدة بيانات شاملة للعملاء والموردين
- **نظام الفواتير الكامل** - إنشاء، تعديل، عرض وإدارة فواتير المبيعات والمشتريات
- **نظام المدفوعات** - تسجيل وإدارة المدفوعات والمقبوضات
- **دفتر اليومية** - تسجيل القيود المحاسبية اليدوية والتلقائية
- **نظام التقارير المالية** - تقارير شاملة للحسابات والفواتير والمدفوعات
- **إدارة المخزون** - تتبع المنتجات وحركات المخزون
- **إدارة المنتجات** - قاعدة بيانات شاملة للمنتجات والخدمات
- **واجهة مستخدم عصرية** - تصميم متجاوب يدعم جميع الأجهزة
- **دعم اللغة العربية** - واجهة باللغة العربية بالكامل
- **نظام اختبار شامل** - صفحة اختبار لفحص جميع مكونات النظام

### 🔄 المميزات المتقدمة (قيد التطوير)
- الميزانية العمومية التفصيلية
- قائمة الدخل الشاملة
- تقارير التدفق النقدي
- نظام الموازنات والتخطيط المالي

## 📋 متطلبات النظام

- **PHP** 7.4 أو أحدث
- **MySQL** 5.7 أو أحدث
- **Apache/Nginx** خادم ويب
- **XAMPP/WAMP** (للتطوير المحلي)

## 🛠️ التثبيت والإعداد

### 1. تحميل الملفات
```bash
git clone [repository-url]
cd seasystem
```

### 2. إعداد قاعدة البيانات
```bash
# تشغيل سكريبت إنشاء قاعدة البيانات
php create_database.php
```

أو يدوياً:
1. افتح phpMyAdmin
2. أنشئ قاعدة بيانات جديدة باسم `seasystem`
3. استورد ملف `database/schema.sql`

### 3. إعداد الاتصال
تأكد من إعدادات قاعدة البيانات في `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'seasystem');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### 4. تشغيل النظام
```bash
# باستخدام خادم PHP المدمج
php -S localhost:8000

# أو ضع الملفات في مجلد htdocs
# واذهب إلى http://localhost/seasystem
```

## 🔐 بيانات الدخول الافتراضية

- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

## 📁 هيكل المشروع

```
seasystem/
├── assets/                    # الملفات الثابتة
│   ├── css/
│   │   └── style.css         # ملف التنسيق الرئيسي
│   └── js/
│       └── main.js           # ملف JavaScript الرئيسي
├── classes/                   # فئات PHP
│   ├── Account.php           # إدارة الحسابات
│   ├── Customer.php          # إدارة العملاء
│   ├── Supplier.php          # إدارة الموردين
│   ├── Invoice.php           # إدارة الفواتير
│   ├── Payment.php           # إدارة المدفوعات
│   ├── JournalEntry.php      # دفتر اليومية
│   ├── FinancialReport.php   # التقارير المالية
│   ├── Inventory.php         # إدارة المخزون
│   └── Product.php           # إدارة المنتجات
├── config/                    # ملفات الإعداد
│   ├── config.php            # الإعدادات الرئيسية
│   ├── database.php          # إعدادات قاعدة البيانات
│   └── .htaccess             # حماية ملفات الإعداد
├── includes/                  # ملفات مساعدة
│   ├── auth.php              # نظام المصادقة
│   └── .htaccess             # حماية الملفات المساعدة
├── الصفحات الرئيسية:
├── index.php                  # الصفحة الرئيسية
├── dashboard.php              # لوحة التحكم
├── login.php                  # تسجيل الدخول
├── logout.php                 # تسجيل الخروج
├── إدارة الحسابات:
├── accounts.php               # قائمة الحسابات
├── account_create.php         # إضافة حساب جديد
├── إدارة العملاء والموردين:
├── customers.php              # قائمة العملاء
├── customer_create.php        # إضافة عميل جديد
├── إدارة الفواتير:
├── invoices.php               # قائمة الفواتير
├── invoice_create.php         # إنشاء فاتورة جديدة
├── invoice_view.php           # عرض الفاتورة
├── invoice_edit.php           # تعديل الفاتورة
├── invoice_payment.php        # معالجة مدفوعات الفاتورة
├── إدارة المدفوعات:
├── payments.php               # قائمة المدفوعات
├── payment_create.php         # إنشاء مدفوع جديد
├── دفتر اليومية:
├── journal_entries.php        # قائمة القيود
├── journal_entry_create.php   # إنشاء قيد جديد
├── التقارير:
├── reports.php                # التقارير الرئيسية
├── report_trial_balance.php   # ميزان المراجعة
├── report_income_statement.php # قائمة الدخل
├── report_balance_sheet.php   # الميزانية العمومية
├── report_inventory.php       # تقرير المخزون
├── إدارة المخزون:
├── inventory.php              # إدارة المخزون
├── products.php               # إدارة المنتجات
├── الاختبار والصيانة:
├── test.php                   # صفحة اختبار النظام
├── create_database.php        # إنشاء قاعدة البيانات
└── README.md                  # هذا الملف
```

## 🎯 كيفية الاستخدام

### 1. تسجيل الدخول
- اذهب إلى الصفحة الرئيسية
- أدخل بيانات الدخول
- ستنتقل إلى لوحة التحكم

### 2. إدارة الحسابات
- من القائمة الجانبية اختر "دليل الحسابات"
- يمكنك إضافة، تعديل، أو حذف الحسابات
- الحسابات مقسمة إلى: أصول، خصوم، حقوق ملكية، إيرادات، مصروفات

### 3. إدارة العملاء
- اختر "العملاء" من القائمة
- أضف بيانات العملاء الكاملة
- تتبع أرصدة العملاء والفواتير المستحقة

### 4. إنشاء الفواتير
- اختر "الفواتير" من القائمة
- أنشئ فاتورة مبيعات أو مشتريات جديدة
- أضف العناصر والكميات والأسعار
- احفظ الفاتورة وتتبع حالتها

## 🔧 التخصيص والتطوير

### إضافة ميزات جديدة
1. أنشئ فئة جديدة في مجلد `classes/`
2. أضف الجداول المطلوبة في قاعدة البيانات
3. أنشئ صفحات الواجهة المطلوبة
4. أضف الروابط في القائمة الجانبية

### تخصيص التصميم
- عدّل ملف `assets/css/style.css`
- استخدم Bootstrap 5 للمكونات الجاهزة
- الألوان الأساسية محددة في متغيرات CSS

### إضافة تقارير
- أنشئ فئة `Report.php` في مجلد `classes/`
- استخدم مكتبات PDF مثل TCPDF أو FPDF
- أضف صفحة `reports.php` لعرض التقارير

## 🛡️ الأمان

- **تشفير كلمات المرور** باستخدام `password_hash()`
- **حماية من SQL Injection** باستخدام Prepared Statements
- **إدارة الجلسات** مع انتهاء صلاحية تلقائي
- **التحقق من الصلاحيات** لكل صفحة
- **تسجيل الأنشطة** لمراقبة العمليات

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**خطأ في الاتصال بقاعدة البيانات:**
- تأكد من تشغيل MySQL
- تحقق من إعدادات الاتصال في `config/database.php`

**صفحة بيضاء أو أخطاء PHP:**
- فعّل عرض الأخطاء: `ini_set('display_errors', 1);`
- تحقق من ملفات السجل (logs)

**مشاكل في الجلسات:**
- تأكد من أن مجلد `sessions` قابل للكتابة
- تحقق من إعدادات `session.save_path`

## 📞 الدعم والمساعدة

### اختبار النظام
قم بزيارة `http://localhost/seasystem/test.php` لاختبار جميع مكونات النظام.

صفحة الاختبار تفحص:
- الاتصال بقاعدة البيانات
- جميع الفئات والملفات المطلوبة
- متطلبات PHP والإضافات
- أذونات المجلدات
- حالة جميع الصفحات

إذا ظهرت أي أخطاء، يرجى إصلاحها قبل استخدام النظام.

### تسجيل الأخطاء
جميع الأخطاء يتم تسجيلها في ملفات السجل. تحقق من:
- سجل أخطاء PHP
- سجل أخطاء MySQL
- سجل أخطاء Apache/Nginx

## 📈 خطة التطوير المستقبلية

### المرحلة الثانية
- [ ] نظام التقارير المالية الشامل
- [ ] إدارة المخزون
- [ ] نظام نقاط البيع (POS)
- [ ] تطبيق الجوال

### المرحلة الثالثة
- [ ] التكامل مع البنوك
- [ ] النسخ الاحتياطي التلقائي
- [ ] نظام الإشعارات
- [ ] API للتكامل مع أنظمة أخرى

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للمزيد من التفاصيل.

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

---

**تم تطوير النظام بواسطة:** فريق SeaSystem
**تاريخ الإصدار:** يونيو 2025
**الإصدار:** 1.0.0

🌊 **SeaSystem - نحو محاسبة أكثر ذكاءً وفعالية**
