<?php
/**
 * SeaSystem - نظام حماية CSRF
 * CSRF Protection System
 */

class CSRF {
    
    /**
     * إنشاء رمز CSRF جديد
     */
    public static function generateToken() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        $token = bin2hex(random_bytes(32));
        $_SESSION[CSRF_TOKEN_NAME] = [
            'token' => $token,
            'expire' => time() + CSRF_TOKEN_EXPIRE
        ];
        
        return $token;
    }
    
    /**
     * التحقق من صحة رمز CSRF
     */
    public static function validateToken($token) {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
            return false;
        }
        
        $sessionToken = $_SESSION[CSRF_TOKEN_NAME];
        
        // التحقق من انتهاء الصلاحية
        if (time() > $sessionToken['expire']) {
            unset($_SESSION[CSRF_TOKEN_NAME]);
            return false;
        }
        
        // التحقق من تطابق الرمز
        if (!hash_equals($sessionToken['token'], $token)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * الحصول على رمز CSRF الحالي أو إنشاء جديد
     */
    public static function getToken() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION[CSRF_TOKEN_NAME]) || 
            time() > $_SESSION[CSRF_TOKEN_NAME]['expire']) {
            return self::generateToken();
        }
        
        return $_SESSION[CSRF_TOKEN_NAME]['token'];
    }
    
    /**
     * إنشاء حقل مخفي للنموذج
     */
    public static function getHiddenField() {
        $token = self::getToken();
        return '<input type="hidden" name="' . CSRF_TOKEN_NAME . '" value="' . htmlspecialchars($token) . '">';
    }
    
    /**
     * التحقق من الطلب POST
     */
    public static function validateRequest() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $token = $_POST[CSRF_TOKEN_NAME] ?? '';
            
            if (!self::validateToken($token)) {
                http_response_code(403);
                die('خطأ في التحقق من الأمان - CSRF Token Invalid');
            }
        }
    }
}

/**
 * دوال مساعدة سريعة
 */
function csrf_token() {
    return CSRF::getToken();
}

function csrf_field() {
    return CSRF::getHiddenField();
}

function csrf_validate() {
    return CSRF::validateRequest();
}
?>
