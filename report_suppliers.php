<?php
/**
 * SeaSystem - تقرير الموردين
 * Suppliers Report
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/Supplier.php';
require_once __DIR__ . '/classes/Invoice.php';

// التأكد من تسجيل الدخول
requireLogin();

$supplier = new Supplier();
$invoice = new Invoice();
$current_user = getCurrentUser();

// الحصول على المرشحات
$filters = [
    'status' => $_GET['status'] ?? '',
    'search' => $_GET['search'] ?? ''
];

// الحصول على بيانات الموردين
$suppliers_data = $supplier->getSuppliersReport($filters);

// حساب الإحصائيات
$total_suppliers = count($suppliers_data);
$active_suppliers = count(array_filter($suppliers_data, function($s) { return $s['is_active'] == 1; }));
$inactive_suppliers = $total_suppliers - $active_suppliers;

// حساب إجمالي المشتريات لكل مورد
foreach ($suppliers_data as &$supplier_data) {
    $purchase_stats = $invoice->getStatistics([
        'invoice_type' => 'purchase',
        'supplier_id' => $supplier_data['id']
    ]);
    $supplier_data['total_purchases'] = $purchase_stats['total_amount'] ?? 0;
    $supplier_data['outstanding_amount'] = $purchase_stats['total_outstanding'] ?? 0;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الموردين - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <style>
        .report-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #6f42c1;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #6f42c1;
        }
        
        .filter-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .supplier-card {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .supplier-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .supplier-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6f42c1, #e83e8c);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            .report-header {
                background: #f8f9fa !important;
                color: #333 !important;
                border: 2px solid #dee2e6;
            }
            
            .supplier-card {
                break-inside: avoid;
                margin-bottom: 1rem;
            }
        }
    </style>

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }
        
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->
    
    <div class="container-fluid">
        <div class="row">
            <!-- المحتوى الرئيسي -->
            <div class="col-12 p-4">
                <!-- أزرار الإجراءات -->
                <div class="row mb-3 no-print">
                    <div class="col">
                        <a href="reports.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                        </a>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.print()">
                                <i class="fas fa-print me-2"></i>طباعة
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-2"></i>تصدير Excel
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                            </button>
                        </div>
                    </div>
                </div>

                <!-- رأس التقرير -->
                <div class="report-header">
                    <h1 class="mb-2">
                        <i class="fas fa-truck me-3"></i>تقرير الموردين
                    </h1>
                    <p class="mb-0">قائمة شاملة بجميع الموردين وبياناتهم المالية</p>
                    <small class="opacity-75">تم إنشاؤه في: <?php echo date('Y-m-d H:i'); ?></small>
                </div>

                <!-- مرشحات البحث -->
                <div class="filter-card no-print">
                    <h5 class="mb-3">
                        <i class="fas fa-filter me-2"></i>مرشحات التقرير
                    </h5>
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">البحث</label>
                            <input type="text" class="form-control" name="search" 
                                   placeholder="البحث في اسم المورد أو البريد الإلكتروني..."
                                   value="<?php echo htmlspecialchars($filters['search']); ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="active" <?php echo $filters['status'] == 'active' ? 'selected' : ''; ?>>نشط</option>
                                <option value="inactive" <?php echo $filters['status'] == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>بحث
                            </button>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <a href="report_suppliers.php" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-redo me-2"></i>إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>

                <!-- إحصائيات الموردين -->
                <div class="row mb-4">
                    <div class="col-lg-4 col-md-6">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="text-muted mb-1">إجمالي الموردين</h6>
                                    <div class="stats-number">
                                        <?php echo $total_suppliers; ?>
                                        <small class="text-muted">مورد</small>
                                    </div>
                                </div>
                                <div class="text-primary">
                                    <i class="fas fa-truck fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 col-md-6">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="text-muted mb-1">الموردين النشطين</h6>
                                    <div class="stats-number text-success">
                                        <?php echo $active_suppliers; ?>
                                        <small class="text-muted">مورد</small>
                                    </div>
                                </div>
                                <div class="text-success">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 col-md-6">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="text-muted mb-1">الموردين غير النشطين</h6>
                                    <div class="stats-number text-warning">
                                        <?php echo $inactive_suppliers; ?>
                                        <small class="text-muted">مورد</small>
                                    </div>
                                </div>
                                <div class="text-warning">
                                    <i class="fas fa-pause-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة الموردين -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>تفاصيل الموردين
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($suppliers_data)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد موردين</h5>
                                <p class="text-muted">لم يتم العثور على موردين مطابقين للمرشحات المحددة</p>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($suppliers_data as $supplier_info): ?>
                                    <div class="col-lg-6 col-xl-4">
                                        <div class="supplier-card">
                                            <div class="d-flex align-items-start">
                                                <div class="supplier-avatar me-3">
                                                    <?php echo strtoupper(substr($supplier_info['name'], 0, 2)); ?>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1">
                                                        <?php echo htmlspecialchars($supplier_info['name']); ?>
                                                        <?php if ($supplier_info['is_active']): ?>
                                                            <span class="badge bg-success ms-2">نشط</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-secondary ms-2">غير نشط</span>
                                                        <?php endif; ?>
                                                    </h6>
                                                    <p class="text-muted small mb-2">
                                                        <i class="fas fa-code me-1"></i>
                                                        <?php echo htmlspecialchars($supplier_info['supplier_code']); ?>
                                                    </p>
                                                    
                                                    <?php if (!empty($supplier_info['email'])): ?>
                                                        <p class="text-muted small mb-1">
                                                            <i class="fas fa-envelope me-1"></i>
                                                            <?php echo htmlspecialchars($supplier_info['email']); ?>
                                                        </p>
                                                    <?php endif; ?>
                                                    
                                                    <?php if (!empty($supplier_info['phone'])): ?>
                                                        <p class="text-muted small mb-1">
                                                            <i class="fas fa-phone me-1"></i>
                                                            <?php echo htmlspecialchars($supplier_info['phone']); ?>
                                                        </p>
                                                    <?php endif; ?>
                                                    
                                                    <?php if (!empty($supplier_info['address'])): ?>
                                                        <p class="text-muted small mb-2">
                                                            <i class="fas fa-map-marker-alt me-1"></i>
                                                            <?php echo htmlspecialchars($supplier_info['address']); ?>
                                                        </p>
                                                    <?php endif; ?>
                                                    
                                                    <!-- الإحصائيات المالية -->
                                                    <div class="row text-center mt-3">
                                                        <div class="col-6">
                                                            <div class="border-end">
                                                                <h6 class="text-primary mb-0">
                                                                    <?php echo number_format($supplier_info['total_purchases'], 0); ?>
                                                                </h6>
                                                                <small class="text-muted">إجمالي المشتريات</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <h6 class="text-warning mb-0">
                                                                <?php echo number_format($supplier_info['outstanding_amount'], 0); ?>
                                                            </h6>
                                                            <small class="text-muted">المبلغ المستحق</small>
                                                        </div>
                                                    </div>
                                                    
                                                    <!-- أزرار الإجراءات -->
                                                    <div class="d-flex gap-2 mt-3 no-print">
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                                    onclick="viewSupplier(<?php echo $supplier_info['id']; ?>)">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">عرض</small>
                                                        </div>
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-info btn-sm"
                                                                    onclick="supplierStatement(<?php echo $supplier_info['id']; ?>)">
                                                                <i class="fas fa-file-alt"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">كشف حساب</small>
                                                        </div>
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-success btn-sm"
                                                                    onclick="supplierPurchases(<?php echo $supplier_info['id']; ?>)">
                                                                <i class="fas fa-shopping-cart"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">المشتريات</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>ملاحظات
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>يتم تحديث البيانات المالية تلقائياً</li>
                                    <li><i class="fas fa-check text-success me-2"></i>الموردين غير النشطين لا يظهرون في القوائم الجديدة</li>
                                    <li><i class="fas fa-check text-success me-2"></i>يمكن تصدير التقرير بصيغ مختلفة</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>توزيع الموردين
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>الموردين النشطين</span>
                                    <span class="badge bg-success"><?php echo $active_suppliers; ?></span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>الموردين غير النشطين</span>
                                    <span class="badge bg-secondary"><?php echo $inactive_suppliers; ?></span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between align-items-center">
                                    <strong>الإجمالي</strong>
                                    <span class="badge bg-primary"><?php echo $total_suppliers; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function exportToExcel() {
            alert('سيتم إضافة وظيفة تصدير Excel قريباً');
        }
        
        function exportToPDF() {
            alert('سيتم إضافة وظيفة تصدير PDF قريباً');
        }
        
        function viewSupplier(supplierId) {
            window.open(`supplier_view.php?id=${supplierId}`, '_blank');
        }
        
        function supplierStatement(supplierId) {
            window.open(`supplier_statement.php?id=${supplierId}`, '_blank');
        }
        
        function supplierPurchases(supplierId) {
            window.open(`report_purchases.php?supplier_id=${supplierId}`, '_blank');
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
