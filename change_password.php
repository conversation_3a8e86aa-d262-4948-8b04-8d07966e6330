<?php
/**
 * SeaSystem - تغيير كلمة المرور
 * Change Password Handler
 */

session_start();
require_once 'config/config.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $old_password = $_POST['old_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';

    // التحقق من صحة البيانات
    if (empty($old_password) || empty($new_password) || empty($confirm_password)) {
        $message = 'جميع الحقول مطلوبة';
        $messageType = 'error';
    } elseif ($new_password !== $confirm_password) {
        $message = 'كلمة المرور الجديدة وتأكيدها غير متطابقين';
        $messageType = 'error';
    } elseif (strlen($new_password) < 6) {
        $message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        $messageType = 'error';
    } else {
        // محاولة تغيير كلمة المرور
        global $auth;
        $result = $auth->changePassword($_SESSION['user_id'], $old_password, $new_password);
        
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'error';
    }
}

// إعادة توجيه إلى الصفحة السابقة مع الرسالة
$redirect_url = $_SERVER['HTTP_REFERER'] ?? 'dashboard.php';
$redirect_url .= (strpos($redirect_url, '?') !== false ? '&' : '?') . 'password_message=' . urlencode($message) . '&password_type=' . $messageType;

header("Location: $redirect_url");
exit();
?>
