<?php
/**
 * SeaSystem - التحقق من حذف المنتجات
 * Verify Products Deletion
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/auth.php';

// التأكد من تسجيل الدخول
requireLogin();

// الحصول على بيانات المستخدم الحالي
$current_user = getCurrentUser();

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحقق من حذف المنتجات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    
    <style>
        body { padding-top: 80px !important; }
        .verify-card { background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.08); padding: 2rem; margin-bottom: 2rem; }
        .status-success { background: linear-gradient(45deg, #28a745, #20c997); color: white; }
        .status-warning { background: linear-gradient(45deg, #ffc107, #fd7e14); color: white; }
        .status-danger { background: linear-gradient(45deg, #dc3545, #e83e8c); color: white; }
        .check-item { padding: 1rem; border-radius: 8px; margin-bottom: 1rem; }
        .check-success { background: #d4edda; border-left: 4px solid #28a745; }
        .check-warning { background: #fff3cd; border-left: 4px solid #ffc107; }
        .check-danger { background: #f8d7da; border-left: 4px solid #dc3545; }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12 p-4">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="page-title">
                                <i class="fas fa-search me-2"></i>التحقق من حذف المنتجات
                            </h1>
                            <p class="page-subtitle">فحص حالة قاعدة البيانات والتأكد من حذف المنتجات</p>
                        </div>
                        <div class="col-auto">
                            <button onclick="location.reload()" class="btn btn-outline-primary">
                                <i class="fas fa-sync-alt me-2"></i>إعادة فحص
                            </button>
                        </div>
                    </div>
                </div>

                <?php
                try {
                    $database = new Database();
                    $pdo = $database->getConnection();
                    
                    // فحص المنتجات
                    echo '<div class="verify-card">';
                    echo '<h3 class="mb-4"><i class="fas fa-database me-2"></i>نتائج الفحص</h3>';
                    
                    // 1. فحص جدول المنتجات
                    echo '<div class="check-item">';
                    $products_count = $pdo->query("SELECT COUNT(*) FROM products")->fetchColumn();
                    
                    if ($products_count == 0) {
                        echo '<div class="check-success">';
                        echo '<h5><i class="fas fa-check-circle text-success me-2"></i>جدول المنتجات</h5>';
                        echo '<p class="mb-0">✅ تم حذف جميع المنتجات بنجاح - الجدول فارغ تماماً</p>';
                        echo '</div>';
                    } else {
                        echo '<div class="check-danger">';
                        echo '<h5><i class="fas fa-exclamation-triangle text-danger me-2"></i>جدول المنتجات</h5>';
                        echo '<p class="mb-0">❌ ما زال يوجد ' . $products_count . ' منتج في قاعدة البيانات</p>';
                        echo '</div>';
                    }
                    echo '</div>';
                    
                    // 2. فحص AUTO_INCREMENT
                    echo '<div class="check-item">';
                    $auto_increment_info = $pdo->query("SHOW TABLE STATUS LIKE 'products'")->fetch();
                    $auto_increment = $auto_increment_info['Auto_increment'];
                    
                    if ($auto_increment == 1) {
                        echo '<div class="check-success">';
                        echo '<h5><i class="fas fa-check-circle text-success me-2"></i>معرف AUTO_INCREMENT</h5>';
                        echo '<p class="mb-0">✅ تم إعادة تعيين AUTO_INCREMENT إلى 1</p>';
                        echo '</div>';
                    } else {
                        echo '<div class="check-warning">';
                        echo '<h5><i class="fas fa-exclamation-triangle text-warning me-2"></i>معرف AUTO_INCREMENT</h5>';
                        echo '<p class="mb-0">⚠️ AUTO_INCREMENT = ' . $auto_increment . ' (لم يتم إعادة تعيينه)</p>';
                        echo '</div>';
                    }
                    echo '</div>';
                    
                    // 3. فحص حركات المخزون
                    echo '<div class="check-item">';
                    try {
                        $movements_count = $pdo->query("SELECT COUNT(*) FROM inventory_movements")->fetchColumn();
                        
                        if ($movements_count == 0) {
                            echo '<div class="check-success">';
                            echo '<h5><i class="fas fa-check-circle text-success me-2"></i>حركات المخزون</h5>';
                            echo '<p class="mb-0">✅ لا توجد حركات مخزون مرتبطة بمنتجات محذوفة</p>';
                            echo '</div>';
                        } else {
                            // فحص الحركات المرتبطة بمنتجات غير موجودة
                            $orphaned_movements = $pdo->query("SELECT COUNT(*) FROM inventory_movements WHERE product_id NOT IN (SELECT id FROM products)")->fetchColumn();
                            
                            if ($orphaned_movements == 0) {
                                echo '<div class="check-success">';
                                echo '<h5><i class="fas fa-check-circle text-success me-2"></i>حركات المخزون</h5>';
                                echo '<p class="mb-0">✅ جميع حركات المخزون صحيحة (' . $movements_count . ' حركة)</p>';
                                echo '</div>';
                            } else {
                                echo '<div class="check-warning">';
                                echo '<h5><i class="fas fa-exclamation-triangle text-warning me-2"></i>حركات المخزون</h5>';
                                echo '<p class="mb-0">⚠️ يوجد ' . $orphaned_movements . ' حركة مخزون مرتبطة بمنتجات محذوفة</p>';
                                echo '</div>';
                            }
                        }
                    } catch (Exception $e) {
                        echo '<div class="check-warning">';
                        echo '<h5><i class="fas fa-info-circle text-info me-2"></i>حركات المخزون</h5>';
                        echo '<p class="mb-0">ℹ️ جدول حركات المخزون غير موجود</p>';
                        echo '</div>';
                    }
                    echo '</div>';
                    
                    // 4. فحص عناصر الفواتير
                    echo '<div class="check-item">';
                    try {
                        $invoice_items_count = $pdo->query("SELECT COUNT(*) FROM invoice_items")->fetchColumn();
                        
                        if ($invoice_items_count == 0) {
                            echo '<div class="check-success">';
                            echo '<h5><i class="fas fa-check-circle text-success me-2"></i>عناصر الفواتير</h5>';
                            echo '<p class="mb-0">✅ لا توجد عناصر فواتير مرتبطة بمنتجات محذوفة</p>';
                            echo '</div>';
                        } else {
                            // فحص العناصر المرتبطة بمنتجات غير موجودة
                            $orphaned_items = $pdo->query("SELECT COUNT(*) FROM invoice_items WHERE product_id NOT IN (SELECT id FROM products)")->fetchColumn();
                            
                            if ($orphaned_items == 0) {
                                echo '<div class="check-success">';
                                echo '<h5><i class="fas fa-check-circle text-success me-2"></i>عناصر الفواتير</h5>';
                                echo '<p class="mb-0">✅ جميع عناصر الفواتير صحيحة (' . $invoice_items_count . ' عنصر)</p>';
                                echo '</div>';
                            } else {
                                echo '<div class="check-warning">';
                                echo '<h5><i class="fas fa-exclamation-triangle text-warning me-2"></i>عناصر الفواتير</h5>';
                                echo '<p class="mb-0">⚠️ يوجد ' . $orphaned_items . ' عنصر فاتورة مرتبط بمنتجات محذوفة</p>';
                                echo '</div>';
                            }
                        }
                    } catch (Exception $e) {
                        echo '<div class="check-warning">';
                        echo '<h5><i class="fas fa-info-circle text-info me-2"></i>عناصر الفواتير</h5>';
                        echo '<p class="mb-0">ℹ️ جدول عناصر الفواتير غير موجود</p>';
                        echo '</div>';
                    }
                    echo '</div>';
                    
                    // 5. فحص التصنيفات ووحدات القياس
                    echo '<div class="check-item">';
                    $categories_count = $pdo->query("SELECT COUNT(*) FROM product_categories")->fetchColumn();
                    $units_count = $pdo->query("SELECT COUNT(*) FROM units_of_measure")->fetchColumn();
                    
                    echo '<div class="check-success">';
                    echo '<h5><i class="fas fa-check-circle text-success me-2"></i>البيانات المساعدة</h5>';
                    echo '<p class="mb-0">✅ التصنيفات: ' . $categories_count . ' | وحدات القياس: ' . $units_count . ' (محتفظ بها)</p>';
                    echo '</div>';
                    echo '</div>';
                    
                    echo '</div>';
                    
                    // ملخص النتائج
                    echo '<div class="verify-card">';
                    
                    if ($products_count == 0 && $auto_increment == 1) {
                        echo '<div class="alert alert-success">';
                        echo '<h4><i class="fas fa-check-circle me-2"></i>تم الحذف بنجاح!</h4>';
                        echo '<p class="mb-0">تم حذف جميع المنتجات بنجاح وقاعدة البيانات نظيفة ومستعدة لإضافة منتجات جديدة.</p>';
                        echo '</div>';
                        
                        echo '<div class="text-center">';
                        echo '<a href="inventory.php#add-product-tab" class="btn btn-success btn-lg me-3">';
                        echo '<i class="fas fa-plus me-2"></i>إضافة منتجات جديدة';
                        echo '</a>';
                        echo '<a href="dashboard.php" class="btn btn-primary btn-lg">';
                        echo '<i class="fas fa-tachometer-alt me-2"></i>العودة للوحة التحكم';
                        echo '</a>';
                        echo '</div>';
                        
                    } elseif ($products_count > 0) {
                        echo '<div class="alert alert-danger">';
                        echo '<h4><i class="fas fa-exclamation-triangle me-2"></i>لم يتم الحذف!</h4>';
                        echo '<p class="mb-0">ما زال يوجد ' . $products_count . ' منتج في قاعدة البيانات. قد تحتاج لإعادة تشغيل عملية الحذف.</p>';
                        echo '</div>';
                        
                        echo '<div class="text-center">';
                        echo '<a href="delete_all_products.php" class="btn btn-danger btn-lg me-3">';
                        echo '<i class="fas fa-trash-alt me-2"></i>إعادة محاولة الحذف';
                        echo '</a>';
                        echo '<a href="inventory.php" class="btn btn-info btn-lg">';
                        echo '<i class="fas fa-boxes me-2"></i>عرض المنتجات';
                        echo '</a>';
                        echo '</div>';
                        
                    } else {
                        echo '<div class="alert alert-warning">';
                        echo '<h4><i class="fas fa-exclamation-triangle me-2"></i>حذف جزئي!</h4>';
                        echo '<p class="mb-0">تم حذف المنتجات ولكن هناك بعض المشاكل الطفيفة في قاعدة البيانات.</p>';
                        echo '</div>';
                    }
                    
                    echo '</div>';
                    
                    // معلومات إضافية
                    echo '<div class="verify-card bg-light">';
                    echo '<h4><i class="fas fa-info-circle me-2"></i>معلومات إضافية</h4>';
                    echo '<div class="row">';
                    
                    echo '<div class="col-md-6">';
                    echo '<h6>📊 إحصائيات قاعدة البيانات:</h6>';
                    echo '<ul>';
                    echo '<li>المنتجات: ' . $products_count . '</li>';
                    echo '<li>التصنيفات: ' . $categories_count . '</li>';
                    echo '<li>وحدات القياس: ' . $units_count . '</li>';
                    echo '<li>AUTO_INCREMENT: ' . $auto_increment . '</li>';
                    echo '</ul>';
                    echo '</div>';
                    
                    echo '<div class="col-md-6">';
                    echo '<h6>🕒 وقت الفحص:</h6>';
                    echo '<p>' . date('Y-m-d H:i:s') . '</p>';
                    echo '<h6>👤 المستخدم:</h6>';
                    echo '<p>' . htmlspecialchars($current_user['username']) . '</p>';
                    echo '</div>';
                    
                    echo '</div>';
                    echo '</div>';
                    
                } catch (Exception $e) {
                    echo '<div class="verify-card">';
                    echo '<div class="alert alert-danger">';
                    echo '<h4><i class="fas fa-exclamation-triangle me-2"></i>خطأ في الاتصال!</h4>';
                    echo '<p class="mb-0">لا يمكن الوصول لقاعدة البيانات: ' . htmlspecialchars($e->getMessage()) . '</p>';
                    echo '</div>';
                    echo '</div>';
                }
                ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
