<?php
/**
 * SeaSystem - صفحة عرض المدفوع
 * View Payment Page
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/Payment.php';

// التأكد من تسجيل الدخول
requireLogin();

$payment = new Payment();
$current_user = getCurrentUser();

// الحصول على معرف المدفوع
$payment_id = $_GET['id'] ?? 0;
if (!$payment_id) {
    header('Location: payments.php');
    exit();
}

// الحصول على بيانات المدفوع
$payment_data = $payment->getById($payment_id);
if (!$payment_data) {
    header('Location: payments.php?error=' . urlencode('المدفوع غير موجود'));
    exit();
}

// الحصول على ربط الفواتير
$allocations = [];
try {
    $sql = "SELECT pia.*, i.invoice_number, i.total_amount as invoice_total
            FROM payment_invoice_allocations pia
            JOIN invoices i ON pia.invoice_id = i.id
            WHERE pia.payment_id = :payment_id";

    $database = new Database();
    $db = $database->getConnection();
    $stmt = $db->prepare($sql);
    $stmt->bindParam(':payment_id', $payment_id);
    $stmt->execute();
    $allocations = $stmt->fetchAll();
} catch (Exception $e) {
    // تجاهل الأخطاء
}

// أنواع وطرق وحالات المدفوعات
$payment_types = [
    'received' => 'سند قبض',
    'paid' => 'سند صرف'
];

$payment_methods = [
    'cash' => 'نقداً',
    'bank_transfer' => 'تحويل بنكي',
    'check' => 'شيك',
    'credit_card' => 'بطاقة ائتمان',
    'online' => 'دفع إلكتروني'
];

$payment_statuses = [
    'pending' => 'في الانتظار',
    'completed' => 'مكتمل',
    'cancelled' => 'ملغي',
    'bounced' => 'مرتد',
    'scheduled' => 'مجدول'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض المدفوع <?php echo htmlspecialchars($payment_data['payment_number']); ?> - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <style>
        .payment-header {
            background: linear-gradient(135deg,
                <?php echo $payment_data['payment_type'] == 'received' ? '#28a745, #20c997' : '#dc3545, #fd7e14'; ?>);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .info-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .status-badge {
            font-size: 1.1rem;
            padding: 0.5rem 1rem;
            border-radius: 25px;
        }

        .amount-display {
            font-size: 2rem;
            font-weight: bold;
            color: <?php echo $payment_data['payment_type'] == 'received' ? '#28a745' : '#dc3545'; ?>;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            .payment-header {
                background: #f8f9fa !important;
                color: #333 !important;
                border: 2px solid #dee2e6;
            }
        }
    </style>

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }
        
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <div class="container-fluid">
        <div class="row">
            <!-- المحتوى الرئيسي -->
            <div class="col-12 p-4">
                <!-- أزرار الإجراءات -->
                <div class="row mb-3 no-print">
                    <div class="col">
                        <a href="payments.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للمدفوعات
                        </a>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.print()">
                                <i class="fas fa-print me-2"></i>طباعة
                            </button>
                            <?php if ($payment_data['status'] != 'completed'): ?>
                                <a href="payment_edit.php?id=<?php echo $payment_id; ?>" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-edit me-2"></i>تعديل
                                </a>
                            <?php endif; ?>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="downloadPDF()">
                                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                            </button>
                        </div>
                    </div>
                </div>

                <!-- رأس المدفوع -->
                <div class="payment-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="mb-2">
                                <i class="fas fa-<?php echo $payment_data['payment_type'] == 'received' ? 'arrow-down' : 'arrow-up'; ?> me-3"></i>
                                <?php echo $payment_types[$payment_data['payment_type']]; ?>
                            </h1>
                            <h3 class="mb-0"><?php echo htmlspecialchars($payment_data['payment_number']); ?></h3>
                            <p class="mb-0 opacity-75">
                                تاريخ الإنشاء: <?php echo date('Y-m-d H:i', strtotime($payment_data['created_at'])); ?>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="amount-display">
                                <?php echo number_format($payment_data['amount'], 2) . ' ' . CURRENCY_SYMBOL; ?>
                            </div>
                            <span class="status-badge bg-<?php
                                echo $payment_data['status'] == 'completed' ? 'success' :
                                    ($payment_data['status'] == 'pending' ? 'warning' : 'danger');
                            ?>">
                                <?php echo $payment_statuses[$payment_data['status']]; ?>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- معلومات المدفوع -->
                    <div class="col-lg-6">
                        <div class="info-card">
                            <h5 class="mb-3">
                                <i class="fas fa-info-circle me-2"></i>معلومات المدفوع
                            </h5>

                            <div class="row mb-2">
                                <div class="col-4"><strong>رقم المدفوع:</strong></div>
                                <div class="col-8"><?php echo htmlspecialchars($payment_data['payment_number']); ?></div>
                            </div>

                            <div class="row mb-2">
                                <div class="col-4"><strong>النوع:</strong></div>
                                <div class="col-8">
                                    <span class="badge bg-<?php echo $payment_data['payment_type'] == 'received' ? 'success' : 'danger'; ?>">
                                        <?php echo $payment_types[$payment_data['payment_type']]; ?>
                                    </span>
                                </div>
                            </div>

                            <div class="row mb-2">
                                <div class="col-4"><strong>طريقة الدفع:</strong></div>
                                <div class="col-8"><?php echo $payment_methods[$payment_data['payment_method']]; ?></div>
                            </div>

                            <div class="row mb-2">
                                <div class="col-4"><strong>تاريخ المدفوع:</strong></div>
                                <div class="col-8"><?php echo date('Y-m-d', strtotime($payment_data['payment_date'])); ?></div>
                            </div>

                            <?php if ($payment_data['due_date']): ?>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>تاريخ الاستحقاق:</strong></div>
                                    <div class="col-8"><?php echo date('Y-m-d', strtotime($payment_data['due_date'])); ?></div>
                                </div>
                            <?php endif; ?>

                            <?php if ($payment_data['reference_number']): ?>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>رقم المرجع:</strong></div>
                                    <div class="col-8"><?php echo htmlspecialchars($payment_data['reference_number']); ?></div>
                                </div>
                            <?php endif; ?>

                            <?php if ($payment_data['check_number']): ?>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>رقم الشيك:</strong></div>
                                    <div class="col-8"><?php echo htmlspecialchars($payment_data['check_number']); ?></div>
                                </div>
                            <?php endif; ?>

                            <?php if ($payment_data['check_date']): ?>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>تاريخ الشيك:</strong></div>
                                    <div class="col-8"><?php echo date('Y-m-d', strtotime($payment_data['check_date'])); ?></div>
                                </div>
                            <?php endif; ?>

                            <div class="row mb-2">
                                <div class="col-4"><strong>الحالة:</strong></div>
                                <div class="col-8">
                                    <span class="badge bg-<?php
                                        echo $payment_data['status'] == 'completed' ? 'success' :
                                            ($payment_data['status'] == 'pending' ? 'warning' : 'danger');
                                    ?>">
                                        <?php echo $payment_statuses[$payment_data['status']]; ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات العميل/المورد -->
                    <div class="col-lg-6">
                        <div class="info-card">
                            <h5 class="mb-3">
                                <i class="fas fa-<?php echo $payment_data['payment_type'] == 'received' ? 'user' : 'truck'; ?> me-2"></i>
                                معلومات <?php echo $payment_data['payment_type'] == 'received' ? 'العميل' : 'المورد'; ?>
                            </h5>

                            <?php if ($payment_data['customer_name'] || $payment_data['supplier_name']): ?>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>الاسم:</strong></div>
                                    <div class="col-8">
                                        <?php echo htmlspecialchars($payment_data['customer_name'] ?? $payment_data['supplier_name']); ?>
                                    </div>
                                </div>
                            <?php else: ?>
                                <p class="text-muted">لا توجد معلومات <?php echo $payment_data['payment_type'] == 'received' ? 'عميل' : 'مورد'; ?> مرتبطة</p>
                            <?php endif; ?>

                            <?php if ($payment_data['bank_account_name']): ?>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>الحساب البنكي:</strong></div>
                                    <div class="col-8"><?php echo htmlspecialchars($payment_data['bank_account_name']); ?></div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- معلومات النظام -->
                        <div class="info-card">
                            <h5 class="mb-3">
                                <i class="fas fa-cog me-2"></i>معلومات النظام
                            </h5>

                            <div class="row mb-2">
                                <div class="col-4"><strong>أنشأ بواسطة:</strong></div>
                                <div class="col-8"><?php echo htmlspecialchars($payment_data['created_by_name']); ?></div>
                            </div>

                            <div class="row mb-2">
                                <div class="col-4"><strong>تاريخ الإنشاء:</strong></div>
                                <div class="col-8"><?php echo date('Y-m-d H:i', strtotime($payment_data['created_at'])); ?></div>
                            </div>

                            <?php if ($payment_data['approved_by_name']): ?>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>اعتمد بواسطة:</strong></div>
                                    <div class="col-8"><?php echo htmlspecialchars($payment_data['approved_by_name']); ?></div>
                                </div>
                            <?php endif; ?>

                            <?php if ($payment_data['approved_at']): ?>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>تاريخ الاعتماد:</strong></div>
                                    <div class="col-8"><?php echo date('Y-m-d H:i', strtotime($payment_data['approved_at'])); ?></div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- ربط الفواتير -->
                <?php if (!empty($allocations)): ?>
                    <div class="row">
                        <div class="col-12">
                            <div class="info-card">
                                <h5 class="mb-3">
                                    <i class="fas fa-link me-2"></i>الفواتير المرتبطة
                                </h5>

                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>رقم الفاتورة</th>
                                                <th>إجمالي الفاتورة</th>
                                                <th>المبلغ المخصص</th>
                                                <th>تاريخ التخصيص</th>
                                                <th>ملاحظات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($allocations as $allocation): ?>
                                                <tr>
                                                    <td><strong><?php echo htmlspecialchars($allocation['invoice_number']); ?></strong></td>
                                                    <td><?php echo number_format($allocation['invoice_total'], 2) . ' ' . CURRENCY_SYMBOL; ?></td>
                                                    <td class="text-success">
                                                        <strong><?php echo number_format($allocation['allocated_amount'], 2) . ' ' . CURRENCY_SYMBOL; ?></strong>
                                                    </td>
                                                    <td><?php echo date('Y-m-d H:i', strtotime($allocation['allocation_date'])); ?></td>
                                                    <td><?php echo htmlspecialchars($allocation['notes'] ?? '-'); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                        <tfoot class="table-secondary">
                                            <tr>
                                                <td colspan="2"><strong>إجمالي المخصص:</strong></td>
                                                <td><strong class="text-success">
                                                    <?php
                                                    $total_allocated = array_sum(array_column($allocations, 'allocated_amount'));
                                                    echo number_format($total_allocated, 2) . ' ' . CURRENCY_SYMBOL;
                                                    ?>
                                                </strong></td>
                                                <td colspan="2"></td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- الملاحظات -->
                <?php if ($payment_data['notes']): ?>
                    <div class="row">
                        <div class="col-12">
                            <div class="info-card">
                                <h5 class="mb-3">
                                    <i class="fas fa-sticky-note me-2"></i>الملاحظات
                                </h5>
                                <p class="mb-0"><?php echo nl2br(htmlspecialchars($payment_data['notes'])); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function downloadPDF() {
            alert('سيتم إضافة وظيفة تصدير PDF قريباً');
            // يمكن إضافة مكتبة jsPDF أو html2pdf هنا
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
