<?php
/**
 * SeaSystem - صفحة إنشاء مدفوع جديد
 * Create Payment Page
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/Payment.php';
require_once __DIR__ . '/classes/Customer.php';

// التأكد من تسجيل الدخول
requireLogin();

$payment = new Payment();
$customer = new Customer();
$current_user = getCurrentUser();

// نوع المدفوع من الرابط
$payment_type = $_GET['type'] ?? 'received';
if (!in_array($payment_type, ['received', 'paid'])) {
    $payment_type = 'received';
}

// معالجة إنشاء المدفوع
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_payment'])) {
    $data = [
        'payment_type' => $_POST['payment_type'],
        'payment_method' => $_POST['payment_method'],
        'customer_id' => !empty($_POST['customer_id']) ? $_POST['customer_id'] : null,
        'supplier_id' => !empty($_POST['supplier_id']) ? $_POST['supplier_id'] : null,
        'amount' => $_POST['amount'],
        'payment_date' => $_POST['payment_date'],
        'due_date' => !empty($_POST['due_date']) ? $_POST['due_date'] : null,
        'reference_number' => $_POST['reference_number'],
        'bank_account_id' => !empty($_POST['bank_account_id']) ? $_POST['bank_account_id'] : null,
        'check_number' => $_POST['check_number'],
        'check_date' => !empty($_POST['check_date']) ? $_POST['check_date'] : null,
        'status' => $_POST['status'],
        'notes' => $_POST['notes'],
        'created_by' => $current_user['id']
    ];

    // ربط الفواتير إذا تم تحديدها
    $invoice_allocations = [];
    if (!empty($_POST['invoice_ids'])) {
        foreach ($_POST['invoice_ids'] as $index => $invoice_id) {
            if (!empty($invoice_id) && !empty($_POST['allocation_amounts'][$index])) {
                $invoice_allocations[] = [
                    'invoice_id' => $invoice_id,
                    'amount' => $_POST['allocation_amounts'][$index]
                ];
            }
        }
    }

    if (!empty($invoice_allocations)) {
        $data['invoice_allocations'] = $invoice_allocations;
    }

    $result = $payment->create($data);
    $message = $result['message'];
    $message_type = $result['success'] ? 'success' : 'danger';

    if ($result['success']) {
        // إعادة توجيه إلى صفحة المدفوعات
        header('Location: payments.php?success=' . urlencode($message));
        exit();
    }
}

// الحصول على قائمة العملاء والموردين
$customers = $customer->getAll();

// محاولة الحصول على الموردين
$suppliers = [];
try {
    require_once __DIR__ . '/classes/Supplier.php';
    $supplier_class = new Supplier();
    $suppliers = $supplier_class->getAll();
} catch (Exception $e) {
    // إذا لم تكن فئة الموردين موجودة، استخدم قائمة فارغة
}

// طرق الدفع
$payment_methods = [
    'cash' => 'نقداً',
    'bank_transfer' => 'تحويل بنكي',
    'check' => 'شيك',
    'credit_card' => 'بطاقة ائتمان',
    'online' => 'دفع إلكتروني'
];

// حالات المدفوع
$payment_statuses = [
    'pending' => 'في الانتظار',
    'completed' => 'مكتمل',
    'scheduled' => 'مجدول'
];

// توليد رقم مدفوع تلقائي
$payment_number = $payment->generatePaymentNumber($payment_type);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء <?php echo $payment_type == 'received' ? 'سند قبض' : 'سند صرف'; ?> - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <style>
        .payment-form-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .invoice-allocation-table {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
        }

        .add-invoice-btn {
            background: #e9ecef;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 0.75rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .add-invoice-btn:hover {
            background: #dee2e6;
            border-color: #667eea;
            color: #667eea;
        }

        .payment-type-header {
            background: linear-gradient(135deg,
                <?php echo $payment_type == 'received' ? '#28a745, #20c997' : '#dc3545, #fd7e14'; ?>);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
        }
    </style>

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }
        
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <div class="container-fluid">
        <div class="row">
            <!-- المحتوى الرئيسي -->
            <div class="col-12 p-4">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="page-title">
                                <i class="fas fa-<?php echo $payment_type == 'received' ? 'arrow-down' : 'arrow-up'; ?> me-2"></i>
                                إنشاء <?php echo $payment_type == 'received' ? 'سند قبض' : 'سند صرف'; ?> جديد
                            </h1>
                            <p class="page-subtitle">تسجيل <?php echo $payment_type == 'received' ? 'سند قبض من العملاء' : 'سند صرف للموردين'; ?></p>
                        </div>
                        <div class="col-auto">
                            <a href="payments.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-2"></i>العودة للمدفوعات
                            </a>
                        </div>
                    </div>
                </div>

                <!-- الرسائل -->
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- نموذج إنشاء المدفوع -->
                <div class="row justify-content-center">
                    <div class="col-lg-10">
                        <div class="payment-form-card">
                            <!-- رأس النموذج -->
                            <div class="payment-type-header">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h3 class="mb-0">
                                            <i class="fas fa-<?php echo $payment_type == 'received' ? 'arrow-down' : 'arrow-up'; ?> me-2"></i>
                                            <?php echo $payment_type == 'received' ? 'سند قبض من العملاء' : 'سند صرف للموردين'; ?>
                                        </h3>
                                        <p class="mb-0 opacity-75">رقم المدفوع: <?php echo $payment_number; ?></p>
                                    </div>
                                    <div class="col-auto">
                                        <div class="d-flex gap-2">
                                            <a href="payment_create.php?type=received"
                                               class="btn btn-sm <?php echo $payment_type == 'received' ? 'btn-outline-light' : 'btn-outline-light'; ?>">
                                                <i class="fas fa-arrow-down me-1"></i>سند قبض
                                            </a>
                                            <a href="payment_create.php?type=paid"
                                               class="btn btn-sm <?php echo $payment_type == 'paid' ? 'btn-outline-light' : 'btn-outline-light'; ?>">
                                                <i class="fas fa-arrow-up me-1"></i>سند صرف
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- محتوى النموذج -->
                            <div class="card-body p-4">
                                <form method="POST" id="paymentForm">
                                    <input type="hidden" name="payment_type" value="<?php echo $payment_type; ?>">

                                    <div class="row">
                                        <!-- البيانات الأساسية -->
                                        <div class="col-lg-6 mb-4">
                                            <h5 class="mb-3">
                                                <i class="fas fa-info-circle me-2"></i>البيانات الأساسية
                                            </h5>

                                            <div class="mb-3">
                                                <label for="payment_date" class="form-label">تاريخ المدفوع *</label>
                                                <input type="date" class="form-control" id="payment_date" name="payment_date"
                                                       value="<?php echo date('Y-m-d'); ?>" required>
                                            </div>

                                            <div class="mb-3">
                                                <label for="amount" class="form-label">المبلغ *</label>
                                                <div class="input-group">
                                                    <input type="number" class="form-control" id="amount" name="amount"
                                                           step="0.01" min="0" required placeholder="0.00">
                                                    <span class="input-group-text"><?php echo CURRENCY_SYMBOL; ?></span>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <label for="<?php echo $payment_type == 'received' ? 'customer_id' : 'supplier_id'; ?>" class="form-label">
                                                    <?php echo $payment_type == 'received' ? 'العميل' : 'المورد'; ?> *
                                                </label>
                                                <select class="form-select" id="<?php echo $payment_type == 'received' ? 'customer_id' : 'supplier_id'; ?>"
                                                        name="<?php echo $payment_type == 'received' ? 'customer_id' : 'supplier_id'; ?>" required>
                                                    <option value="">اختر <?php echo $payment_type == 'received' ? 'العميل' : 'المورد'; ?></option>
                                                    <?php
                                                    $entities = $payment_type == 'received' ? $customers : $suppliers;
                                                    foreach ($entities as $entity):
                                                    ?>
                                                        <option value="<?php echo $entity['id']; ?>">
                                                            <?php echo htmlspecialchars($entity['name']); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>

                                            <div class="mb-3">
                                                <label for="payment_method" class="form-label">طريقة الدفع *</label>
                                                <select class="form-select" id="payment_method" name="payment_method" required>
                                                    <?php foreach ($payment_methods as $method => $method_name): ?>
                                                        <option value="<?php echo $method; ?>"><?php echo $method_name; ?></option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>

                                            <div class="mb-3">
                                                <label for="status" class="form-label">حالة المدفوع</label>
                                                <select class="form-select" id="status" name="status">
                                                    <?php foreach ($payment_statuses as $status => $status_name): ?>
                                                        <option value="<?php echo $status; ?>" <?php echo $status == 'completed' ? 'selected' : ''; ?>>
                                                            <?php echo $status_name; ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- التفاصيل الإضافية -->
                                        <div class="col-lg-6 mb-4">
                                            <h5 class="mb-3">
                                                <i class="fas fa-cog me-2"></i>التفاصيل الإضافية
                                            </h5>

                                            <div class="mb-3">
                                                <label for="reference_number" class="form-label">رقم المرجع</label>
                                                <input type="text" class="form-control" id="reference_number" name="reference_number"
                                                       placeholder="رقم الشيك، التحويل، إلخ">
                                            </div>

                                            <div class="mb-3" id="check_fields" style="display: none;">
                                                <label for="check_number" class="form-label">رقم الشيك</label>
                                                <input type="text" class="form-control" id="check_number" name="check_number">
                                            </div>

                                            <div class="mb-3" id="check_date_field" style="display: none;">
                                                <label for="check_date" class="form-label">تاريخ الشيك</label>
                                                <input type="date" class="form-control" id="check_date" name="check_date">
                                            </div>

                                            <div class="mb-3" id="due_date_field" style="display: none;">
                                                <label for="due_date" class="form-label">تاريخ الاستحقاق</label>
                                                <input type="date" class="form-control" id="due_date" name="due_date">
                                            </div>

                                            <div class="mb-3">
                                                <label for="notes" class="form-label">ملاحظات</label>
                                                <textarea class="form-control" id="notes" name="notes" rows="3"
                                                          placeholder="ملاحظات إضافية"></textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- ربط الفواتير -->
                                    <div class="row">
                                        <div class="col-12">
                                            <h5 class="mb-3">
                                                <i class="fas fa-link me-2"></i>ربط بالفواتير (اختياري)
                                            </h5>

                                            <div class="invoice-allocation-table">
                                                <div id="invoiceAllocations">
                                                    <!-- سيتم إضافة الفواتير هنا بواسطة JavaScript -->
                                                </div>

                                                <div class="add-invoice-btn" onclick="addInvoiceAllocation()">
                                                    <i class="fas fa-plus me-2"></i>إضافة فاتورة
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- أزرار الحفظ -->
                                    <div class="row mt-4">
                                        <div class="col-12 text-center">
                                            <div class="d-flex gap-2 justify-content-center">
                                                <button type="submit" name="create_payment" class="btn btn-outline-<?php echo $payment_type == 'received' ? 'success' : 'danger'; ?> btn-sm">
                                                    <i class="fas fa-save me-2"></i>حفظ المدفوع
                                                </button>
                                                <a href="payments.php" class="btn btn-outline-secondary btn-sm">
                                                    <i class="fas fa-times me-2"></i>إلغاء
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        let invoiceCounter = 0;

        // إظهار/إخفاء حقول حسب طريقة الدفع
        document.getElementById('payment_method').addEventListener('change', function() {
            const method = this.value;
            const checkFields = document.getElementById('check_fields');
            const checkDateField = document.getElementById('check_date_field');
            const dueDateField = document.getElementById('due_date_field');

            // إخفاء جميع الحقول أولاً
            checkFields.style.display = 'none';
            checkDateField.style.display = 'none';
            dueDateField.style.display = 'none';

            // إظهار الحقول المناسبة
            if (method === 'check') {
                checkFields.style.display = 'block';
                checkDateField.style.display = 'block';
                dueDateField.style.display = 'block';
            }
        });

        // إظهار/إخفاء تاريخ الاستحقاق حسب الحالة
        document.getElementById('status').addEventListener('change', function() {
            const status = this.value;
            const dueDateField = document.getElementById('due_date_field');

            if (status === 'scheduled') {
                dueDateField.style.display = 'block';
            } else if (document.getElementById('payment_method').value !== 'check') {
                dueDateField.style.display = 'none';
            }
        });

        // إضافة ربط فاتورة
        function addInvoiceAllocation() {
            invoiceCounter++;
            const container = document.getElementById('invoiceAllocations');
            const div = document.createElement('div');
            div.className = 'row mb-2 align-items-center';
            div.id = `invoice_${invoiceCounter}`;

            div.innerHTML = `
                <div class="col-md-6">
                    <select class="form-select" name="invoice_ids[]">
                        <option value="">اختر الفاتورة</option>
                        <!-- سيتم تحميل الفواتير بواسطة AJAX -->
                    </select>
                </div>
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="number" class="form-control" name="allocation_amounts[]"
                               step="0.01" min="0" placeholder="المبلغ">
                        <span class="input-group-text"><?php echo CURRENCY_SYMBOL; ?></span>
                    </div>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-outline-danger btn-sm w-100"
                            onclick="removeInvoiceAllocation('invoice_${invoiceCounter}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

            container.appendChild(div);
        }

        // حذف ربط فاتورة
        function removeInvoiceAllocation(id) {
            const element = document.getElementById(id);
            if (element) {
                element.remove();
            }
        }

        // تحديث المبلغ الإجمالي عند تغيير المبالغ المخصصة
        document.addEventListener('input', function(e) {
            if (e.target.name === 'allocation_amounts[]') {
                updateTotalAllocation();
            }
        });

        function updateTotalAllocation() {
            const amounts = document.querySelectorAll('input[name="allocation_amounts[]"]');
            let total = 0;

            amounts.forEach(input => {
                total += parseFloat(input.value) || 0;
            });

            const paymentAmount = parseFloat(document.getElementById('amount').value) || 0;

            if (total > paymentAmount) {
                alert('إجمالي المبالغ المخصصة أكبر من مبلغ المدفوع');
            }
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
