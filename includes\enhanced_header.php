<?php
/**
 * SeaSystem - الهيدر المحسن والثابت
 * Enhanced Fixed Header
 */

if (!isset($_SESSION)) {
    session_start();
}

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: login.php');
    exit();
}

$current_user = getCurrentUser();
?>

<!-- الهيدر المحسن والثابت -->
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container-fluid">
        <!-- العلامة التجارية -->
        <a class="navbar-brand d-flex align-items-center" href="dashboard.php">
            <i class="fas fa-anchor me-2"></i>
            <span class="brand-text"><?php echo SITE_NAME; ?></span>
            <small class="version-badge ms-2 badge bg-light text-dark">v<?php echo SITE_VERSION; ?></small>
        </a>
        
        <!-- زر التبديل للموبايل -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="تبديل التنقل">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <!-- قائمة التنقل -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <!-- القائمة الرئيسية -->
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-1"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </li>
                
                <!-- قائمة التقارير -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-chart-line me-1"></i>
                        <span>التقارير</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="reports.php">
                            <i class="fas fa-chart-bar me-2"></i>التقارير الرئيسية
                        </a></li>
                        <li><a class="dropdown-item" href="report_trial_balance.php">
                            <i class="fas fa-balance-scale me-2"></i>ميزان المراجعة
                        </a></li>
                        <li><a class="dropdown-item" href="report_income_statement.php">
                            <i class="fas fa-chart-pie me-2"></i>قائمة الدخل
                        </a></li>
                        <li><a class="dropdown-item" href="report_balance_sheet.php">
                            <i class="fas fa-file-alt me-2"></i>الميزانية العمومية
                        </a></li>
                    </ul>
                </li>
                
                <!-- الوصول السريع -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-bolt me-1"></i>
                        <span>وصول سريع</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="customers.php">
                            <i class="fas fa-users me-2"></i>العملاء
                        </a></li>
                        <li><a class="dropdown-item" href="suppliers.php">
                            <i class="fas fa-truck me-2"></i>الموردين
                        </a></li>
                        <li><a class="dropdown-item" href="invoices.php">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a></li>
                        <li><a class="dropdown-item" href="inventory.php">
                            <i class="fas fa-boxes me-2"></i>المخزون
                        </a></li>
                    </ul>
                </li>
            </ul>
            
            <!-- أدوات الهيدر -->
            <ul class="navbar-nav">
                <!-- البحث السريع -->
                <li class="nav-item me-2">
                    <form class="d-flex" role="search">
                        <div class="input-group input-group-sm">
                            <input class="form-control search-input" type="search" placeholder="بحث سريع..." aria-label="البحث">
                            <button class="btn btn-outline-light" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </li>
                
                <!-- الإشعارات -->
                <li class="nav-item dropdown me-2">
                    <a class="nav-link position-relative notification-badge" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-bell"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            3
                            <span class="visually-hidden">إشعارات غير مقروءة</span>
                        </span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                        <li><h6 class="dropdown-header">الإشعارات الحديثة</h6></li>
                        <li><a class="dropdown-item" href="#">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-info-circle text-info"></i>
                                </div>
                                <div class="flex-grow-1 ms-2">
                                    <h6 class="mb-1">فاتورة جديدة</h6>
                                    <p class="mb-1 small">تم إنشاء فاتورة رقم #1001</p>
                                    <small class="text-muted">منذ 5 دقائق</small>
                                </div>
                            </div>
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-center" href="#">عرض جميع الإشعارات</a></li>
                    </ul>
                </li>
                
                <!-- الوقت والتاريخ -->
                <li class="nav-item me-3">
                    <span class="navbar-text time-display">
                        <i class="fas fa-clock me-1"></i>
                        <span id="current-time"><?php echo date('H:i'); ?></span>
                        <br>
                        <small id="current-date"><?php echo date('Y-m-d'); ?></small>
                    </span>
                </li>
                
                <!-- قائمة المستخدم -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <div class="user-avatar me-2">
                            <i class="fas fa-user-circle fa-lg"></i>
                        </div>
                        <div class="user-info d-none d-md-block">
                            <div class="user-name"><?php echo htmlspecialchars($current_user['full_name'] ?? $current_user['username']); ?></div>
                            <small class="user-role text-light opacity-75"><?php echo htmlspecialchars($current_user['role']); ?></small>
                        </div>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end user-dropdown">
                        <li><div class="dropdown-header">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-user-circle fa-2x me-2"></i>
                                <div>
                                    <h6 class="mb-0"><?php echo htmlspecialchars($current_user['full_name']); ?></h6>
                                    <small class="text-muted"><?php echo htmlspecialchars($current_user['email']); ?></small>
                                </div>
                            </div>
                        </div></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#profileModal">
                            <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                            <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                        </a></li>
                        <li><a class="dropdown-item" href="settings.php">
                            <i class="fas fa-cog me-2"></i>الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- أزرار التحكم السريع -->
<div class="quick-controls">
    <button class="btn btn-primary btn-sm" onclick="HeaderUtils.scrollToTop()" title="العودة للأعلى">
        <i class="fas fa-arrow-up"></i>
    </button>
    <button class="btn btn-secondary btn-sm" onclick="HeaderUtils.toggleHeader()" title="إخفاء/إظهار الهيدر">
        <i class="fas fa-eye-slash"></i>
    </button>
</div>

<!-- نماذج منبثقة -->
<!-- نموذج الملف الشخصي -->
<div class="modal fade" id="profileModal" tabindex="-1" aria-labelledby="profileModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="profileModalLabel">الملف الشخصي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($current_user['username']); ?></p>
                        <p><strong>الاسم الكامل:</strong> <?php echo htmlspecialchars($current_user['full_name']); ?></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($current_user['email']); ?></p>
                        <p><strong>الدور:</strong> <?php echo htmlspecialchars($current_user['role']); ?></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary">تحديث البيانات</button>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تغيير كلمة المرور -->
<div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changePasswordModalLabel">تغيير كلمة المرور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    <div class="mb-3">
                        <label for="currentPassword" class="form-label">كلمة المرور الحالية</label>
                        <input type="password" class="form-control" id="currentPassword" required>
                    </div>
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">كلمة المرور الجديدة</label>
                        <input type="password" class="form-control" id="newPassword" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">تأكيد كلمة المرور</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>

<style>
/* تنسيقات إضافية للهيدر المحسن */
.search-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    width: 200px;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-input:focus {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    color: white;
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

.time-display {
    font-size: 0.85rem;
    text-align: center;
    line-height: 1.2;
}

.user-avatar {
    font-size: 1.5rem;
}

.user-info {
    text-align: right;
    line-height: 1.2;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.75rem;
}

.notification-dropdown {
    width: 350px;
    max-height: 400px;
    overflow-y: auto;
}

.user-dropdown {
    width: 280px;
}

.version-badge {
    font-size: 0.6rem;
    padding: 0.2rem 0.4rem;
}

.quick-controls {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1025;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.quick-controls .btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.quick-controls .btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

@media (max-width: 991.98px) {
    .search-input {
        width: 150px;
    }
    
    .time-display {
        display: none;
    }
    
    .quick-controls {
        bottom: 10px;
        right: 10px;
    }
}
</style>

<script>
// تحديث الوقت والتاريخ
function updateDateTime() {
    const now = new Date();
    const timeElement = document.getElementById('current-time');
    const dateElement = document.getElementById('current-date');
    
    if (timeElement) {
        timeElement.textContent = now.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    if (dateElement) {
        dateElement.textContent = now.toLocaleDateString('ar-SA');
    }
}

// تحديث كل دقيقة
setInterval(updateDateTime, 60000);
updateDateTime(); // تحديث فوري
</script>
