<?php
/**
 * ملف لإنشاء فواتير البيع
 */

require_once __DIR__ . '/config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>إنشاء فواتير البيع</h2>";
    
    // إنشاء فاتورة بيع 1: لأحمد محمد علي
    $invoice_data_1 = [
        'invoice_number' => 'SAL001',
        'invoice_type' => 'sale',
        'customer_id' => 1, // أحمد محمد علي
        'invoice_date' => date('Y-m-d'),
        'due_date' => date('Y-m-d', strtotime('+15 days')),
        'subtotal' => 24500.00,
        'tax_amount' => 3430.00,
        'total_amount' => 27930.00,
        'notes' => 'فاتورة بيع لأحمد محمد علي',
        'items' => [
            [
                'product_id' => 1, // لابتوب ديل
                'quantity' => 2,
                'unit_price' => 12000.00,
                'total_price' => 24000.00
            ],
            [
                'product_id' => 3, // ماوس لاسلكي
                'quantity' => 2,
                'unit_price' => 250.00,
                'total_price' => 500.00
            ]
        ]
    ];
    
    // إدراج فاتورة البيع الأولى
    $sql = "INSERT INTO invoices (invoice_number, invoice_type, customer_id, invoice_date, due_date, subtotal, tax_amount, total_amount, notes, status) 
            VALUES (:invoice_number, :invoice_type, :customer_id, :invoice_date, :due_date, :subtotal, :tax_amount, :total_amount, :notes, 'pending')";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([
        ':invoice_number' => $invoice_data_1['invoice_number'],
        ':invoice_type' => $invoice_data_1['invoice_type'],
        ':customer_id' => $invoice_data_1['customer_id'],
        ':invoice_date' => $invoice_data_1['invoice_date'],
        ':due_date' => $invoice_data_1['due_date'],
        ':subtotal' => $invoice_data_1['subtotal'],
        ':tax_amount' => $invoice_data_1['tax_amount'],
        ':total_amount' => $invoice_data_1['total_amount'],
        ':notes' => $invoice_data_1['notes']
    ]);
    
    $invoice_id_1 = $db->lastInsertId();
    
    // إدراج عناصر فاتورة البيع الأولى
    foreach ($invoice_data_1['items'] as $item) {
        $sql = "INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, total_price) 
                VALUES (:invoice_id, :product_id, :quantity, :unit_price, :total_price)";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            ':invoice_id' => $invoice_id_1,
            ':product_id' => $item['product_id'],
            ':quantity' => $item['quantity'],
            ':unit_price' => $item['unit_price'],
            ':total_price' => $item['total_price']
        ]);
        
        // تحديث المخزون (خصم الكمية المباعة)
        $sql = "UPDATE products SET current_stock = current_stock - :quantity WHERE id = :product_id";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            ':quantity' => $item['quantity'],
            ':product_id' => $item['product_id']
        ]);
    }
    
    echo "تم إنشاء فاتورة البيع الأولى: SAL001<br>";
    
    // إنشاء فاتورة بيع 2: لفاطمة حسن محمود
    $invoice_data_2 = [
        'invoice_number' => 'SAL002',
        'invoice_type' => 'sale',
        'customer_id' => 2, // فاطمة حسن محمود
        'invoice_date' => date('Y-m-d'),
        'due_date' => date('Y-m-d', strtotime('+15 days')),
        'subtotal' => 4200.00,
        'tax_amount' => 588.00,
        'total_amount' => 4788.00,
        'notes' => 'فاتورة بيع لفاطمة حسن محمود',
        'items' => [
            [
                'product_id' => 2, // طابعة HP
                'quantity' => 1,
                'unit_price' => 3000.00,
                'total_price' => 3000.00
            ],
            [
                'product_id' => 4, // كيبورد ميكانيكي
                'quantity' => 1,
                'unit_price' => 1200.00,
                'total_price' => 1200.00
            ]
        ]
    ];
    
    // إدراج فاتورة البيع الثانية
    $sql = "INSERT INTO invoices (invoice_number, invoice_type, customer_id, invoice_date, due_date, subtotal, tax_amount, total_amount, notes, status) 
            VALUES (:invoice_number, :invoice_type, :customer_id, :invoice_date, :due_date, :subtotal, :tax_amount, :total_amount, :notes, 'pending')";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([
        ':invoice_number' => $invoice_data_2['invoice_number'],
        ':invoice_type' => $invoice_data_2['invoice_type'],
        ':customer_id' => $invoice_data_2['customer_id'],
        ':invoice_date' => $invoice_data_2['invoice_date'],
        ':due_date' => $invoice_data_2['due_date'],
        ':subtotal' => $invoice_data_2['subtotal'],
        ':tax_amount' => $invoice_data_2['tax_amount'],
        ':total_amount' => $invoice_data_2['total_amount'],
        ':notes' => $invoice_data_2['notes']
    ]);
    
    $invoice_id_2 = $db->lastInsertId();
    
    // إدراج عناصر فاتورة البيع الثانية
    foreach ($invoice_data_2['items'] as $item) {
        $sql = "INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, total_price) 
                VALUES (:invoice_id, :product_id, :quantity, :unit_price, :total_price)";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            ':invoice_id' => $invoice_id_2,
            ':product_id' => $item['product_id'],
            ':quantity' => $item['quantity'],
            ':unit_price' => $item['unit_price'],
            ':total_price' => $item['total_price']
        ]);
        
        // تحديث المخزون (خصم الكمية المباعة)
        $sql = "UPDATE products SET current_stock = current_stock - :quantity WHERE id = :product_id";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            ':quantity' => $item['quantity'],
            ':product_id' => $item['product_id']
        ]);
    }
    
    echo "تم إنشاء فاتورة البيع الثانية: SAL002<br>";
    
    echo "<br><strong>ملخص فواتير البيع:</strong><br>";
    echo "- فاتورة SAL001: 27,930 ج.م لأحمد محمد علي<br>";
    echo "- فاتورة SAL002: 4,788 ج.م لفاطمة حسن محمود<br>";
    echo "- إجمالي المبيعات: 32,718 ج.م<br>";
    
    echo '<br><a href="invoices.php">انتقل لصفحة الفواتير</a>';
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
