<?php
/**
 * SeaSystem - إصلاح عمود الرصيد الافتتاحي
 * Fix Opening Balance Column
 */

require_once __DIR__ . '/config/database.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح عمود الرصيد الافتتاحي - SeaSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .fix-step {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        .step-success { border-color: #198754; background: #d1e7dd; }
        .step-warning { border-color: #ffc107; background: #fff3cd; }
        .step-danger { border-color: #dc3545; background: #f8d7da; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-tools text-primary me-2"></i>إصلاح عمود الرصيد الافتتاحي
                </h1>
                
                <?php
                try {
                    $database = new Database();
                    $db = $database->getConnection();
                    
                    echo '<div class="fix-step step-warning">';
                    echo '<h4><i class="fas fa-search me-2"></i>فحص هيكل جدول العملاء...</h4>';
                    
                    // فحص الأعمدة الموجودة
                    $sql = "DESCRIBE customers";
                    $stmt = $db->prepare($sql);
                    $stmt->execute();
                    $columns = $stmt->fetchAll();
                    
                    $existing_columns = [];
                    foreach ($columns as $column) {
                        $existing_columns[] = $column['Field'];
                    }
                    
                    echo '<p>الأعمدة الموجودة: ' . implode(', ', $existing_columns) . '</p>';
                    echo '</div>';
                    
                    // التحقق من وجود عمود opening_balance
                    if (!in_array('opening_balance', $existing_columns)) {
                        echo '<div class="fix-step step-warning">';
                        echo '<h4><i class="fas fa-plus me-2"></i>إضافة عمود opening_balance...</h4>';
                        
                        $sql = "ALTER TABLE customers ADD COLUMN opening_balance DECIMAL(15,2) DEFAULT 0.00 AFTER credit_limit";
                        $db->exec($sql);
                        
                        echo '<p class="text-success">✅ تم إضافة عمود opening_balance بنجاح</p>';
                        echo '</div>';
                    } else {
                        echo '<div class="fix-step step-success">';
                        echo '<h4><i class="fas fa-check me-2"></i>عمود opening_balance موجود</h4>';
                        echo '<p>العمود موجود مسبقاً، لا حاجة للإضافة</p>';
                        echo '</div>';
                    }
                    
                    // التحقق من وجود عمود opening_balance في جدول الموردين أيضاً
                    echo '<div class="fix-step step-warning">';
                    echo '<h4><i class="fas fa-search me-2"></i>فحص جدول الموردين...</h4>';
                    
                    $sql = "DESCRIBE suppliers";
                    $stmt = $db->prepare($sql);
                    $stmt->execute();
                    $supplier_columns = $stmt->fetchAll();
                    
                    $supplier_existing_columns = [];
                    foreach ($supplier_columns as $column) {
                        $supplier_existing_columns[] = $column['Field'];
                    }
                    
                    if (!in_array('opening_balance', $supplier_existing_columns)) {
                        echo '<p class="text-warning">⚠️ عمود opening_balance مفقود في جدول الموردين</p>';
                        
                        $sql = "ALTER TABLE suppliers ADD COLUMN opening_balance DECIMAL(15,2) DEFAULT 0.00 AFTER tax_number";
                        $db->exec($sql);
                        
                        echo '<p class="text-success">✅ تم إضافة عمود opening_balance لجدول الموردين</p>';
                    } else {
                        echo '<p class="text-success">✅ عمود opening_balance موجود في جدول الموردين</p>';
                    }
                    echo '</div>';
                    
                    // تحديث البيانات الموجودة
                    echo '<div class="fix-step step-warning">';
                    echo '<h4><i class="fas fa-sync me-2"></i>تحديث البيانات الموجودة...</h4>';
                    
                    // تحديث العملاء الذين لديهم opening_balance = 0
                    $sql = "UPDATE customers SET opening_balance = current_balance WHERE opening_balance = 0 AND current_balance > 0";
                    $stmt = $db->prepare($sql);
                    $stmt->execute();
                    $updated_customers = $stmt->rowCount();
                    
                    echo '<p class="text-success">✅ تم تحديث ' . $updated_customers . ' عميل</p>';
                    
                    // تحديث الموردين الذين لديهم opening_balance = 0
                    $sql = "UPDATE suppliers SET opening_balance = current_balance WHERE opening_balance = 0 AND current_balance > 0";
                    $stmt = $db->prepare($sql);
                    $stmt->execute();
                    $updated_suppliers = $stmt->rowCount();
                    
                    echo '<p class="text-success">✅ تم تحديث ' . $updated_suppliers . ' مورد</p>';
                    echo '</div>';
                    
                    // عرض الهيكل النهائي
                    echo '<div class="fix-step step-success">';
                    echo '<h4><i class="fas fa-table me-2"></i>الهيكل النهائي لجدول العملاء:</h4>';
                    
                    $sql = "DESCRIBE customers";
                    $stmt = $db->prepare($sql);
                    $stmt->execute();
                    $final_columns = $stmt->fetchAll();
                    
                    echo '<table class="table table-bordered table-sm">';
                    echo '<thead class="table-light">';
                    echo '<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>';
                    echo '</thead>';
                    echo '<tbody>';
                    foreach ($final_columns as $column) {
                        echo '<tr>';
                        echo '<td><strong>' . $column['Field'] . '</strong></td>';
                        echo '<td>' . $column['Type'] . '</td>';
                        echo '<td>' . $column['Null'] . '</td>';
                        echo '<td>' . $column['Key'] . '</td>';
                        echo '<td>' . ($column['Default'] ?? 'NULL') . '</td>';
                        echo '</tr>';
                    }
                    echo '</tbody>';
                    echo '</table>';
                    echo '</div>';
                    
                    // اختبار الاستعلام
                    echo '<div class="fix-step step-success">';
                    echo '<h4><i class="fas fa-vial me-2"></i>اختبار الاستعلام:</h4>';
                    
                    try {
                        $sql = "SELECT id, customer_code, name, opening_balance, current_balance FROM customers LIMIT 5";
                        $stmt = $db->prepare($sql);
                        $stmt->execute();
                        $test_customers = $stmt->fetchAll();
                        
                        if (!empty($test_customers)) {
                            echo '<table class="table table-bordered table-sm">';
                            echo '<thead class="table-light">';
                            echo '<tr><th>ID</th><th>رمز العميل</th><th>الاسم</th><th>الرصيد الافتتاحي</th><th>الرصيد الحالي</th></tr>';
                            echo '</thead>';
                            echo '<tbody>';
                            foreach ($test_customers as $customer) {
                                echo '<tr>';
                                echo '<td>' . $customer['id'] . '</td>';
                                echo '<td>' . $customer['customer_code'] . '</td>';
                                echo '<td>' . $customer['name'] . '</td>';
                                echo '<td>' . number_format($customer['opening_balance'], 2) . '</td>';
                                echo '<td>' . number_format($customer['current_balance'], 2) . '</td>';
                                echo '</tr>';
                            }
                            echo '</tbody>';
                            echo '</table>';
                            
                            echo '<p class="text-success"><strong>✅ الاستعلام يعمل بنجاح!</strong></p>';
                        } else {
                            echo '<p class="text-info">ℹ️ لا توجد بيانات عملاء للاختبار</p>';
                        }
                        
                    } catch (Exception $e) {
                        echo '<p class="text-danger">❌ خطأ في الاستعلام: ' . $e->getMessage() . '</p>';
                    }
                    
                    echo '</div>';
                    
                    // النتيجة النهائية
                    echo '<div class="fix-step step-success">';
                    echo '<h3><i class="fas fa-check-circle me-2"></i>تم الإصلاح بنجاح!</h3>';
                    echo '<ul>';
                    echo '<li>✅ تم إضافة عمود opening_balance لجدول العملاء</li>';
                    echo '<li>✅ تم إضافة عمود opening_balance لجدول الموردين</li>';
                    echo '<li>✅ تم تحديث البيانات الموجودة</li>';
                    echo '<li>✅ تم اختبار الاستعلامات</li>';
                    echo '</ul>';
                    
                    echo '<div class="alert alert-success mt-3">';
                    echo '<h5><i class="fas fa-lightbulb me-2"></i>الآن يمكنك:</h5>';
                    echo '<ul class="mb-0">';
                    echo '<li>الذهاب إلى صفحة العملاء والتأكد من عملها</li>';
                    echo '<li>إضافة عملاء جدد مع رصيد افتتاحي</li>';
                    echo '<li>تعديل الأرصدة الافتتاحية للعملاء الموجودين</li>';
                    echo '</ul>';
                    echo '</div>';
                    echo '</div>';
                    
                } catch (Exception $e) {
                    echo '<div class="fix-step step-danger">';
                    echo '<h4><i class="fas fa-exclamation-triangle me-2"></i>خطأ في الإصلاح</h4>';
                    echo '<p class="text-danger">❌ ' . $e->getMessage() . '</p>';
                    echo '</div>';
                }
                ?>
                
                <div class="text-center mt-4">
                    <a href="customers.php" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-users me-2"></i>اختبار صفحة العملاء
                    </a>
                    <a href="dashboard.php" class="btn btn-secondary btn-lg">
                        <i class="fas fa-home me-2"></i>العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
