<?php
/**
 * SeaSystem - اختبار نظام حجز الأرقام
 * Test Number Reservation System
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/classes/NumberGenerator.php';

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$numberGenerator = new NumberGenerator();
$message = '';
$messageType = '';

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    $type = $_POST['type'] ?? '';
    
    switch ($action) {
        case 'reserve':
            try {
                $number = '';
                switch ($type) {
                    case 'customer':
                        $number = $numberGenerator->reserveNumber('customer', 'CUS', 3);
                        break;
                    case 'supplier':
                        $number = $numberGenerator->reserveNumber('supplier', 'SUP', 3);
                        break;
                    case 'product':
                        $number = $numberGenerator->reserveNumber('product', 'PRD', 3);
                        break;
                }
                $message = "تم حجز الرقم: $number";
                $messageType = 'success';
            } catch (Exception $e) {
                $message = "خطأ في الحجز: " . $e->getMessage();
                $messageType = 'danger';
            }
            break;
            
        case 'confirm':
            try {
                $number = $numberGenerator->confirmReservedNumber($type);
                $message = "تم تأكيد الرقم: $number";
                $messageType = 'success';
            } catch (Exception $e) {
                $message = "خطأ في التأكيد: " . $e->getMessage();
                $messageType = 'danger';
            }
            break;
            
        case 'cancel':
            $cancelled = $numberGenerator->cancelReservedNumber($type);
            if ($cancelled) {
                $message = "تم إلغاء حجز الرقم";
                $messageType = 'warning';
            } else {
                $message = "لا يوجد رقم محجوز لإلغائه";
                $messageType = 'info';
            }
            break;
            
        case 'cleanup':
            $numberGenerator->cleanupExpiredReservations();
            $message = "تم تنظيف الحجوزات المنتهية الصلاحية";
            $messageType = 'info';
            break;
    }
}

// الحصول على الحجوزات الحالية
$reservations = $_SESSION['reserved_numbers'] ?? [];

// الحصول على إحصائيات الترقيم
$stats = $numberGenerator->getNumberingStats();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام حجز الأرقام - SeaSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-cogs text-primary me-2"></i>اختبار نظام حجز الأرقام
                </h1>
                
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                        <i class="fas fa-info-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- أزرار الاختبار -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-users me-2"></i>العملاء
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" class="mb-2">
                                    <input type="hidden" name="action" value="reserve">
                                    <input type="hidden" name="type" value="customer">
                                    <button type="submit" class="btn btn-outline-primary btn-sm w-100">
                                        <i class="fas fa-bookmark me-1"></i>حجز رقم عميل
                                    </button>
                                </form>
                                
                                <form method="POST" class="mb-2">
                                    <input type="hidden" name="action" value="confirm">
                                    <input type="hidden" name="type" value="customer">
                                    <button type="submit" class="btn btn-outline-success btn-sm w-100">
                                        <i class="fas fa-check me-1"></i>تأكيد الحجز
                                    </button>
                                </form>
                                
                                <form method="POST">
                                    <input type="hidden" name="action" value="cancel">
                                    <input type="hidden" name="type" value="customer">
                                    <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                        <i class="fas fa-times me-1"></i>إلغاء الحجز
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-truck me-2"></i>الموردين
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" class="mb-2">
                                    <input type="hidden" name="action" value="reserve">
                                    <input type="hidden" name="type" value="supplier">
                                    <button type="submit" class="btn btn-outline-primary btn-sm w-100">
                                        <i class="fas fa-bookmark me-1"></i>حجز رقم مورد
                                    </button>
                                </form>
                                
                                <form method="POST" class="mb-2">
                                    <input type="hidden" name="action" value="confirm">
                                    <input type="hidden" name="type" value="supplier">
                                    <button type="submit" class="btn btn-outline-success btn-sm w-100">
                                        <i class="fas fa-check me-1"></i>تأكيد الحجز
                                    </button>
                                </form>
                                
                                <form method="POST">
                                    <input type="hidden" name="action" value="cancel">
                                    <input type="hidden" name="type" value="supplier">
                                    <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                        <i class="fas fa-times me-1"></i>إلغاء الحجز
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">
                                    <i class="fas fa-boxes me-2"></i>المنتجات
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" class="mb-2">
                                    <input type="hidden" name="action" value="reserve">
                                    <input type="hidden" name="type" value="product">
                                    <button type="submit" class="btn btn-outline-primary btn-sm w-100">
                                        <i class="fas fa-bookmark me-1"></i>حجز رقم منتج
                                    </button>
                                </form>
                                
                                <form method="POST" class="mb-2">
                                    <input type="hidden" name="action" value="confirm">
                                    <input type="hidden" name="type" value="product">
                                    <button type="submit" class="btn btn-outline-success btn-sm w-100">
                                        <i class="fas fa-check me-1"></i>تأكيد الحجز
                                    </button>
                                </form>
                                
                                <form method="POST">
                                    <input type="hidden" name="action" value="cancel">
                                    <input type="hidden" name="type" value="product">
                                    <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                        <i class="fas fa-times me-1"></i>إلغاء الحجز
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- الحجوزات الحالية -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-bookmark me-2"></i>الحجوزات الحالية
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($reservations)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-bookmark fa-2x mb-2"></i>
                                <p>لا توجد حجوزات حالياً</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>نوع الكيان</th>
                                            <th>الرقم المحجوز</th>
                                            <th>وقت الحجز</th>
                                            <th>المدة المتبقية</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($reservations as $type => $reservation): ?>
                                            <?php 
                                            $elapsed = time() - $reservation['timestamp'];
                                            $remaining = 1800 - $elapsed; // 30 دقيقة
                                            $remainingMinutes = max(0, floor($remaining / 60));
                                            ?>
                                            <tr>
                                                <td>
                                                    <?php 
                                                    $typeNames = [
                                                        'customer' => 'عميل',
                                                        'supplier' => 'مورد',
                                                        'product' => 'منتج'
                                                    ];
                                                    echo $typeNames[$type] ?? $type;
                                                    ?>
                                                </td>
                                                <td><code><?php echo htmlspecialchars($reservation['number']); ?></code></td>
                                                <td><?php echo date('H:i:s', $reservation['timestamp']); ?></td>
                                                <td>
                                                    <?php if ($remaining > 0): ?>
                                                        <span class="badge bg-success"><?php echo $remainingMinutes; ?> دقيقة</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">منتهي الصلاحية</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- إحصائيات الترقيم -->
                <div class="card mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>إحصائيات الترقيم
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>نوع الكيان</th>
                                        <th>البادئة</th>
                                        <th>آخر رقم</th>
                                        <th>الرقم التالي</th>
                                        <th>آخر تحديث</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($stats as $stat): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($stat['entity_type']); ?></td>
                                            <td><code><?php echo htmlspecialchars($stat['prefix']); ?></code></td>
                                            <td><?php echo $stat['last_number']; ?></td>
                                            <td><code><?php echo $stat['prefix'] . str_pad($stat['last_number'] + 1, 3, '0', STR_PAD_LEFT); ?></code></td>
                                            <td><?php echo $stat['updated_at'] ?? 'غير محدد'; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- أدوات إضافية -->
                <div class="card mb-4">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-tools me-2"></i>أدوات إضافية
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="action" value="cleanup">
                            <button type="submit" class="btn btn-warning me-2">
                                <i class="fas fa-broom me-1"></i>تنظيف الحجوزات المنتهية
                            </button>
                        </form>
                        
                        <a href="customers.php" class="btn btn-primary me-2">
                            <i class="fas fa-users me-1"></i>اختبار في صفحة العملاء
                        </a>
                        
                        <a href="dashboard.php" class="btn btn-secondary">
                            <i class="fas fa-home me-1"></i>العودة للوحة التحكم
                        </a>
                    </div>
                </div>
                
                <!-- شرح النظام -->
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-question-circle me-2"></i>كيف يعمل النظام؟
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>المشكلة القديمة:</h6>
                                <ul class="text-danger">
                                    <li>عند فتح نموذج إضافة عميل، يتم توليد رقم جديد</li>
                                    <li>إذا ألغى المستخدم العملية، الرقم يضيع</li>
                                    <li>في المرة التالية، يظهر رقم جديد (فجوة في الترقيم)</li>
                                </ul>
                            </div>
                            
                            <div class="col-md-6">
                                <h6>الحل الجديد:</h6>
                                <ul class="text-success">
                                    <li>عند فتح النموذج، يتم <strong>حجز</strong> الرقم مؤقتاً</li>
                                    <li>الرقم محفوظ في الجلسة، لا يتم زيادة العداد</li>
                                    <li>عند الحفظ، يتم <strong>تأكيد</strong> الرقم وزيادة العداد</li>
                                    <li>عند الإلغاء، يتم <strong>إلغاء الحجز</strong> والرقم يبقى متاح</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث الصفحة كل 30 ثانية لعرض الوقت المتبقي
        setInterval(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
