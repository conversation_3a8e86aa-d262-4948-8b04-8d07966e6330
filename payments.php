<?php
/**
 * SeaSystem - صفحة إدارة المدفوعات
 * Payments Management Page
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/includes/sidebar.php';
require_once __DIR__ . '/classes/Payment.php';
require_once __DIR__ . '/classes/Customer.php';

// التأكد من تسجيل الدخول
requireLogin();

$payment = new Payment();
$customer = new Customer();
$current_user = getCurrentUser();

// معالجة العمليات
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $data = [
                    'payment_type' => $_POST['payment_type'],
                    'payment_method' => $_POST['payment_method'],
                    'customer_id' => !empty($_POST['customer_id']) ? $_POST['customer_id'] : null,
                    'supplier_id' => !empty($_POST['supplier_id']) ? $_POST['supplier_id'] : null,
                    'amount' => $_POST['amount'],
                    'payment_date' => $_POST['payment_date'],
                    'reference_number' => $_POST['reference_number'],
                    'notes' => $_POST['notes'],
                    'status' => 'completed',
                    'created_by' => $current_user['id']
                ];

                $result = $payment->create($data);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;
        }
    }
}

// الحصول على قائمة المدفوعات
$filters = [];
if (!empty($_GET['type'])) {
    $filters['payment_type'] = $_GET['type'];
}
if (!empty($_GET['method'])) {
    $filters['payment_method'] = $_GET['method'];
}
if (!empty($_GET['status'])) {
    $filters['status'] = $_GET['status'];
}
if (!empty($_GET['date_from'])) {
    $filters['date_from'] = $_GET['date_from'];
}
if (!empty($_GET['date_to'])) {
    $filters['date_to'] = $_GET['date_to'];
}

$payments = $payment->getAll($filters);

// البحث
$search_term = $_GET['search'] ?? '';
if (!empty($search_term)) {
    $payments = $payment->search($search_term, $filters);
}

// إحصائيات المدفوعات
$stats = $payment->getStatistics($filters);

// قائمة العملاء للفلترة
$customers = $customer->getAll();

$payment_types = [
    'received' => 'سند قبض',
    'paid' => 'سند صرف'
];

$payment_methods = [
    'cash' => 'نقداً',
    'bank_transfer' => 'تحويل بنكي',
    'check' => 'شيك',
    'credit_card' => 'بطاقة ائتمان'
];

$payment_statuses = [
    'pending' => 'في الانتظار',
    'completed' => 'مكتمل',
    'cancelled' => 'ملغي',
    'bounced' => 'مرتد',
    'scheduled' => 'مجدول'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المدفوعات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <link href="assets/css/sidebar-only.css" rel="stylesheet">
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي الموحد -->
            <?php renderSidebar('payments.php'); ?>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="page-title">
                                <i class="fas fa-credit-card me-2"></i>إدارة المدفوعات
                            </h1>
                            <p class="page-subtitle">تسجيل ومتابعة المدفوعات والمقبوضات</p>
                        </div>
                        <div class="col-auto">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="addPayment('received')">
                                    <i class="fas fa-plus me-2"></i>إنشاء سند قبض
                                </button>
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="addPayment('paid')">
                                    <i class="fas fa-plus me-2"></i>إنشاء سند صرف
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon success">
                                    <i class="fas fa-arrow-down"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format($stats['total_received_amount'], 0); ?></h3>
                                    <p class="text-muted mb-0">إجمالي المقبوضات</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon danger">
                                    <i class="fas fa-arrow-up"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format($stats['total_paid_amount'], 0); ?></h3>
                                    <p class="text-muted mb-0">إجمالي المدفوعات</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon info">
                                    <i class="fas fa-balance-scale"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format($stats['net_cash_flow'], 0); ?></h3>
                                    <p class="text-muted mb-0">صافي التدفق النقدي</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon warning">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format($stats['pending_payments']); ?></h3>
                                    <p class="text-muted mb-0">مدفوعات معلقة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- شريط البحث والفلترة -->
                <div class="card mb-4">
                    <div class="card-body">
                        <!-- البحث الديناميكي -->
                        <div class="row g-3 mb-3">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="paymentSearch"
                                           placeholder="البحث السريع في المدفوعات..."
                                           value="<?php echo $_GET['search'] ?? ''; ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-outline-secondary w-100" id="clearPaymentSearch">
                                    <i class="fas fa-times me-2"></i>مسح البحث
                                </button>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-outline-info w-100" id="toggleFilters">
                                    <i class="fas fa-filter me-2"></i>فلاتر متقدمة
                                </button>
                            </div>
                        </div>

                        <!-- الفلاتر المتقدمة (مخفية افتراضياً) -->
                        <div id="advancedFilters" class="border-top pt-3" style="display: none;">
                            <form method="GET" class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">نوع المدفوع</label>
                                    <select class="form-select" name="type">
                                        <option value="">جميع الأنواع</option>
                                        <?php foreach ($payment_types as $type => $type_name): ?>
                                            <option value="<?php echo $type; ?>" <?php echo ($_GET['type'] ?? '') == $type ? 'selected' : ''; ?>><?php echo $type_name; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">طريقة الدفع</label>
                                    <select class="form-select" name="method">
                                        <option value="">جميع الطرق</option>
                                        <?php foreach ($payment_methods as $method => $method_name): ?>
                                            <option value="<?php echo $method; ?>" <?php echo ($_GET['method'] ?? '') == $method ? 'selected' : ''; ?>><?php echo $method_name; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" name="date_from"
                                           value="<?php echo $_GET['date_from'] ?? ''; ?>">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" name="date_to"
                                           value="<?php echo $_GET['date_to'] ?? ''; ?>">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="submit" class="btn btn-primary w-100 d-block">
                                        <i class="fas fa-search me-2"></i>تطبيق الفلاتر
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- جدول المدفوعات -->
                <div class="table-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-credit-card me-2"></i>قائمة المدفوعات (<?php echo count($payments); ?>)
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="paymentsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم المدفوع</th>
                                        <th>النوع</th>
                                        <th>العميل/المورد</th>
                                        <th>المبلغ</th>
                                        <th>التاريخ</th>
                                        <th>طريقة الدفع</th>
                                        <th>المرجع</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($payments)): ?>
                                        <tr>
                                            <td colspan="9" class="text-center py-4">
                                                <i class="fas fa-credit-card fa-2x text-muted mb-2"></i>
                                                <p class="text-muted mb-0">لا توجد مدفوعات حتى الآن</p>
                                                <div class="mt-2">
                                                    <div class="d-flex gap-2 justify-content-center">
                                                        <button type="button" class="btn btn-outline-success btn-sm" onclick="addPayment('received')">
                                                            إنشاء سند قبض
                                                        </button>
                                                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="addPayment('paid')">
                                                            إنشاء سند صرف
                                                        </button>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($payments as $payment): ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($payment['payment_number']); ?></strong></td>
                                                <td>
                                                    <span class="badge <?php echo $payment['payment_type'] == 'received' ? 'bg-success' : 'bg-danger'; ?>">
                                                        <?php echo $payment_types[$payment['payment_type']]; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars($payment['customer_name'] ?? $payment['supplier_name'] ?? '-'); ?></td>
                                                <td>
                                                    <span class="<?php echo $payment['payment_type'] == 'received' ? 'text-success' : 'text-danger'; ?>">
                                                        <?php echo number_format($payment['amount'], 2) . ' ' . CURRENCY_SYMBOL; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('Y-m-d', strtotime($payment['payment_date'])); ?></td>
                                                <td><?php echo $payment_methods[$payment['payment_method']]; ?></td>
                                                <td><?php echo htmlspecialchars($payment['reference_number']); ?></td>
                                                <td>
                                                    <?php
                                                    $status_class = '';
                                                    switch ($payment['status']) {
                                                        case 'pending':
                                                            $status_class = 'bg-warning';
                                                            break;
                                                        case 'completed':
                                                            $status_class = 'bg-success';
                                                            break;
                                                        case 'cancelled':
                                                            $status_class = 'bg-danger';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>">
                                                        <?php echo $payment_statuses[$payment['status']]; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="d-flex gap-2">
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                                    onclick="viewPayment(<?php echo $payment['id']; ?>)">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">عرض</small>
                                                        </div>
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-success btn-sm"
                                                                    onclick="editPayment(<?php echo $payment['id']; ?>)">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">تعديل</small>
                                                        </div>
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-info btn-sm"
                                                                    onclick="printPayment(<?php echo $payment['id']; ?>)">
                                                                <i class="fas fa-print"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">طباعة</small>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- ملاحظة تطويرية -->
                <div class="alert alert-info mt-4" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> هذه الصفحة تعرض بيانات تجريبية. سيتم ربطها بقاعدة البيانات وإضافة وظائف إدارة المدفوعات الكاملة في التحديث القادم.
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        // إضافة مدفوع جديد
        function addPayment(type) {
            window.location.href = `payment_create.php?type=${type}`;
        }

        // عرض المدفوع
        function viewPayment(paymentId) {
            window.location.href = `payment_view.php?id=${paymentId}`;
        }

        // تعديل المدفوع
        function editPayment(paymentId) {
            alert('سيتم إضافة صفحة تعديل المدفوع قريباً');
            // window.location.href = `payment_edit.php?id=${paymentId}`;
        }

        // طباعة المدفوع
        function printPayment(paymentId) {
            alert('سيتم إضافة وظيفة طباعة المدفوع قريباً');
            // window.open(`payment_print.php?id=${paymentId}`, '_blank');
        }

        // إظهار/إخفاء الفلاتر المتقدمة
        document.getElementById('toggleFilters').addEventListener('click', function() {
            const filtersDiv = document.getElementById('advancedFilters');
            const isVisible = filtersDiv.style.display !== 'none';

            if (isVisible) {
                filtersDiv.style.display = 'none';
                this.innerHTML = '<i class="fas fa-filter me-2"></i>فلاتر متقدمة';
            } else {
                filtersDiv.style.display = 'block';
                this.innerHTML = '<i class="fas fa-times me-2"></i>إخفاء الفلاتر';
            }
        });

        // البحث الديناميكي في المدفوعات
        const paymentSearchInput = document.getElementById('paymentSearch');
        const clearPaymentSearchBtn = document.getElementById('clearPaymentSearch');
        const paymentTableBody = document.querySelector('#paymentsTable tbody');
        let searchTimeout;

        // البحث عند الكتابة
        paymentSearchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const searchTerm = this.value.trim();

            searchTimeout = setTimeout(() => {
                performPaymentSearch(searchTerm);
            }, 300); // انتظار 300 ملي ثانية بعد انتهاء الكتابة
        });

        // مسح البحث
        clearPaymentSearchBtn.addEventListener('click', function() {
            paymentSearchInput.value = '';
            performPaymentSearch('');
        });

        // تنفيذ البحث
        function performPaymentSearch(searchTerm) {
            // إظهار مؤشر التحميل
            paymentTableBody.innerHTML = '<tr><td colspan="9" class="text-center py-4"><i class="fas fa-spinner fa-spin fa-2x text-muted"></i><p class="text-muted mt-2">جاري البحث...</p></td></tr>';

            // إرسال طلب AJAX
            fetch(`ajax_search_payments.php?search=${encodeURIComponent(searchTerm)}`)
                .then(response => response.text())
                .then(data => {
                    paymentTableBody.innerHTML = data;
                })
                .catch(error => {
                    console.error('خطأ في البحث:', error);
                    paymentTableBody.innerHTML = '<tr><td colspan="9" class="text-center py-4 text-danger"><i class="fas fa-exclamation-triangle fa-2x mb-2"></i><p>حدث خطأ في البحث</p></td></tr>';
                });
        }
    
        // تحديث الوقت والتاريخ
        function updateDateTime() {
            const now = new Date();
            const timeElement = document.getElementById('current-time');
            const dateElement = document.getElementById('current-date');
            
            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString('ar-SA', {
                    hour: '2-digit', minute: '2-digit'
                });
            }
            if (dateElement) {
                dateElement.textContent = now.toLocaleDateString('ar-SA');
            }
        }
        setInterval(updateDateTime, 60000);
        updateDateTime();
    </script>
</body>
</html>
