<?php
/**
 * إضافة منتج يدوياً لاختبار النظام
 */

require_once __DIR__ . '/config/database.php';

$message = '';
$message_type = '';

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // إدراج المنتج
        $sql = "INSERT INTO products (product_code, product_name, description, category_id, base_unit_id, cost_price, selling_price, current_stock, min_stock_level, max_stock_level, is_active, created_by) 
                VALUES (:product_code, :product_name, :description, :category_id, :base_unit_id, :cost_price, :selling_price, :current_stock, :min_stock_level, :max_stock_level, 1, 1)";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([
            ':product_code' => $_POST['product_code'],
            ':product_name' => $_POST['product_name'],
            ':description' => $_POST['description'],
            ':category_id' => $_POST['category_id'],
            ':base_unit_id' => $_POST['base_unit_id'],
            ':cost_price' => $_POST['cost_price'],
            ':selling_price' => $_POST['selling_price'],
            ':current_stock' => $_POST['current_stock'],
            ':min_stock_level' => $_POST['min_stock_level'],
            ':max_stock_level' => $_POST['max_stock_level']
        ]);
        
        $message = "✅ تم إضافة المنتج بنجاح!";
        $message_type = "success";
        
    } catch (Exception $e) {
        $message = "❌ خطأ في إضافة المنتج: " . $e->getMessage();
        $message_type = "error";
    }
}

// جلب الفئات ووحدات القياس
try {
    $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $categories = $db->query("SELECT * FROM product_categories ORDER BY category_name")->fetchAll();
    $units = $db->query("SELECT * FROM units_of_measure ORDER BY unit_name")->fetchAll();
    
} catch (Exception $e) {
    $categories = [];
    $units = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة منتج يدوياً</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .container { max-width: 800px; margin: 50px auto; }
        .card { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">🛠️ إضافة منتج يدوياً</h3>
                <small>لاختبار النظام وفهم المشكلة</small>
            </div>
            <div class="card-body">
                
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type == 'success' ? 'success' : 'danger'; ?>" role="alert">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="product_code" class="form-label">رمز المنتج *</label>
                            <input type="text" class="form-control" id="product_code" name="product_code" required 
                                   value="GRN001" placeholder="مثال: GRN001">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="product_name" class="form-label">اسم المنتج *</label>
                            <input type="text" class="form-control" id="product_name" name="product_name" required 
                                   value="جرانيت نيوحلايب" placeholder="مثال: جرانيت نيوحلايب">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3" 
                                  placeholder="وصف المنتج...">جرانيت نيوحلايب عالي الجودة، مناسب للأرضيات والجدران</textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category_id" class="form-label">الفئة</label>
                            <select class="form-select" id="category_id" name="category_id">
                                <option value="">اختر الفئة</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" <?php echo $category['id'] == 1 ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['category_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="base_unit_id" class="form-label">وحدة القياس</label>
                            <select class="form-select" id="base_unit_id" name="base_unit_id">
                                <option value="">اختر الوحدة</option>
                                <?php foreach ($units as $unit): ?>
                                    <option value="<?php echo $unit['id']; ?>" <?php echo $unit['unit_name'] == 'متر مربع' ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($unit['unit_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="cost_price" class="form-label">سعر التكلفة *</label>
                            <input type="number" class="form-control" id="cost_price" name="cost_price" 
                                   step="0.01" min="0" required value="120.00">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="selling_price" class="form-label">سعر البيع *</label>
                            <input type="number" class="form-control" id="selling_price" name="selling_price" 
                                   step="0.01" min="0" required value="180.00">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="current_stock" class="form-label">المخزون الحالي</label>
                            <input type="number" class="form-control" id="current_stock" name="current_stock" 
                                   step="0.001" min="0" value="50.000">
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="min_stock_level" class="form-label">الحد الأدنى</label>
                            <input type="number" class="form-control" id="min_stock_level" name="min_stock_level" 
                                   step="0.001" min="0" value="10.000">
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="max_stock_level" class="form-label">الحد الأقصى</label>
                            <input type="number" class="form-control" id="max_stock_level" name="max_stock_level" 
                                   step="0.001" min="0" value="200.000">
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary">
                            💾 حفظ المنتج
                        </button>
                        <a href="inventory.php" class="btn btn-secondary">
                            🔙 العودة للمخزون
                        </a>
                        <a href="check_database.php" class="btn btn-info">
                            🔍 فحص قاعدة البيانات
                        </a>
                    </div>
                </form>

                <!-- معلومات إضافية -->
                <hr class="my-4">
                <div class="row">
                    <div class="col-md-6">
                        <h6>📂 الفئات المتاحة:</h6>
                        <ul class="list-unstyled">
                            <?php foreach ($categories as $category): ?>
                                <li>• <?php echo htmlspecialchars($category['category_name']); ?> (ID: <?php echo $category['id']; ?>)</li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>📏 وحدات القياس المتاحة:</h6>
                        <ul class="list-unstyled">
                            <?php foreach ($units as $unit): ?>
                                <li>• <?php echo htmlspecialchars($unit['unit_name']); ?> (ID: <?php echo $unit['id']; ?>)</li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
