<?php
/**
 * ملف لإنشاء سندات الدفع والقبض
 */

require_once __DIR__ . '/config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>إنشاء سندات الدفع والقبض</h2>";
    
    // سند صرف 1: دفع لشركة الأهرام للتجارة
    $payment_data_1 = [
        'payment_number' => 'PAY001',
        'payment_type' => 'payment_voucher', // سند صرف
        'supplier_id' => 1, // شركة الأهرام للتجارة
        'amount' => 30000.00,
        'payment_date' => date('Y-m-d'),
        'payment_method' => 'cash',
        'description' => 'دفعة من قيمة فاتورة الشراء PUR001',
        'reference_number' => 'PUR001'
    ];
    
    $sql = "INSERT INTO payments (payment_number, payment_type, supplier_id, amount, payment_date, payment_method, description, reference_number, status) 
            VALUES (:payment_number, :payment_type, :supplier_id, :amount, :payment_date, :payment_method, :description, :reference_number, 'completed')";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([
        ':payment_number' => $payment_data_1['payment_number'],
        ':payment_type' => $payment_data_1['payment_type'],
        ':supplier_id' => $payment_data_1['supplier_id'],
        ':amount' => $payment_data_1['amount'],
        ':payment_date' => $payment_data_1['payment_date'],
        ':payment_method' => $payment_data_1['payment_method'],
        ':description' => $payment_data_1['description'],
        ':reference_number' => $payment_data_1['reference_number']
    ]);
    
    // تحديث رصيد المورد
    $sql = "UPDATE suppliers SET current_balance = current_balance - :amount WHERE id = :supplier_id";
    $stmt = $db->prepare($sql);
    $stmt->execute([
        ':amount' => $payment_data_1['amount'],
        ':supplier_id' => $payment_data_1['supplier_id']
    ]);
    
    echo "تم إنشاء سند الصرف الأول: PAY001 - 30,000 ج.م<br>";
    
    // سند قبض 1: تحصيل من أحمد محمد علي
    $payment_data_2 = [
        'payment_number' => 'REC001',
        'payment_type' => 'receipt_voucher', // سند قبض
        'customer_id' => 1, // أحمد محمد علي
        'amount' => 20000.00,
        'payment_date' => date('Y-m-d'),
        'payment_method' => 'bank_transfer',
        'description' => 'دفعة من قيمة فاتورة البيع SAL001',
        'reference_number' => 'SAL001'
    ];
    
    $sql = "INSERT INTO payments (payment_number, payment_type, customer_id, amount, payment_date, payment_method, description, reference_number, status) 
            VALUES (:payment_number, :payment_type, :customer_id, :amount, :payment_date, :payment_method, :description, :reference_number, 'completed')";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([
        ':payment_number' => $payment_data_2['payment_number'],
        ':payment_type' => $payment_data_2['payment_type'],
        ':customer_id' => $payment_data_2['customer_id'],
        ':amount' => $payment_data_2['amount'],
        ':payment_date' => $payment_data_2['payment_date'],
        ':payment_method' => $payment_data_2['payment_method'],
        ':description' => $payment_data_2['description'],
        ':reference_number' => $payment_data_2['reference_number']
    ]);
    
    // تحديث رصيد العميل
    $sql = "UPDATE customers SET current_balance = current_balance - :amount WHERE id = :customer_id";
    $stmt = $db->prepare($sql);
    $stmt->execute([
        ':amount' => $payment_data_2['amount'],
        ':customer_id' => $payment_data_2['customer_id']
    ]);
    
    echo "تم إنشاء سند القبض الأول: REC001 - 20,000 ج.م<br>";
    
    // سند صرف 2: دفع لمؤسسة النيل للمواد
    $payment_data_3 = [
        'payment_number' => 'PAY002',
        'payment_type' => 'payment_voucher',
        'supplier_id' => 2, // مؤسسة النيل للمواد
        'amount' => 8000.00,
        'payment_date' => date('Y-m-d'),
        'payment_method' => 'check',
        'description' => 'دفعة من قيمة فاتورة الشراء PUR002',
        'reference_number' => 'PUR002'
    ];
    
    $sql = "INSERT INTO payments (payment_number, payment_type, supplier_id, amount, payment_date, payment_method, description, reference_number, status) 
            VALUES (:payment_number, :payment_type, :supplier_id, :amount, :payment_date, :payment_method, :description, :reference_number, 'completed')";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([
        ':payment_number' => $payment_data_3['payment_number'],
        ':payment_type' => $payment_data_3['payment_type'],
        ':supplier_id' => $payment_data_3['supplier_id'],
        ':amount' => $payment_data_3['amount'],
        ':payment_date' => $payment_data_3['payment_date'],
        ':payment_method' => $payment_data_3['payment_method'],
        ':description' => $payment_data_3['description'],
        ':reference_number' => $payment_data_3['reference_number']
    ]);
    
    // تحديث رصيد المورد
    $sql = "UPDATE suppliers SET current_balance = current_balance - :amount WHERE id = :supplier_id";
    $stmt = $db->prepare($sql);
    $stmt->execute([
        ':amount' => $payment_data_3['amount'],
        ':supplier_id' => $payment_data_3['supplier_id']
    ]);
    
    echo "تم إنشاء سند الصرف الثاني: PAY002 - 8,000 ج.م<br>";
    
    // سند قبض 2: تحصيل من فاطمة حسن محمود
    $payment_data_4 = [
        'payment_number' => 'REC002',
        'payment_type' => 'receipt_voucher',
        'customer_id' => 2, // فاطمة حسن محمود
        'amount' => 4788.00,
        'payment_date' => date('Y-m-d'),
        'payment_method' => 'cash',
        'description' => 'تحصيل كامل لفاتورة البيع SAL002',
        'reference_number' => 'SAL002'
    ];
    
    $sql = "INSERT INTO payments (payment_number, payment_type, customer_id, amount, payment_date, payment_method, description, reference_number, status) 
            VALUES (:payment_number, :payment_type, :customer_id, :amount, :payment_date, :payment_method, :description, :reference_number, 'completed')";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([
        ':payment_number' => $payment_data_4['payment_number'],
        ':payment_type' => $payment_data_4['payment_type'],
        ':customer_id' => $payment_data_4['customer_id'],
        ':amount' => $payment_data_4['amount'],
        ':payment_date' => $payment_data_4['payment_date'],
        ':payment_method' => $payment_data_4['payment_method'],
        ':description' => $payment_data_4['description'],
        ':reference_number' => $payment_data_4['reference_number']
    ]);
    
    // تحديث رصيد العميل
    $sql = "UPDATE customers SET current_balance = current_balance - :amount WHERE id = :customer_id";
    $stmt = $db->prepare($sql);
    $stmt->execute([
        ':amount' => $payment_data_4['amount'],
        ':customer_id' => $payment_data_4['customer_id']
    ]);
    
    echo "تم إنشاء سند القبض الثاني: REC002 - 4,788 ج.م<br>";
    
    echo "<br><strong>ملخص المدفوعات:</strong><br>";
    echo "<strong>سندات الصرف:</strong><br>";
    echo "- PAY001: 30,000 ج.م لشركة الأهرام للتجارة<br>";
    echo "- PAY002: 8,000 ج.م لمؤسسة النيل للمواد<br>";
    echo "- إجمالي المدفوعات: 38,000 ج.م<br><br>";
    
    echo "<strong>سندات القبض:</strong><br>";
    echo "- REC001: 20,000 ج.م من أحمد محمد علي<br>";
    echo "- REC002: 4,788 ج.م من فاطمة حسن محمود<br>";
    echo "- إجمالي المقبوضات: 24,788 ج.م<br>";
    
    echo '<br><a href="payments.php">انتقل لصفحة المدفوعات</a>';
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
