<?php
/**
 * صفحة عرض تفاصيل العميل
 */

session_start();
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/config/constants.php';
require_once __DIR__ . '/includes/sidebar.php';
require_once __DIR__ . '/classes/Customer.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// التحقق من وجود معرف العميل
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: customers.php');
    exit();
}

$customer_id = (int)$_GET['id'];

// إنشاء كائن العميل
$customer = new Customer();
$customer_data = $customer->getById($customer_id);

// التحقق من وجود العميل
if (!$customer_data) {
    $_SESSION['error_message'] = 'العميل غير موجود';
    header('Location: customers.php');
    exit();
}

// الحصول على المعاملات المالية للعميل (يمكن إضافتها لاحقاً)
// $transactions = $customer->getTransactions($customer_id);
// $invoices = $customer->getInvoices($customer_id);
// $payments = $customer->getPayments($customer_id);

$current_user = $_SESSION;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض تفاصيل العميل - <?php echo htmlspecialchars($customer_data['name']); ?> | <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/sidebar-only.css" rel="stylesheet">

    <style>
        .info-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .info-card .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0;
            padding: 15px 20px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
            min-width: 150px;
        }

        .info-value {
            color: #212529;
            flex: 1;
            text-align: left;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .balance-positive {
            color: #28a745;
            font-weight: bold;
        }

        .balance-negative {
            color: #dc3545;
            font-weight: bold;
        }

        .action-buttons {
            gap: 10px;
        }

        .print-section {
            display: none;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            .print-section {
                display: block !important;
            }

            .container-fluid {
                margin: 0;
                padding: 0;
            }

            .info-card {
                box-shadow: none;
                border: 1px solid #ddd;
            }
        }
    </style>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }
        
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي الموحد -->
            <div class="no-print">
                <?php renderSidebar('customers.php'); ?>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- رأس الصفحة -->
                <div class="d-flex justify-content-between align-items-center mb-4 no-print">
                    <div>
                        <h1 class="h3 mb-1">
                            <i class="fas fa-user me-2"></i>تفاصيل العميل
                        </h1>
                        <p class="text-muted mb-0">عرض جميع بيانات ومعاملات العميل</p>
                    </div>
                    <div class="action-buttons d-flex">
                        <a href="customers.php" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
                        </a>
                        <button onclick="editCustomer(<?php echo htmlspecialchars(json_encode($customer_data)); ?>)" class="btn btn-primary me-2">
                            <i class="fas fa-edit me-2"></i>تعديل
                        </button>
                        <button onclick="window.print()" class="btn btn-info">
                            <i class="fas fa-print me-2"></i>طباعة
                        </button>
                    </div>
                </div>

                <!-- معلومات العميل الأساسية -->
                <div class="info-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-row">
                                    <span class="info-label">رمز العميل:</span>
                                    <span class="info-value">
                                        <strong><?php echo htmlspecialchars($customer_data['customer_code']); ?></strong>
                                    </span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">اسم العميل:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($customer_data['name']); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">البريد الإلكتروني:</span>
                                    <span class="info-value">
                                        <?php if (!empty($customer_data['email'])): ?>
                                            <a href="mailto:<?php echo htmlspecialchars($customer_data['email']); ?>">
                                                <?php echo htmlspecialchars($customer_data['email']); ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">رقم الهاتف:</span>
                                    <span class="info-value">
                                        <?php if (!empty($customer_data['phone'])): ?>
                                            <a href="tel:<?php echo htmlspecialchars($customer_data['phone']); ?>">
                                                <?php echo htmlspecialchars($customer_data['phone']); ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-row">
                                    <span class="info-label">الرقم الضريبي:</span>
                                    <span class="info-value">
                                        <?php echo !empty($customer_data['tax_number']) ? htmlspecialchars($customer_data['tax_number']) : '<span class="text-muted">غير محدد</span>'; ?>
                                    </span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">حد الائتمان:</span>
                                    <span class="info-value">
                                        <?php echo number_format($customer_data['credit_limit'], 2) . ' ' . CURRENCY_SYMBOL; ?>
                                    </span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">الحالة:</span>
                                    <span class="info-value">
                                        <?php if ($customer_data['is_active']): ?>
                                            <span class="status-badge bg-success text-white">نشط</span>
                                        <?php else: ?>
                                            <span class="status-badge bg-secondary text-white">غير نشط</span>
                                        <?php endif; ?>
                                    </span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">تاريخ الإنشاء:</span>
                                    <span class="info-value">
                                        <?php echo date('Y-m-d H:i', strtotime($customer_data['created_at'])); ?>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <?php if (!empty($customer_data['address'])): ?>
                            <div class="info-row mt-3">
                                <span class="info-label">العنوان:</span>
                                <span class="info-value"><?php echo nl2br(htmlspecialchars($customer_data['address'])); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- المعلومات المالية -->
                <div class="info-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-money-bill-wave me-2"></i>المعلومات المالية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <h6 class="text-muted mb-2">الرصيد الافتتاحي</h6>
                                    <h4 class="mb-0">
                                        <?php echo number_format($customer_data['opening_balance'], 2) . ' ' . CURRENCY_SYMBOL; ?>
                                    </h4>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <h6 class="text-muted mb-2">الرصيد الحالي</h6>
                                    <h4 class="mb-0 <?php echo $customer_data['current_balance'] >= 0 ? 'balance-positive' : 'balance-negative'; ?>">
                                        <?php echo number_format($customer_data['current_balance'], 2) . ' ' . CURRENCY_SYMBOL; ?>
                                    </h4>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <h6 class="text-muted mb-2">حد الائتمان المتاح</h6>
                                    <h4 class="mb-0 text-info">
                                        <?php
                                        $available_credit = $customer_data['credit_limit'] - abs($customer_data['current_balance']);
                                        echo number_format(max(0, $available_credit), 2) . ' ' . CURRENCY_SYMBOL;
                                        ?>
                                    </h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قسم المعاملات (يمكن إضافته لاحقاً) -->
                <div class="info-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>آخر المعاملات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center py-4">
                            <i class="fas fa-clock fa-2x text-muted mb-3"></i>
                            <p class="text-muted mb-0">سيتم إضافة تاريخ المعاملات قريباً</p>
                            <small class="text-muted">الفواتير، المدفوعات، والحركات المالية</small>
                        </div>
                    </div>
                </div>

                <!-- قسم الطباعة -->
                <div class="print-section">
                    <div class="text-center mb-4">
                        <h2><?php echo SITE_NAME; ?></h2>
                        <h4>بيانات العميل</h4>
                        <p>تاريخ الطباعة: <?php echo date('Y-m-d H:i:s'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        // دالة تعديل العميل
        function editCustomer(customer) {
            // الانتقال لصفحة العملاء مع فتح نافذة التعديل
            window.location.href = 'customers.php?edit=' + customer.id;
        }

        // طباعة الصفحة
        function printPage() {
            window.print();
        }

        // رسالة ترحيب
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ تم تحميل صفحة عرض العميل بنجاح');
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
