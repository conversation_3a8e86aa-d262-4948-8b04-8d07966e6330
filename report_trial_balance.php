<?php
/**
 * SeaSystem - تقرير ميزان المراجعة
 * Trial Balance Report
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/FinancialReport.php';

// التأكد من تسجيل الدخول
requireLogin();

$financial_report = new FinancialReport();
$current_user = getCurrentUser();

// الحصول على التواريخ من المعاملات
$date_from = $_GET['date_from'] ?? date('Y-01-01');
$date_to = $_GET['date_to'] ?? date('Y-m-d');

// الحصول على ميزان المراجعة
$trial_balance = $financial_report->getTrialBalance($date_from, $date_to);

// أنواع الحسابات
$account_types = [
    'asset' => 'الأصول',
    'liability' => 'الخصوم',
    'equity' => 'حقوق الملكية',
    'revenue' => 'الإيرادات',
    'expense' => 'المصروفات'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ميزان المراجعة - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <style>
        .report-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .balance-indicator {
            position: fixed;
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
            z-index: 1000;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            padding: 1rem;
            min-width: 200px;
        }

        .account-type-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 0.5rem 1rem;
            margin: 1rem 0;
            border-left: 4px solid #667eea;
        }

        .balanced {
            color: #28a745;
        }

        .unbalanced {
            color: #dc3545;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            .balance-indicator {
                position: static;
                transform: none;
                margin-bottom: 1rem;
            }

            .report-header {
                background: #f8f9fa !important;
                color: #333 !important;
                border: 2px solid #dee2e6;
            }
        }
    </style>

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }
        
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <!-- مؤشر التوازن -->
    <div class="balance-indicator no-print">
        <div class="text-center">
            <i class="fas fa-balance-scale fa-2x <?php echo $trial_balance['is_balanced'] ? 'balanced' : 'unbalanced'; ?>"></i>
            <h6 class="mt-2 mb-1">حالة التوازن</h6>
            <span class="badge <?php echo $trial_balance['is_balanced'] ? 'bg-success' : 'bg-danger'; ?>">
                <?php echo $trial_balance['is_balanced'] ? 'متوازن' : 'غير متوازن'; ?>
            </span>

            <?php if (!$trial_balance['is_balanced']): ?>
                <div class="mt-2 small">
                    <div>الفرق: <?php echo number_format(abs($trial_balance['total_debit'] - $trial_balance['total_credit']), 2); ?></div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- المحتوى الرئيسي -->
            <div class="col-12 p-4">
                <!-- أزرار الإجراءات -->
                <div class="row mb-3 no-print">
                    <div class="col">
                        <a href="reports.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                        </a>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.print()">
                                <i class="fas fa-print me-2"></i>طباعة
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-2"></i>تصدير Excel
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                            </button>
                        </div>
                    </div>
                </div>

                <!-- رأس التقرير -->
                <div class="report-header">
                    <h1 class="mb-2">
                        <i class="fas fa-balance-scale me-3"></i>ميزان المراجعة
                    </h1>
                    <h4 class="mb-3">Trial Balance</h4>
                    <p class="mb-0">
                        من <?php echo date('d/m/Y', strtotime($trial_balance['date_from'])); ?>
                        إلى <?php echo date('d/m/Y', strtotime($trial_balance['date_to'])); ?>
                    </p>
                    <small class="opacity-75">تاريخ الإنشاء: <?php echo date('d/m/Y H:i'); ?></small>
                </div>

                <!-- فلترة التواريخ -->
                <div class="card mb-4 no-print">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="date_from" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from"
                                       value="<?php echo $date_from; ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="date_to" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to"
                                       value="<?php echo $date_to; ?>">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>تحديث التقرير
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- جدول ميزان المراجعة -->
                <div class="card">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th>رمز الحساب</th>
                                        <th>اسم الحساب</th>
                                        <th>نوع الحساب</th>
                                        <th class="text-end">مدين</th>
                                        <th class="text-end">دائن</th>
                                        <th class="text-end">الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($trial_balance['accounts'])): ?>
                                        <tr>
                                            <td colspan="6" class="text-center py-4">
                                                <i class="fas fa-exclamation-triangle fa-2x text-muted mb-2"></i>
                                                <p class="text-muted mb-0">لا توجد بيانات للفترة المحددة</p>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php
                                        $current_type = '';
                                        foreach ($trial_balance['accounts'] as $account):
                                            // إظهار عنوان نوع الحساب
                                            if ($current_type != $account['account_type']):
                                                $current_type = $account['account_type'];
                                        ?>
                                                <tr>
                                                    <td colspan="6" class="account-type-section">
                                                        <strong><?php echo $account_types[$account['account_type']] ?? $account['account_type']; ?></strong>
                                                    </td>
                                                </tr>
                                        <?php endif; ?>

                                        <?php if ($account['total_debit'] > 0 || $account['total_credit'] > 0): ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($account['account_code']); ?></strong></td>
                                                <td><?php echo htmlspecialchars($account['account_name']); ?></td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <?php echo $account_types[$account['account_type']] ?? $account['account_type']; ?>
                                                    </span>
                                                </td>
                                                <td class="text-end">
                                                    <?php if ($account['total_debit'] > 0): ?>
                                                        <strong><?php echo number_format($account['total_debit'], 2); ?></strong>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="text-end">
                                                    <?php if ($account['total_credit'] > 0): ?>
                                                        <strong><?php echo number_format($account['total_credit'], 2); ?></strong>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="text-end">
                                                    <span class="<?php echo $account['display_balance'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                        <strong><?php echo number_format(abs($account['display_balance']), 2); ?></strong>
                                                        <?php echo $account['display_balance'] >= 0 ? 'مدين' : 'دائن'; ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>

                                <!-- المجاميع -->
                                <tfoot class="table-secondary">
                                    <tr>
                                        <td colspan="3"><strong>الإجمالي:</strong></td>
                                        <td class="text-end">
                                            <strong><?php echo number_format($trial_balance['total_debit'], 2); ?></strong>
                                        </td>
                                        <td class="text-end">
                                            <strong><?php echo number_format($trial_balance['total_credit'], 2); ?></strong>
                                        </td>
                                        <td class="text-end">
                                            <span class="<?php echo $trial_balance['is_balanced'] ? 'balanced' : 'unbalanced'; ?>">
                                                <strong>
                                                    <?php if ($trial_balance['is_balanced']): ?>
                                                        متوازن ✓
                                                    <?php else: ?>
                                                        فرق: <?php echo number_format(abs($trial_balance['total_debit'] - $trial_balance['total_credit']), 2); ?>
                                                    <?php endif; ?>
                                                </strong>
                                            </span>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- ملاحظات -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-info-circle me-2"></i>ملاحظات
                        </h6>
                        <ul class="mb-0">
                            <li>يعرض هذا التقرير جميع الحسابات التي لها حركة في الفترة المحددة</li>
                            <li>الحسابات مرتبة حسب رمز الحساب ومجمعة حسب نوع الحساب</li>
                            <li>يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن</li>
                            <li>الرصيد يظهر حسب الطبيعة الطبيعية للحساب (مدين للأصول والمصروفات، دائن للخصوم والإيرادات)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function exportToExcel() {
            alert('سيتم إضافة وظيفة تصدير Excel قريباً');
            // يمكن إضافة مكتبة SheetJS هنا
        }

        function exportToPDF() {
            alert('سيتم إضافة وظيفة تصدير PDF قريباً');
            // يمكن إضافة مكتبة jsPDF هنا
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
