/**
 * SeaSystem - تحسين حقول الأرقام
 * Number Input Enhancement
 */

document.addEventListener('DOMContentLoaded', function() {
    // البحث عن جميع حقول الأرقام النصية
    const numberInputs = document.querySelectorAll('input[type="text"][pattern*="[0-9]"]');
    
    numberInputs.forEach(function(input) {
        // منع إدخال الحروف
        input.addEventListener('keypress', function(e) {
            const char = String.fromCharCode(e.which);
            const pattern = /[0-9.]/;
            
            // السماح بالأرقام والنقطة العشرية فقط
            if (!pattern.test(char)) {
                e.preventDefault();
                return false;
            }
            
            // منع إدخال أكثر من نقطة عشرية واحدة
            if (char === '.' && input.value.includes('.')) {
                e.preventDefault();
                return false;
            }
        });
        
        // تنسيق الرقم عند فقدان التركيز
        input.addEventListener('blur', function() {
            let value = this.value.trim();
            
            if (value === '' || value === '.') {
                this.value = '0.00';
                return;
            }
            
            // تحويل إلى رقم وإعادة تنسيقه
            const number = parseFloat(value);
            if (!isNaN(number)) {
                this.value = number.toFixed(2);
            } else {
                this.value = '0.00';
            }
        });
        
        // تحديد النص عند التركيز للتسهيل على المستخدم
        input.addEventListener('focus', function() {
            this.select();
        });
        
        // منع لصق النصوص غير الرقمية
        input.addEventListener('paste', function(e) {
            e.preventDefault();
            
            const paste = (e.clipboardData || window.clipboardData).getData('text');
            const pattern = /^[0-9]+(\.[0-9]+)?$/;
            
            if (pattern.test(paste)) {
                this.value = parseFloat(paste).toFixed(2);
                
                // تشغيل حدث input للتحديثات الأخرى
                const event = new Event('input', { bubbles: true });
                this.dispatchEvent(event);
            }
        });
    });
    
    // تحسين خاص لحقول الأرصدة
    const balanceInputs = document.querySelectorAll('#opening_balance, #edit_opening_balance');
    balanceInputs.forEach(function(input) {
        // إضافة أيقونة العملة
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
    
    // تحسين خاص لحقول الأسعار
    const priceInputs = document.querySelectorAll('#cost_price, #selling_price');
    priceInputs.forEach(function(input) {
        // تحديث حساب الربح عند تغيير الأسعار
        input.addEventListener('input', function() {
            if (typeof calculateProfit === 'function') {
                calculateProfit();
            }
        });
    });
});

// دالة مساعدة لتنسيق الأرقام
function formatNumber(value, decimals = 2) {
    const number = parseFloat(value);
    if (isNaN(number)) return '0.00';
    return number.toFixed(decimals);
}

// دالة مساعدة للتحقق من صحة الرقم
function isValidNumber(value) {
    const pattern = /^[0-9]+(\.[0-9]+)?$/;
    return pattern.test(value) && !isNaN(parseFloat(value));
}

// إضافة تنسيق CSS للحقول المركزة
const style = document.createElement('style');
style.textContent = `
    .input-group.focused {
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        border-radius: 0.375rem;
    }
    
    .input-group.focused .form-control {
        border-color: #86b7fe;
    }
    
    .input-group.focused .input-group-text {
        border-color: #86b7fe;
        background-color: #e7f1ff;
    }
    
    input[type="text"][pattern*="[0-9]"] {
        text-align: left;
        direction: ltr;
    }
    
    input[type="text"][pattern*="[0-9]"]::placeholder {
        text-align: right;
        direction: rtl;
        opacity: 0.6;
    }
`;
document.head.appendChild(style);
