<?php
/**
 * SeaSystem - ملف الإعدادات الرئيسي
 * Main Configuration File
 */

// منع الوصول المباشر
if (!defined('SEASYSTEM_ACCESS')) {
    define('SEASYSTEM_ACCESS', true);
}

// إعدادات النظام الأساسية
define('SITE_NAME', 'SeaSystem');
define('SITE_VERSION', '1.0.0');
define('SITE_DESCRIPTION', 'نظام محاسبي متكامل');

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'seasystem');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('PASSWORD_MIN_LENGTH', 6);
define('MAX_LOGIN_ATTEMPTS', 5);

// إعدادات العملة
define('CURRENCY_SYMBOL', 'ر.س');
define('CURRENCY_CODE', 'SAR');
define('DECIMAL_PLACES', 2);

// إعدادات التاريخ والوقت
define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
define('DATE_FORMAT', 'd/m/Y');
define('DATETIME_FORMAT', 'd/m/Y H:i');

// إعدادات النظام
define('ENABLE_DEBUG', true);
define('ENABLE_LOGGING', true);

// مسارات النظام
define('ROOT_PATH', dirname(__DIR__));

// إعدادات المنطقة الزمنية
date_default_timezone_set(DEFAULT_TIMEZONE);

// إعدادات PHP
ini_set('display_errors', ENABLE_DEBUG ? 1 : 0);
ini_set('log_errors', ENABLE_LOGGING ? 1 : 0);
error_reporting(ENABLE_DEBUG ? E_ALL : E_ERROR | E_WARNING);

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// دوال مساعدة أساسية
function formatCurrency($amount, $show_symbol = true) {
    $formatted = number_format($amount, DECIMAL_PLACES);
    return $show_symbol ? $formatted . ' ' . CURRENCY_SYMBOL : $formatted;
}

function formatDate($date, $format = DATE_FORMAT) {
    if (empty($date) || $date == '0000-00-00' || $date == '0000-00-00 00:00:00') {
        return '-';
    }
    return date($format, strtotime($date));
}

function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}


?>
