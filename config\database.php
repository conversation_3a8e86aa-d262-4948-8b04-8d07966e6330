<?php
/**
 * SeaSystem - إعدادات قاعدة البيانات
 * Database Configuration File
 */

// إعدادات قاعدة البيانات (فقط إذا لم تكن معرفة من قبل)
if (!defined('DB_HOST')) {
    define('DB_HOST', 'localhost');
    define('DB_NAME', 'seasystem');
    define('DB_USER', 'root');
    define('DB_PASS', '');
    define('DB_CHARSET', 'utf8mb4');
}

// إعدادات النظام
if (!defined('SITE_URL')) {
    define('SITE_URL', 'http://localhost/seasystem');
    define('TIMEZONE', 'Asia/Riyadh');
}

// إعدادات الأمان
if (!defined('SECRET_KEY')) {
    // مفتاح سري عشوائي - يجب تغييره في الإنتاج
    define('SECRET_KEY', hash('sha256', 'SeaSystem_' . date('Y-m-d') . '_' . uniqid()));
}

// إعدادات CSRF
if (!defined('CSRF_TOKEN_NAME')) {
    define('CSRF_TOKEN_NAME', 'csrf_token');
    define('CSRF_TOKEN_EXPIRE', 3600); // ساعة واحدة
}

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    public $conn;

    /**
     * الاتصال بقاعدة البيانات
     */
    public function getConnection() {
        $this->conn = null;

        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

            // تعيين المنطقة الزمنية
            date_default_timezone_set(TIMEZONE);

        } catch(PDOException $exception) {
            echo "خطأ في الاتصال بقاعدة البيانات: " . $exception->getMessage();
            die();
        }

        return $this->conn;
    }

    /**
     * إغلاق الاتصال
     */
    public function closeConnection() {
        $this->conn = null;
    }

    /**
     * تنفيذ استعلام SQL
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch(PDOException $e) {
            throw new Exception("خطأ في تنفيذ الاستعلام: " . $e->getMessage());
        }
    }

    /**
     * الحصول على آخر ID مُدرج
     */
    public function lastInsertId() {
        return $this->conn->lastInsertId();
    }

    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        return $this->conn->beginTransaction();
    }

    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->conn->commit();
    }

    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->conn->rollback();
    }
}

// إنشاء اتصال عام لقاعدة البيانات
try {
    $database = new Database();
    $pdo = $database->getConnection();
} catch (Exception $e) {
    error_log("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
    $pdo = null;
}

?>
