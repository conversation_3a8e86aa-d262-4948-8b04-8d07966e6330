<?php
/**
 * SeaSystem - فئة إدارة الصلاحيات
 * Permission Management Class
 */

class Permission {
    private $db;
    
    public function __construct() {
        try {
            $this->db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (PDOException $e) {
            throw new Exception("فشل الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على جميع الصلاحيات مجمعة حسب المجموعة
     */
    public function getAllPermissionsGrouped() {
        try {
            $stmt = $this->db->query("
                SELECT permission_key, permission_name, permission_group, description 
                FROM permissions 
                ORDER BY permission_group, permission_name
            ");
            
            $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $grouped = [];
            
            foreach ($permissions as $permission) {
                $grouped[$permission['permission_group']][] = $permission;
            }
            
            return $grouped;
        } catch (PDOException $e) {
            throw new Exception("خطأ في جلب الصلاحيات: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على صلاحيات مستخدم معين
     */
    public function getUserPermissions($userId) {
        try {
            $stmt = $this->db->prepare("
                SELECT permission_key 
                FROM user_permissions 
                WHERE user_id = ? AND granted = 1
            ");
            $stmt->execute([$userId]);
            
            return array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'permission_key');
        } catch (PDOException $e) {
            throw new Exception("خطأ في جلب صلاحيات المستخدم: " . $e->getMessage());
        }
    }
    
    /**
     * تحديث صلاحيات مستخدم
     */
    public function updateUserPermissions($userId, $permissions, $grantedBy) {
        try {
            $this->db->beginTransaction();
            
            // حذف جميع الصلاحيات الحالية للمستخدم
            $stmt = $this->db->prepare("DELETE FROM user_permissions WHERE user_id = ?");
            $stmt->execute([$userId]);
            
            // إضافة الصلاحيات الجديدة
            if (!empty($permissions)) {
                $stmt = $this->db->prepare("
                    INSERT INTO user_permissions (user_id, permission_key, granted_by) 
                    VALUES (?, ?, ?)
                ");
                
                foreach ($permissions as $permission) {
                    $stmt->execute([$userId, $permission, $grantedBy]);
                }
            }
            
            $this->db->commit();
            return ['success' => true, 'message' => 'تم تحديث الصلاحيات بنجاح'];
            
        } catch (PDOException $e) {
            $this->db->rollBack();
            throw new Exception("خطأ في تحديث الصلاحيات: " . $e->getMessage());
        }
    }
    
    /**
     * التحقق من صلاحية مستخدم
     */
    public function hasPermission($userId, $permissionKey) {
        try {
            $stmt = $this->db->prepare("
                SELECT COUNT(*) 
                FROM user_permissions 
                WHERE user_id = ? AND permission_key = ? AND granted = 1
            ");
            $stmt->execute([$userId, $permissionKey]);
            
            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            return false;
        }
    }
    
    /**
     * الحصول على أسماء المجموعات بالعربية
     */
    public function getGroupNames() {
        return [
            'dashboard' => 'لوحة التحكم',
            'customers' => 'إدارة العملاء',
            'suppliers' => 'إدارة الموردين',
            'inventory' => 'إدارة المخزون',
            'invoices' => 'إدارة الفواتير',
            'payments' => 'إدارة المدفوعات',
            'accounts' => 'دليل الحسابات',
            'journal' => 'دفتر اليومية',
            'reports' => 'التقارير',
            'system' => 'إدارة النظام'
        ];
    }
    
    /**
     * منح صلاحيات افتراضية حسب الدور
     */
    public function grantDefaultPermissionsByRole($userId, $role, $grantedBy) {
        $defaultPermissions = [];
        
        switch ($role) {
            case 'admin':
                // مدير النظام - جميع الصلاحيات
                $stmt = $this->db->query("SELECT permission_key FROM permissions");
                $defaultPermissions = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'permission_key');
                break;
                
            case 'financial_manager':
                // مدير مالي
                $defaultPermissions = [
                    'dashboard_view', 'invoices_view', 'sales_invoices_add', 'purchase_invoices_add',
                    'invoices_edit', 'invoices_print', 'payments_view', 'payments_add', 'payments_edit',
                    'accounts_view', 'accounts_add', 'accounts_edit', 'journal_view', 'journal_add',
                    'journal_edit', 'reports_view', 'reports_financial', 'customers_view', 'suppliers_view'
                ];
                break;
                
            case 'accountant':
                // محاسب
                $defaultPermissions = [
                    'dashboard_view', 'accounts_view', 'accounts_add', 'accounts_edit',
                    'journal_view', 'journal_add', 'journal_edit', 'reports_view', 'reports_financial',
                    'invoices_view', 'payments_view'
                ];
                break;
                
            case 'inventory_manager':
                // مدير مخزون
                $defaultPermissions = [
                    'dashboard_view', 'inventory_view', 'inventory_add', 'inventory_edit',
                    'inventory_movements', 'warehouses_manage', 'reports_inventory'
                ];
                break;
                
            case 'sales_manager':
                // مدير مبيعات
                $defaultPermissions = [
                    'dashboard_view', 'customers_view', 'customers_add', 'customers_edit',
                    'invoices_view', 'sales_invoices_add', 'invoices_edit', 'invoices_print',
                    'reports_sales', 'reports_customers'
                ];
                break;
                
            case 'purchase_manager':
                // مدير مشتريات
                $defaultPermissions = [
                    'dashboard_view', 'suppliers_view', 'suppliers_add', 'suppliers_edit',
                    'invoices_view', 'purchase_invoices_add', 'invoices_edit', 'invoices_print',
                    'reports_purchases', 'reports_suppliers'
                ];
                break;
                
            case 'cashier':
                // أمين صندوق
                $defaultPermissions = [
                    'dashboard_view', 'payments_view', 'payments_add', 'payments_edit',
                    'invoices_view', 'customers_view', 'suppliers_view'
                ];
                break;
                
            case 'data_entry':
                // مدخل بيانات
                $defaultPermissions = [
                    'dashboard_view', 'customers_view', 'customers_add', 'customers_edit',
                    'suppliers_view', 'suppliers_add', 'suppliers_edit',
                    'inventory_view', 'inventory_add', 'inventory_edit'
                ];
                break;
                
            case 'viewer':
                // مستعرض فقط
                $defaultPermissions = [
                    'dashboard_view', 'customers_view', 'suppliers_view', 'inventory_view',
                    'invoices_view', 'payments_view', 'accounts_view', 'journal_view', 'reports_view'
                ];
                break;
        }
        
        if (!empty($defaultPermissions)) {
            $this->updateUserPermissions($userId, $defaultPermissions, $grantedBy);
        }
    }
}
?>
