<?php
/**
 * SeaSystem - اختبار نظام الأرقام الذكي
 * Test Smart Number System
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/classes/SmartNumberManager.php';

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$smartManager = new SmartNumberManager();
$message = '';
$messageType = '';
$testResults = [];

// إنشاء الجداول إذا لم تكن موجودة
$setup_result = $smartManager->createTables();

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'test_full_cycle':
            // اختبار دورة كاملة
            $testResults['title'] = 'اختبار دورة كاملة للنظام';
            
            // 1. الحصول على رقم جديد
            $result1 = $smartManager->getNextNumber('test', 'TEST', 3, 1);
            $testResults['step1'] = [
                'action' => 'الحصول على رقم جديد',
                'result' => $result1
            ];
            
            if ($result1['success']) {
                // 2. تأكيد الرقم
                $result2 = $smartManager->confirmNumber('test', 100, 1);
                $testResults['step2'] = [
                    'action' => 'تأكيد الرقم',
                    'result' => $result2
                ];
                
                if ($result2['success']) {
                    // 3. حذف الرقم (إعادة تدوير)
                    $result3 = $smartManager->deleteNumber('test', $result2['number'], 100, 1, 'اختبار النظام');
                    $testResults['step3'] = [
                        'action' => 'حذف الرقم وإعادة تدويره',
                        'result' => $result3
                    ];
                    
                    if ($result3['success']) {
                        // 4. الحصول على رقم مرة أخرى (يجب أن يكون نفس الرقم المحذوف)
                        $result4 = $smartManager->getNextNumber('test', 'TEST', 3, 1);
                        $testResults['step4'] = [
                            'action' => 'الحصول على رقم مرة أخرى (إعادة تدوير)',
                            'result' => $result4
                        ];
                        
                        // 5. إلغاء الحجز
                        $result5 = $smartManager->cancelReservation('test');
                        $testResults['step5'] = [
                            'action' => 'إلغاء حجز الرقم المعاد تدويره',
                            'result' => $result5
                        ];
                    }
                }
            }
            
            $message = 'تم إجراء اختبار دورة كاملة للنظام';
            $messageType = 'info';
            break;
            
        case 'test_multiple_numbers':
            // اختبار أرقام متعددة
            $testResults['title'] = 'اختبار أرقام متعددة';
            
            for ($i = 1; $i <= 5; $i++) {
                $result = $smartManager->getNextNumber('multi_test', 'MT', 3, 1);
                if ($result['success']) {
                    $confirm = $smartManager->confirmNumber('multi_test', $i * 100, 1);
                    $testResults["number_$i"] = [
                        'get' => $result,
                        'confirm' => $confirm
                    ];
                }
            }
            
            // حذف الأرقام 2 و 4
            $delete2 = $smartManager->deleteNumber('multi_test', 'MT002', 200, 1, 'اختبار حذف');
            $delete4 = $smartManager->deleteNumber('multi_test', 'MT004', 400, 1, 'اختبار حذف');
            
            $testResults['deletions'] = [
                'delete_MT002' => $delete2,
                'delete_MT004' => $delete4
            ];
            
            // الحصول على أرقام جديدة (يجب أن تعيد استخدام 2 و 4)
            $new1 = $smartManager->getNextNumber('multi_test', 'MT', 3, 1);
            $new2 = $smartManager->getNextNumber('multi_test', 'MT', 3, 1);
            
            $testResults['recycled'] = [
                'first_recycled' => $new1,
                'second_recycled' => $new2
            ];
            
            $message = 'تم اختبار الأرقام المتعددة وإعادة التدوير';
            $messageType = 'success';
            break;
            
        case 'clear_test_data':
            // مسح بيانات الاختبار
            try {
                $pdo->exec("DELETE FROM deleted_numbers_pool WHERE entity_type LIKE '%test%'");
                $pdo->exec("DELETE FROM number_usage_log WHERE entity_type LIKE '%test%'");
                $pdo->exec("DELETE FROM number_deletion_log WHERE entity_type LIKE '%test%'");
                $pdo->exec("DELETE FROM auto_numbering WHERE entity_type LIKE '%test%'");
                
                // مسح الحجوزات من الجلسة
                if (isset($_SESSION['smart_numbers']['reserved'])) {
                    foreach ($_SESSION['smart_numbers']['reserved'] as $type => $reservation) {
                        if (strpos($type, 'test') !== false) {
                            unset($_SESSION['smart_numbers']['reserved'][$type]);
                        }
                    }
                }
                
                $message = 'تم مسح جميع بيانات الاختبار';
                $messageType = 'warning';
            } catch (Exception $e) {
                $message = 'خطأ في مسح البيانات: ' . $e->getMessage();
                $messageType = 'danger';
            }
            break;
    }
}

// الحصول على الإحصائيات
$stats = $smartManager->getStatistics();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الأرقام الذكي - SeaSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .success-result { border-left: 4px solid #198754; }
        .error-result { border-left: 4px solid #dc3545; }
        .recycled-badge { background: #17a2b8; }
        .new-badge { background: #28a745; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-brain text-primary me-2"></i>اختبار نظام الأرقام الذكي
                </h1>
                
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                        <i class="fas fa-info-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- أزرار الاختبار -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-play me-2"></i>اختبارات النظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <form method="POST">
                                    <input type="hidden" name="action" value="test_full_cycle">
                                    <button type="submit" class="btn btn-success w-100">
                                        <i class="fas fa-sync me-2"></i>اختبار دورة كاملة
                                    </button>
                                </form>
                                <small class="text-muted">حجز → تأكيد → حذف → إعادة تدوير → إلغاء</small>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <form method="POST">
                                    <input type="hidden" name="action" value="test_multiple_numbers">
                                    <button type="submit" class="btn btn-info w-100">
                                        <i class="fas fa-list-ol me-2"></i>اختبار أرقام متعددة
                                    </button>
                                </form>
                                <small class="text-muted">إنشاء 5 أرقام، حذف 2، إعادة تدوير</small>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <form method="POST">
                                    <input type="hidden" name="action" value="clear_test_data">
                                    <button type="submit" class="btn btn-warning w-100">
                                        <i class="fas fa-trash me-2"></i>مسح بيانات الاختبار
                                    </button>
                                </form>
                                <small class="text-muted">حذف جميع بيانات الاختبار</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- نتائج الاختبار -->
                <?php if (!empty($testResults)): ?>
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i><?php echo $testResults['title']; ?>
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($testResults as $key => $test): ?>
                                <?php if ($key === 'title') continue; ?>
                                
                                <div class="test-result <?php echo isset($test['result']) && $test['result']['success'] ? 'success-result' : 'error-result'; ?>">
                                    <h6>
                                        <i class="fas fa-<?php echo isset($test['result']) && $test['result']['success'] ? 'check-circle text-success' : 'times-circle text-danger'; ?> me-2"></i>
                                        <?php echo isset($test['action']) ? $test['action'] : $key; ?>
                                    </h6>
                                    
                                    <?php if (isset($test['result'])): ?>
                                        <div class="mt-2">
                                            <?php if ($test['result']['success']): ?>
                                                <span class="badge bg-success me-2">نجح</span>
                                                <?php if (isset($test['result']['number'])): ?>
                                                    <span class="badge <?php echo isset($test['result']['is_recycled']) && $test['result']['is_recycled'] ? 'recycled-badge' : 'new-badge'; ?> me-2">
                                                        <?php echo $test['result']['number']; ?>
                                                        <?php if (isset($test['result']['is_recycled']) && $test['result']['is_recycled']): ?>
                                                            (معاد تدويره)
                                                        <?php else: ?>
                                                            (جديد)
                                                        <?php endif; ?>
                                                    </span>
                                                <?php endif; ?>
                                                <small class="text-muted"><?php echo $test['result']['message'] ?? ''; ?></small>
                                            <?php else: ?>
                                                <span class="badge bg-danger me-2">فشل</span>
                                                <small class="text-danger"><?php echo $test['result']['error'] ?? $test['result']['message'] ?? ''; ?></small>
                                            <?php endif; ?>
                                        </div>
                                    <?php else: ?>
                                        <!-- نتائج متعددة -->
                                        <?php foreach ($test as $subKey => $subTest): ?>
                                            <div class="ms-3 mt-2">
                                                <strong><?php echo $subKey; ?>:</strong>
                                                <?php if (isset($subTest['success'])): ?>
                                                    <span class="badge bg-<?php echo $subTest['success'] ? 'success' : 'danger'; ?> me-2">
                                                        <?php echo $subTest['success'] ? 'نجح' : 'فشل'; ?>
                                                    </span>
                                                    <?php if (isset($subTest['number'])): ?>
                                                        <span class="badge <?php echo isset($subTest['is_recycled']) && $subTest['is_recycled'] ? 'recycled-badge' : 'new-badge'; ?>">
                                                            <?php echo $subTest['number']; ?>
                                                        </span>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <pre class="small"><?php echo json_encode($subTest, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                                                <?php endif; ?>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- الإحصائيات -->
                <?php if ($stats['success']): ?>
                    <div class="card mb-4">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>إحصائيات النظام
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($stats['statistics'])): ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                    <p>لا توجد إحصائيات متاحة</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>نوع الكيان</th>
                                                <th>المستخدم</th>
                                                <th>المحذوف</th>
                                                <th>الكفاءة</th>
                                                <th>أعلى رقم</th>
                                                <th>الأرقام المحذوفة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($stats['statistics'] as $type => $stat): ?>
                                                <tr>
                                                    <td><code><?php echo htmlspecialchars($type); ?></code></td>
                                                    <td><span class="badge bg-primary"><?php echo $stat['total_used']; ?></span></td>
                                                    <td><span class="badge bg-warning"><?php echo $stat['deleted_count']; ?></span></td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $stat['efficiency'] >= 80 ? 'success' : ($stat['efficiency'] >= 60 ? 'warning' : 'danger'); ?>">
                                                            <?php echo $stat['efficiency']; ?>%
                                                        </span>
                                                    </td>
                                                    <td><?php echo $stat['highest_number']; ?></td>
                                                    <td>
                                                        <?php if (!empty($stat['deleted_numbers'])): ?>
                                                            <small class="text-muted"><?php echo $stat['deleted_numbers']; ?></small>
                                                        <?php else: ?>
                                                            <span class="text-muted">لا يوجد</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- الحجوزات الحالية -->
                <?php if (!empty($stats['current_reservations'])): ?>
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-bookmark me-2"></i>الحجوزات الحالية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>نوع الكيان</th>
                                            <th>الرقم المحجوز</th>
                                            <th>النوع</th>
                                            <th>وقت الحجز</th>
                                            <th>المدة المتبقية</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($stats['current_reservations'] as $type => $reservation): ?>
                                            <?php 
                                            $elapsed = time() - $reservation['timestamp'];
                                            $remaining = 1800 - $elapsed; // 30 دقيقة
                                            $remainingMinutes = max(0, floor($remaining / 60));
                                            ?>
                                            <tr>
                                                <td><code><?php echo htmlspecialchars($type); ?></code></td>
                                                <td><code><?php echo htmlspecialchars($reservation['full_code']); ?></code></td>
                                                <td>
                                                    <span class="badge <?php echo $reservation['is_recycled'] ? 'recycled-badge' : 'new-badge'; ?>">
                                                        <?php echo $reservation['is_recycled'] ? 'معاد تدويره' : 'جديد'; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('H:i:s', $reservation['timestamp']); ?></td>
                                                <td>
                                                    <?php if ($remaining > 0): ?>
                                                        <span class="badge bg-success"><?php echo $remainingMinutes; ?> دقيقة</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">منتهي الصلاحية</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- شرح النظام -->
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>كيف يعمل النظام الذكي؟
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>المميزات الجديدة:</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">
                                        <i class="fas fa-recycle text-success me-2"></i>
                                        <strong>إعادة التدوير:</strong> الأرقام المحذوفة تُعاد للاستخدام
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-bookmark text-info me-2"></i>
                                        <strong>الحجز المؤقت:</strong> منع فقدان الأرقام عند الإلغاء
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-chart-line text-primary me-2"></i>
                                        <strong>الكفاءة:</strong> لا توجد فجوات في الترقيم
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-history text-warning me-2"></i>
                                        <strong>السجلات:</strong> تتبع كامل لجميع العمليات
                                    </li>
                                </ul>
                            </div>
                            
                            <div class="col-md-6">
                                <h6>مثال عملي:</h6>
                                <div class="bg-light p-3 rounded">
                                    <p class="mb-2"><strong>الوضع التقليدي:</strong></p>
                                    <p class="small text-muted">CUS001, CUS002, CUS003 (حذف), CUS004, CUS005</p>
                                    <p class="small text-danger">النتيجة: فجوة في الرقم 3</p>
                                    
                                    <hr>
                                    
                                    <p class="mb-2"><strong>النظام الذكي:</strong></p>
                                    <p class="small text-muted">CUS001, CUS002, CUS003 (حذف → إعادة تدوير), CUS004</p>
                                    <p class="small text-success">العميل الجديد يحصل على CUS003 (معاد تدويره)</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <a href="customers.php" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-users me-2"></i>اختبار مع العملاء
                    </a>
                    <a href="dashboard.php" class="btn btn-secondary btn-lg">
                        <i class="fas fa-home me-2"></i>العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
