<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار هيدر لوحة التحكم - SeaSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/sidebar-only.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: '<PERSON><PERSON>e <PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* تنسيقات إضافية للهيدر الثابت */
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }

        .time-display {
            font-size: 0.85rem;
            text-align: center;
            line-height: 1.2;
        }

        .user-avatar {
            font-size: 1.5rem;
        }

        .user-info {
            text-align: right;
            line-height: 1.2;
        }

        .user-name {
            font-weight: 600;
            font-size: 0.9rem;
        }

        .user-role {
            font-size: 0.75rem;
        }

        .notification-dropdown {
            width: 350px;
            max-height: 400px;
            overflow-y: auto;
        }

        .user-dropdown {
            width: 280px;
        }

        .version-badge {
            font-size: 0.6rem;
            padding: 0.2rem 0.4rem;
        }

        .quick-controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1025;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .quick-controls .btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .quick-controls .btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }

        @media (max-width: 991.98px) {
            .search-input {
                width: 150px;
            }
            
            .time-display {
                display: none;
            }
            
            .quick-controls {
                bottom: 10px;
                right: 10px;
            }
        }

        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 0;
        }

        .sidebar .nav-link {
            color: #495057;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover {
            background-color: #f8f9fa;
            color: #667eea;
            transform: translateX(-5px);
        }

        .sidebar .nav-link.active {
            background-color: #667eea;
            color: white;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
            border: none;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
    <!-- الهيدر الثابت المحسن -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <!-- العلامة التجارية -->
            <a class="navbar-brand d-flex align-items-center" href="dashboard.php">
                <i class="fas fa-anchor me-2"></i>
                <span class="brand-text">SeaSystem</span>
                <small class="version-badge ms-2 badge bg-light text-dark">v1.0.0</small>
            </a>
            
            <!-- زر التبديل للموبايل -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- قائمة التنقل -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- القائمة الرئيسية -->
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    
                    <!-- قائمة التقارير -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-line me-1"></i>
                            <span>التقارير</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="reports.php">
                                <i class="fas fa-chart-bar me-2"></i>التقارير الرئيسية
                            </a></li>
                            <li><a class="dropdown-item" href="report_trial_balance.php">
                                <i class="fas fa-balance-scale me-2"></i>ميزان المراجعة
                            </a></li>
                        </ul>
                    </li>
                    
                    <!-- الوصول السريع -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bolt me-1"></i>
                            <span>وصول سريع</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="customers.php">
                                <i class="fas fa-users me-2"></i>العملاء
                            </a></li>
                            <li><a class="dropdown-item" href="suppliers.php">
                                <i class="fas fa-truck me-2"></i>الموردين
                            </a></li>
                            <li><a class="dropdown-item" href="invoices.php">
                                <i class="fas fa-file-invoice me-2"></i>الفواتير
                            </a></li>
                        </ul>
                    </li>
                </ul>
                
                <!-- أدوات الهيدر -->
                <ul class="navbar-nav">
                    <!-- البحث السريع -->
                    <li class="nav-item me-2">
                        <form class="d-flex" role="search">
                            <div class="input-group input-group-sm">
                                <input class="form-control search-input" type="search" placeholder="بحث سريع...">
                                <button class="btn btn-outline-light" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </li>
                    
                    <!-- الإشعارات -->
                    <li class="nav-item dropdown me-2">
                        <a class="nav-link position-relative notification-badge" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">3</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                            <li><h6 class="dropdown-header">الإشعارات الحديثة</h6></li>
                            <li><a class="dropdown-item" href="#">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-info-circle text-info"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-2">
                                        <h6 class="mb-1">فاتورة جديدة</h6>
                                        <p class="mb-1 small">تم إنشاء فاتورة رقم #1001</p>
                                        <small class="text-muted">منذ 5 دقائق</small>
                                    </div>
                                </div>
                            </a></li>
                        </ul>
                    </li>
                    
                    <!-- الوقت والتاريخ -->
                    <li class="nav-item me-3">
                        <span class="navbar-text time-display">
                            <i class="fas fa-clock me-1"></i>
                            <span id="current-time">14:30</span>
                            <br>
                            <small id="current-date">2025-06-24</small>
                        </span>
                    </li>
                    
                    <!-- قائمة المستخدم -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                            <div class="user-avatar me-2">
                                <i class="fas fa-user-circle fa-lg"></i>
                            </div>
                            <div class="user-info d-none d-md-block">
                                <div class="user-name">مدير النظام</div>
                                <small class="user-role text-light opacity-75">admin</small>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end user-dropdown">
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="settings.php">
                                <i class="fas fa-cog me-2"></i>الإعدادات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- أزرار التحكم السريع -->
    <div class="quick-controls">
        <button class="btn btn-primary btn-sm" onclick="HeaderUtils.scrollToTop()" title="العودة للأعلى">
            <i class="fas fa-arrow-up"></i>
        </button>
        <button class="btn btn-secondary btn-sm" onclick="HeaderUtils.toggleHeader()" title="إخفاء/إظهار الهيدر">
            <i class="fas fa-eye-slash"></i>
        </button>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-3 col-lg-2 sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link active" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                    </a>
                    <a class="nav-link" href="customers.php">
                        <i class="fas fa-users me-2"></i>العملاء
                    </a>
                    <a class="nav-link" href="suppliers.php">
                        <i class="fas fa-truck me-2"></i>الموردين
                    </a>
                    <a class="nav-link" href="invoices.php">
                        <i class="fas fa-file-invoice me-2"></i>الفواتير
                    </a>
                    <a class="nav-link" href="inventory.php">
                        <i class="fas fa-boxes me-2"></i>المخزون
                    </a>
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i>التقارير
                    </a>
                </nav>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- بطاقة الترحيب -->
                <div class="welcome-card">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">مرحباً، مدير النظام!</h2>
                            <p class="mb-0">مرحباً بك في نظام SeaSystem المحاسبي. إليك نظرة سريعة على أداء أعمالك اليوم.</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <i class="fas fa-chart-line fa-4x opacity-50"></i>
                        </div>
                    </div>
                </div>

                <!-- بطاقات الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-primary me-3">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0">150</h3>
                                    <p class="text-muted mb-0">العملاء</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-success me-3">
                                    <i class="fas fa-file-invoice"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0">89</h3>
                                    <p class="text-muted mb-0">الفواتير</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-warning me-3">
                                    <i class="fas fa-truck"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0">45</h3>
                                    <p class="text-muted mb-0">الموردين</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-info me-3">
                                    <i class="fas fa-boxes"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0">234</h3>
                                    <p class="text-muted mb-0">المنتجات</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- محتوى إضافي للاختبار -->
                <div class="row">
                    <?php for($i = 1; $i <= 20; $i++): ?>
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">بطاقة اختبار رقم <?php echo $i; ?></h5>
                                <p class="card-text">
                                    هذا محتوى تجريبي لاختبار الهيدر الثابت. 
                                    قم بالتمرير لأعلى وأسفل لرؤية كيف يتفاعل الهيدر مع الحركة.
                                </p>
                                <div class="d-flex gap-2">
                                    <span class="badge bg-primary">تجريبي</span>
                                    <span class="badge bg-success">نشط</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endfor; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
    <script>
        // تحديث الوقت والتاريخ
        function updateDateTime() {
            const now = new Date();
            const timeElement = document.getElementById('current-time');
            const dateElement = document.getElementById('current-date');
            
            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString('ar-SA', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }
            
            if (dateElement) {
                dateElement.textContent = now.toLocaleDateString('ar-SA');
            }
        }

        // تحديث كل دقيقة
        setInterval(updateDateTime, 60000);
        updateDateTime(); // تحديث فوري

        // تأثيرات بصرية للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
</body>
</html>
