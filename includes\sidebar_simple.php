<?php
/**
 * SeaSystem - دالة بسيطة لإضافة تصميم الشريط الجانبي
 * Simple Sidebar Design Function
 */

function addSidebarCSS() {
    echo '<link href="assets/css/sidebar-only.css" rel="stylesheet">';
}

function renderSimpleSidebar($current_page = '') {
    $menu_items = [
        'dashboard.php' => [
            'icon' => 'fas fa-tachometer-alt',
            'title' => 'لوحة التحكم'
        ],
        'accounts.php' => [
            'icon' => 'fas fa-list',
            'title' => 'دليل الحسابات'
        ],
        'customers.php' => [
            'icon' => 'fas fa-users',
            'title' => 'العملاء'
        ],
        'suppliers.php' => [
            'icon' => 'fas fa-truck',
            'title' => 'الموردين'
        ],
        'inventory.php' => [
            'icon' => 'fas fa-boxes',
            'title' => 'المخزون والمنتجات'
        ],
        'invoices.php' => [
            'icon' => 'fas fa-file-invoice',
            'title' => 'الفواتير'
        ],
        'payments.php' => [
            'icon' => 'fas fa-credit-card',
            'title' => 'المدفوعات'
        ],
        'journal.php' => [
            'icon' => 'fas fa-book',
            'title' => 'دفتر اليومية'
        ],
        'reports.php' => [
            'icon' => 'fas fa-chart-bar',
            'title' => 'التقارير'
        ]
    ];

    echo '<div class="col-md-3 col-lg-2 sidebar">';
    echo '<nav class="nav flex-column">';

    foreach ($menu_items as $page => $item) {
        $active_class = '';

        // تحديد الصفحة النشطة
        if ($current_page == $page ||
            (empty($current_page) && $page == 'dashboard.php') ||
            (strpos($_SERVER['PHP_SELF'], $page) !== false)) {
            $active_class = 'active';
        }

        echo '<a class="nav-link ' . $active_class . '" href="' . $page . '">';
        echo '<i class="' . $item['icon'] . ' me-2"></i>' . $item['title'];
        echo '</a>';
    }

    echo '</nav>';
    echo '</div>';
}
?>
