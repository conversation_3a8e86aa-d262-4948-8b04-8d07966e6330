<?php
/**
 * SeaSystem - الهيدر الموحد لجميع الصفحات
 * Universal Header for All Pages
 */

if (!isset($_SESSION)) {
    session_start();
}

// التحقق من تسجيل الدخول
$is_logged_in = isset($_SESSION['user_id']);
$current_user = null;

if ($is_logged_in) {
    require_once __DIR__ . '/../config/config.php';
    require_once __DIR__ . '/auth.php';
    $current_user = getCurrentUser();
}
?>

<!-- الهيدر الثابت الموحد -->
<nav class="navbar navbar-expand-lg navbar-dark navbar-fixed navbar-default"
     style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.6) !important;
            position: fixed !important;
            top: 0 !important;
            width: 100% !important;
            z-index: 1030 !important;">
    <div class="container-fluid">
        <!-- العلامة التجارية -->
        <a class="navbar-brand d-flex align-items-center" href="<?php echo $is_logged_in ? 'dashboard.php' : 'index.php'; ?>">
            <i class="fas fa-anchor me-2"></i>
            <span class="brand-text"><?php echo defined('SITE_NAME') ? SITE_NAME : 'SeaSystem'; ?></span>
            <small class="version-badge ms-2 badge bg-light text-dark">v<?php echo defined('SITE_VERSION') ? SITE_VERSION : '1.0.0'; ?></small>
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-1"></i>
                        لوحة التحكم
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-chart-line me-1"></i>
                        التقارير
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="reports.php">التقارير الرئيسية</a></li>
                        <li><a class="dropdown-item" href="report_trial_balance.php">ميزان المراجعة</a></li>
                        <li><a class="dropdown-item" href="report_income_statement.php">قائمة الدخل</a></li>
                        <li><a class="dropdown-item" href="report_balance_sheet.php">الميزانية العمومية</a></li>
                    </ul>
                </li>
            </ul>

            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        <?php echo htmlspecialchars($current_user['full_name'] ?? $current_user['username']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#profileModal">
                            <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                            <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- نموذج الملف الشخصي -->
<div class="modal fade" id="profileModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">الملف الشخصي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($current_user['username']); ?></p>
                        <p><strong>الاسم الكامل:</strong> <?php echo htmlspecialchars($current_user['full_name']); ?></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($current_user['email']); ?></p>
                        <p><strong>الدور:</strong> <?php echo htmlspecialchars($current_user['role']); ?></p>
                    </div>
                </div>
                <p><strong>تاريخ الإنشاء:</strong> <?php echo date('Y-m-d H:i', strtotime($current_user['created_at'])); ?></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تغيير كلمة المرور -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تغيير كلمة المرور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="change_password.php">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="old_password" class="form-label">كلمة المرور الحالية</label>
                        <input type="password" class="form-control" id="old_password" name="old_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تغيير كلمة المرور</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- ملف ألوان الهيدر المحسن -->
<link href="assets/css/header-colors.css" rel="stylesheet">

<style>
    body {
        padding-top: 76px; /* لتجنب تداخل المحتوى مع الشريط العلوي الثابت */
    }

    /* ضمان ظهور الألوان في جميع الصفحات */
    .navbar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.6) !important;
    }
</style>
