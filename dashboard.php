<?php
/**
 * SeaSystem - لوحة التحكم الرئيسية
 * Main Dashboard
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/includes/sidebar.php';
require_once __DIR__ . '/classes/Account.php';
require_once __DIR__ . '/classes/Customer.php';
require_once __DIR__ . '/classes/Invoice.php';

// التأكد من تسجيل الدخول
requireLogin();

// إنشاء كائنات الفئات
$account = new Account();
$customer = new Customer();
$invoice = new Invoice();

// الحصول على بيانات المستخدم الحالي
$current_user = getCurrentUser();

// التحقق من وجود بيانات المستخدم
if (!$current_user) {
    // إعادة توجيه إلى صفحة تسجيل الدخول إذا لم توجد بيانات المستخدم
    header('Location: login.php?error=session_expired');
    exit();
}

// الحصول على الإحصائيات
$invoice_stats = $invoice->getStatistics();
$total_customers = count($customer->getAll());
$total_accounts = count($account->getAll());

// الحصول على آخر الفواتير
$recent_invoices = $invoice->getAll(['limit' => 5]);

// الحصول على الفواتير المستحقة
$overdue_invoices = $invoice->getAll([
    'status' => 'sent',
    'limit' => 5
]);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/sidebar-only.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* تنسيقات إضافية للهيدر الثابت */
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }

        .time-display {
            font-size: 0.85rem;
            text-align: center;
            line-height: 1.2;
        }

        .user-avatar {
            font-size: 1.5rem;
        }

        .user-info {
            text-align: right;
            line-height: 1.2;
        }

        .user-name {
            font-weight: 600;
            font-size: 0.9rem;
        }

        .user-role {
            font-size: 0.75rem;
        }

        .notification-dropdown {
            width: 350px;
            max-height: 400px;
            overflow-y: auto;
        }

        .user-dropdown {
            width: 280px;
        }

        .version-badge {
            font-size: 0.6rem;
            padding: 0.2rem 0.4rem;
        }

        .quick-controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1025;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .quick-controls .btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .quick-controls .btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }

        @media (max-width: 991.98px) {
            .search-input {
                width: 150px;
            }

            .time-display {
                display: none;
            }

            .quick-controls {
                bottom: 10px;
                right: 10px;
            }
        }

        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 0;
        }

        .sidebar .nav-link {
            color: #495057;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover {
            background-color: #f8f9fa;
            color: #667eea;
            transform: translateX(-5px);
        }

        .sidebar .nav-link.active {
            background-color: #667eea;
            color: white;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
            border: none;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .sidebar {
            background: #f8f9fa;
            min-height: calc(100vh - 76px);
            padding: 1rem 0;
            border-left: 1px solid #dee2e6;
        }

        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin: 0.2rem 0.5rem;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateX(5px);
        }

        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }

        .main-content {
            padding: 2rem;
        }

        .stat-icon.primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-icon.success { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
        .stat-icon.warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stat-icon.info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }

        .table-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .table-card .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 1.5rem;
        }

        .badge-status {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
        }

        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        /* تأثيرات تفاعلية للأزرار */
        .quick-action-btn {
            transition: all 0.3s ease;
            transform: translateY(0);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .quick-action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        /* تأثيرات مخصصة لكل لون */
        .btn-primary.quick-action-btn:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            border-color: #004085;
        }

        .btn-success.quick-action-btn:hover {
            background: linear-gradient(135deg, #157347 0%, #0f5132 100%);
            border-color: #0f5132;
        }

        .btn-warning.quick-action-btn:hover {
            background: linear-gradient(135deg, #e0a800 0%, #b08800 100%);
            border-color: #b08800;
        }

        .btn-secondary.quick-action-btn:hover {
            background: linear-gradient(135deg, #495057 0%, #343a40 100%);
            border-color: #343a40;
        }

        .btn-dark.quick-action-btn:hover {
            background: linear-gradient(135deg, #1c1f23 0%, #0c0d0f 100%);
            border-color: #0c0d0f;
        }

        .btn-info.quick-action-btn:hover {
            background: linear-gradient(135deg, #0dcaf0 0%, #087990 100%);
            border-color: #087990;
        }

        .btn-outline-primary.quick-action-btn:hover {
            background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
            border-color: #0056b3;
            color: white;
        }

        /* تأثير النبضة للأزرار المهمة */
        .btn-primary.quick-action-btn {
            animation: pulse-primary 2s infinite;
        }

        @keyframes pulse-primary {
            0% {
                box-shadow: 0 4px 8px rgba(0,0,0,0.1), 0 0 0 0 rgba(13, 110, 253, 0.4);
            }
            70% {
                box-shadow: 0 4px 8px rgba(0,0,0,0.1), 0 0 0 10px rgba(13, 110, 253, 0);
            }
            100% {
                box-shadow: 0 4px 8px rgba(0,0,0,0.1), 0 0 0 0 rgba(13, 110, 253, 0);
            }
        }

        /* إيقاف النبضة عند التمرير */
        .btn-primary.quick-action-btn:hover {
            animation: none;
        }



        /* تنسيقات الهيدر الثابت */
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }

        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }

        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
        </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- الهيدر الثابت المحسن -->
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <!-- أزرار التحكم السريع -->
    <div class="quick-controls">
        <button class="btn btn-primary btn-sm" onclick="HeaderUtils.scrollToTop()" title="العودة للأعلى">
            <i class="fas fa-arrow-up"></i>
        </button>
        <button class="btn btn-secondary btn-sm" onclick="HeaderUtils.toggleHeader()" title="إخفاء/إظهار الهيدر">
            <i class="fas fa-eye-slash"></i>
        </button>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي الموحد -->
            <?php renderSidebar('dashboard.php'); ?>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- بطاقة الترحيب -->
                <div class="welcome-card">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">مرحباً، <?php echo htmlspecialchars($current_user['full_name'] ?? $current_user['username'] ?? 'مستخدم'); ?>!</h2>
                            <p class="mb-0">مرحباً بك في نظام SeaSystem المحاسبي. إليك نظرة سريعة على أداء أعمالك اليوم.</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <i class="fas fa-chart-line fa-4x opacity-50"></i>
                        </div>
                    </div>
                </div>

                <!-- بطاقات الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon primary">
                                    <i class="fas fa-file-invoice"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format($invoice_stats['total_invoices'] ?? 0); ?></h3>
                                    <p class="text-muted mb-0">إجمالي الفواتير</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon success">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format($invoice_stats['total_paid'] ?? 0, 2); ?></h3>
                                    <p class="text-muted mb-0">المبالغ المحصلة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format($invoice_stats['total_outstanding'] ?? 0, 2); ?></h3>
                                    <p class="text-muted mb-0">المبالغ المستحقة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon info">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo $total_customers; ?></h3>
                                    <p class="text-muted mb-0">العملاء</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- آخر الفواتير -->
                    <div class="col-lg-8 mb-4">
                        <div class="table-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-file-invoice me-2"></i>آخر الفواتير
                                </h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>رقم الفاتورة</th>
                                                <th>العميل</th>
                                                <th>التاريخ</th>
                                                <th>المبلغ</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($recent_invoices)): ?>
                                                <tr>
                                                    <td colspan="5" class="text-center py-4">
                                                        <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                                        <p class="text-muted mb-0">لا توجد فواتير حتى الآن</p>
                                                    </td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($recent_invoices as $inv): ?>
                                                    <tr>
                                                        <td>
                                                            <a href="invoice_view.php?id=<?php echo $inv['id']; ?>" class="text-decoration-none">
                                                                <?php echo $inv['invoice_number']; ?>
                                                            </a>
                                                        </td>
                                                        <td><?php echo $inv['customer_name'] ?? $inv['supplier_name']; ?></td>
                                                        <td><?php echo date('Y-m-d', strtotime($inv['invoice_date'])); ?></td>
                                                        <td><?php echo number_format($inv['total_amount'], 2) . ' ' . CURRENCY_SYMBOL; ?></td>
                                                        <td>
                                                            <?php
                                                            $status_class = '';
                                                            $status_text = '';
                                                            switch ($inv['status']) {
                                                                case 'draft':
                                                                    $status_class = 'bg-secondary';
                                                                    $status_text = 'مسودة';
                                                                    break;
                                                                case 'sent':
                                                                    $status_class = 'bg-warning';
                                                                    $status_text = 'مرسلة';
                                                                    break;
                                                                case 'paid':
                                                                    $status_class = 'bg-success';
                                                                    $status_text = 'مدفوعة';
                                                                    break;
                                                                case 'overdue':
                                                                    $status_class = 'bg-danger';
                                                                    $status_text = 'متأخرة';
                                                                    break;
                                                                case 'cancelled':
                                                                    $status_class = 'bg-dark';
                                                                    $status_text = 'ملغية';
                                                                    break;
                                                            }
                                                            ?>
                                                            <span class="badge badge-status <?php echo $status_class; ?>">
                                                                <?php echo $status_text; ?>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="card-footer text-center">
                                <a href="invoices.php" class="btn btn-outline-primary">
                                    <i class="fas fa-eye me-2"></i>عرض جميع الفواتير
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- الإجراءات السريعة -->
                    <div class="col-lg-4 mb-4">
                        <div class="table-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-bolt me-2"></i>الإجراءات السريعة
                                </h5>
                            </div>
                            <div class="card-body pt-4">
                                <div class="d-grid gap-3">
                                    <a href="invoice_create.php" class="btn btn-primary py-3 quick-action-btn">
                                        <i class="fas fa-plus me-2"></i>إنشاء فاتورة جديدة
                                    </a>
                                    <a href="customers.php#add-customer" class="btn btn-success py-3 quick-action-btn" onclick="openAddCustomerModal()">
                                        <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                                    </a>
                                    <a href="suppliers.php#add-supplier" class="btn btn-warning py-3 quick-action-btn" onclick="openAddSupplierModal()">
                                        <i class="fas fa-truck me-2"></i>إضافة مورد
                                    </a>
                                    <a href="inventory.php#add-product-tab" class="btn btn-secondary py-3 quick-action-btn" onclick="openAddProductTab()">
                                        <i class="fas fa-box me-2"></i>إضافة منتج
                                    </a>
                                    <a href="journal_entry_create.php" class="btn btn-dark py-3 quick-action-btn">
                                        <i class="fas fa-book me-2"></i>إنشاء قيد محاسبي
                                    </a>
                                    <a href="payment_create.php" class="btn btn-info py-3 quick-action-btn">
                                        <i class="fas fa-credit-card me-2"></i>تسجيل دفعة
                                    </a>
                                    <a href="reports.php" class="btn btn-outline-primary py-3 quick-action-btn">
                                        <i class="fas fa-chart-bar me-2"></i>عرض التقارير
                                    </a>
                                </div>

                                <hr>

                                <h6 class="mb-3">معلومات النظام</h6>
                                <div class="small text-muted">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>إجمالي الحسابات:</span>
                                        <strong><?php echo number_format($total_accounts); ?></strong>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>إجمالي العملاء:</span>
                                        <strong><?php echo number_format($total_customers); ?></strong>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>آخر تسجيل دخول:</span>
                                        <strong><?php echo date('Y-m-d H:i'); ?></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
    <script>
        // تحديث الوقت كل ثانية
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');
            const dateString = now.toLocaleDateString('ar-SA');

            // يمكن إضافة عنصر لعرض الوقت إذا لزم الأمر
        }

        // تشغيل تحديث الوقت
        setInterval(updateTime, 1000);

        // تأثيرات بصرية للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });

        // تحديث الوقت والتاريخ
        function updateDateTime() {
            const now = new Date();
            const timeElement = document.getElementById('current-time');
            const dateElement = document.getElementById('current-date');

            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString('ar-SA', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }

            if (dateElement) {
                dateElement.textContent = now.toLocaleDateString('ar-SA');
            }
        }

        // تحديث كل دقيقة
        setInterval(updateDateTime, 60000);
        updateDateTime(); // تحديث فوري

        // دالة فتح نافذة إضافة العميل
        function openAddCustomerModal() {
            // التوجه إلى صفحة العملاء مع فتح النافذة المنبثقة
            window.location.href = 'customers.php';
            // إضافة معرف للنافذة في الرابط
            setTimeout(function() {
                if (typeof bootstrap !== 'undefined') {
                    const modal = new bootstrap.Modal(document.getElementById('addCustomerModal'));
                    modal.show();
                }
            }, 500);
        }

        // دالة فتح نافذة إضافة المورد
        function openAddSupplierModal() {
            // التوجه إلى صفحة الموردين مع فتح النافذة المنبثقة
            window.location.href = 'suppliers.php';
            // إضافة معرف للنافذة في الرابط
            setTimeout(function() {
                if (typeof bootstrap !== 'undefined') {
                    const modal = new bootstrap.Modal(document.getElementById('addSupplierModal'));
                    modal.show();
                }
            }, 500);
        }

        // دالة فتح تبويب إضافة منتج
        function openAddProductTab() {
            // التوجه إلى صفحة المخزون مع فتح تبويب إضافة منتج
            window.location.href = 'inventory.php#add-product-tab';
        }
    </script>
</body>
</html>
