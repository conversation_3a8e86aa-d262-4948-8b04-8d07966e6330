<?php
/**
 * SeaSystem - تهيئة نظام الترقيم التلقائي
 * Setup Automatic Numbering System
 */

require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/classes/NumberGenerator.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تهيئة نظام الترقيم - SeaSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .setup-step {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        .step-success { border-color: #198754; background: #d1e7dd; }
        .step-warning { border-color: #ffc107; background: #fff3cd; }
        .step-danger { border-color: #dc3545; background: #f8d7da; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-cogs text-primary me-2"></i>تهيئة نظام الترقيم التلقائي
                </h1>
                
                <?php
                try {
                    $numberGenerator = new NumberGenerator();
                    
                    echo '<div class="setup-step step-warning">';
                    echo '<h4><i class="fas fa-database me-2"></i>إنشاء جدول الترقيم...</h4>';
                    echo '<p>جاري إنشاء جدول auto_numbering وتهيئة البيانات الأساسية...</p>';
                    echo '</div>';
                    
                    // مزامنة الترقيم مع البيانات الموجودة
                    echo '<div class="setup-step step-warning">';
                    echo '<h4><i class="fas fa-sync me-2"></i>مزامنة الترقيم مع البيانات الموجودة...</h4>';
                    
                    $sync_result = $numberGenerator->syncNumbering();
                    if ($sync_result['success']) {
                        echo '<p class="text-success">✅ ' . $sync_result['message'] . '</p>';
                    } else {
                        echo '<p class="text-danger">❌ ' . $sync_result['message'] . '</p>';
                    }
                    echo '</div>';
                    
                    // عرض إحصائيات الترقيم
                    echo '<div class="setup-step step-success">';
                    echo '<h4><i class="fas fa-chart-bar me-2"></i>إحصائيات الترقيم الحالية:</h4>';
                    
                    $stats = $numberGenerator->getNumberingStats();
                    
                    if (!empty($stats)) {
                        echo '<table class="table table-bordered table-sm">';
                        echo '<thead class="table-light">';
                        echo '<tr><th>نوع الكيان</th><th>البادئة</th><th>آخر رقم</th><th>الرقم التالي</th><th>آخر تحديث</th></tr>';
                        echo '</thead>';
                        echo '<tbody>';
                        
                        $entity_names = [
                            'customer' => 'العملاء',
                            'supplier' => 'الموردين',
                            'product' => 'المنتجات',
                            'warehouse' => 'المستودعات',
                            'invoice_sales' => 'فواتير المبيعات',
                            'invoice_purchase' => 'فواتير المشتريات',
                            'payment' => 'المدفوعات',
                            'journal' => 'القيود المحاسبية'
                        ];
                        
                        foreach ($stats as $stat) {
                            $entity_name = $entity_names[$stat['entity_type']] ?? $stat['entity_type'];
                            $next_number = $stat['last_number'] + 1;
                            
                            echo '<tr>';
                            echo '<td><strong>' . $entity_name . '</strong></td>';
                            echo '<td><code>' . $stat['prefix'] . '</code></td>';
                            echo '<td>' . $stat['last_number'] . '</td>';
                            echo '<td><span class="badge bg-primary">' . $stat['prefix'] . str_pad($next_number, 3, '0', STR_PAD_LEFT) . '</span></td>';
                            echo '<td>' . date('Y-m-d H:i', strtotime($stat['updated_at'])) . '</td>';
                            echo '</tr>';
                        }
                        
                        echo '</tbody>';
                        echo '</table>';
                    } else {
                        echo '<p class="text-warning">⚠️ لا توجد بيانات ترقيم</p>';
                    }
                    
                    echo '</div>';
                    
                    // اختبار توليد الأرقام
                    echo '<div class="setup-step step-success">';
                    echo '<h4><i class="fas fa-vial me-2"></i>اختبار توليد الأرقام:</h4>';
                    
                    $test_numbers = [
                        'عميل جديد' => $numberGenerator->getNextNumber('customer', 'CUS', 3),
                        'مورد جديد' => $numberGenerator->getNextNumber('supplier', 'SUP', 3),
                        'منتج جديد' => $numberGenerator->getNextNumber('product', 'PRD', 3),
                        'مستودع جديد' => $numberGenerator->getNextNumber('warehouse', 'WH', 3),
                        'فاتورة مبيعات' => $numberGenerator->getNextNumber('invoice_sales', 'SALES' . date('Ym'), 4),
                        'فاتورة مشتريات' => $numberGenerator->getNextNumber('invoice_purchase', 'PURCH' . date('Ym'), 4),
                        'مقبوض' => $numberGenerator->getNextNumber('payment', 'REC' . date('Ym'), 3),
                        'قيد محاسبي' => $numberGenerator->getNextNumber('journal', 'JE' . date('Ym'), 4)
                    ];
                    
                    echo '<div class="row">';
                    foreach ($test_numbers as $type => $number) {
                        echo '<div class="col-md-3 mb-2">';
                        echo '<div class="text-center p-2 border rounded">';
                        echo '<small class="text-muted">' . $type . '</small><br>';
                        echo '<strong class="text-primary">' . $number . '</strong>';
                        echo '</div>';
                        echo '</div>';
                    }
                    echo '</div>';
                    
                    echo '</div>';
                    
                    // النتيجة النهائية
                    echo '<div class="setup-step step-success">';
                    echo '<h3><i class="fas fa-check-circle me-2"></i>تم تهيئة نظام الترقيم بنجاح!</h3>';
                    echo '<ul>';
                    echo '<li>✅ تم إنشاء جدول الترقيم التلقائي</li>';
                    echo '<li>✅ تم مزامنة الترقيم مع البيانات الموجودة</li>';
                    echo '<li>✅ تم اختبار توليد الأرقام</li>';
                    echo '<li>✅ النظام جاهز للاستخدام</li>';
                    echo '</ul>';
                    
                    echo '<div class="alert alert-info mt-3">';
                    echo '<h5><i class="fas fa-lightbulb me-2"></i>الآن يمكنك:</h5>';
                    echo '<ul class="mb-0">';
                    echo '<li>إضافة عملاء جدد بأرقام تبدأ من CUS001</li>';
                    echo '<li>إضافة موردين جدد بأرقام تبدأ من SUP001</li>';
                    echo '<li>إضافة منتجات جديدة بأرقام تبدأ من PRD001</li>';
                    echo '<li>إضافة مستودعات جديدة بأرقام تبدأ من WH001</li>';
                    echo '<li>جميع الأرقام ستكون متسلسلة ومنظمة</li>';
                    echo '</ul>';
                    echo '</div>';
                    echo '</div>';
                    
                } catch (Exception $e) {
                    echo '<div class="setup-step step-danger">';
                    echo '<h4><i class="fas fa-exclamation-triangle me-2"></i>خطأ في التهيئة</h4>';
                    echo '<p class="text-danger">❌ ' . $e->getMessage() . '</p>';
                    echo '</div>';
                }
                ?>
                
                <div class="text-center mt-4">
                    <a href="customers.php" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-users me-2"></i>اختبار إضافة عميل
                    </a>
                    <a href="suppliers.php" class="btn btn-success btn-lg me-3">
                        <i class="fas fa-truck me-2"></i>اختبار إضافة مورد
                    </a>
                    <a href="inventory.php" class="btn btn-warning btn-lg me-3">
                        <i class="fas fa-boxes me-2"></i>اختبار إضافة منتج
                    </a>
                    <a href="dashboard.php" class="btn btn-secondary btn-lg">
                        <i class="fas fa-home me-2"></i>العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
