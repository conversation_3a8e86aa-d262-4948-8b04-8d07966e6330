<?php
/**
 * SeaSystem - استعادة الحماية بكلمة المرور
 * Restore Password Security
 */

echo "<h1>🔒 استعادة الحماية بكلمة المرور</h1>";
echo "<hr>";

try {
    // 1. استعادة التحقق من كلمة المرور في auth.php
    echo "<h3>1. استعادة التحقق من كلمة المرور</h3>";
    
    $auth_file = 'includes/auth.php';
    if (file_exists($auth_file)) {
        $content = file_get_contents($auth_file);
        
        // استبدال الكود المؤقت بالكود الأصلي
        $original_password_check = '                if (password_verify($password, $user[\'password\'])) {
                    // مسح محاولات الدخول الفاشلة
                    $this->rateLimiter->clearAttempts($username);
                    
                    // إنشاء جلسة المستخدم
                    $_SESSION[\'user_id\'] = $user[\'id\'];
                    $_SESSION[\'username\'] = $user[\'username\'];
                    $_SESSION[\'full_name\'] = $user[\'full_name\'];
                    $_SESSION[\'role\'] = $user[\'role\'];
                    $_SESSION[\'login_time\'] = time();
                    $_SESSION[\'last_activity\'] = time();

                    // تسجيل عملية الدخول
                    $this->logActivity($user[\'id\'], \'login\', \'تسجيل دخول ناجح\');
                    Security::logSecurityEvent(\'login_success\', "User: {$user[\'username\']}", $user[\'id\']);

                    return [
                        \'success\' => true,
                        \'message\' => \'تم تسجيل الدخول بنجاح\',
                        \'user\' => $user
                    ];
                } else {
                    // تسجيل محاولة فاشلة
                    $this->rateLimiter->recordFailedAttempt($username);
                    Security::logSecurityEvent(\'login_failed\', "Username: $username, Reason: Invalid password");
                    
                    return [
                        \'success\' => false,
                        \'message\' => \'كلمة المرور غير صحيحة\'
                    ];
                }';
        
        // البحث عن الكود المؤقت واستبداله
        $temp_code_pattern = '/\/\/ تسجيل دخول مؤقت بدون كلمة مرور.*?return \[.*?\];/s';
        
        if (preg_match($temp_code_pattern, $content)) {
            $content = preg_replace($temp_code_pattern, $original_password_check, $content);
            file_put_contents($auth_file, $content);
            echo "✅ تم استعادة التحقق من كلمة المرور في auth.php<br>";
        } else {
            echo "ℹ️ لم يتم العثور على الكود المؤقت في auth.php<br>";
        }
    }
    
    // 2. استعادة التحقق من كلمة المرور في login.php
    echo "<h3>2. استعادة نموذج تسجيل الدخول</h3>";
    
    $login_file = 'login.php';
    if (file_exists($login_file)) {
        $content = file_get_contents($login_file);
        
        // استعادة التحقق من كلمة المرور
        $content = str_replace(
            'if (empty($username)) {
            $error_message = \'يرجى إدخال اسم المستخدم\';',
            'if (empty($username) || empty($password)) {
            $error_message = \'يرجى إدخال اسم المستخدم وكلمة المرور\';',
            $content
        );
        
        // إزالة تنبيه وضع التطوير
        $content = preg_replace(
            '/<!-- تنبيه وضع التطوير -->.*?<\/div>/s',
            '',
            $content
        );
        
        // استعادة حقل كلمة المرور المطلوب
        $content = str_replace(
            '<label for="password" class="form-label">كلمة المرور <small class="text-muted">(اختياري في وضع التطوير)</small></label>',
            '<label for="password" class="form-label">كلمة المرور</label>',
            $content
        );
        
        $content = str_replace(
            'placeholder="اتركه فارغاً في وضع التطوير">',
            'required placeholder="أدخل كلمة المرور">',
            $content
        );
        
        file_put_contents($login_file, $content);
        echo "✅ تم استعادة نموذج تسجيل الدخول<br>";
    }
    
    echo "<hr>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>🔒 تم استعادة الحماية بنجاح!</h4>";
    echo "<p style='margin: 0; color: #155724;'>النظام الآن يتطلب كلمة مرور صحيحة لتسجيل الدخول.</p>";
    echo "</div>";
    
    echo "<br><div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>";
    echo "<h4 style='color: #856404; margin: 0 0 10px 0;'>📋 بيانات تسجيل الدخول:</h4>";
    echo "<ul style='margin: 0; color: #856404;'>";
    echo "<li><strong>اسم المستخدم:</strong> admin</li>";
    echo "<li><strong>كلمة المرور:</strong> SeaAdmin@2025!</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h4 style='color: #721c24; margin: 0 0 10px 0;'>❌ خطأ في الاستعادة</h4>";
    echo "<p style='margin: 0; color: #721c24;'>حدث خطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<br><a href='login.php' class='btn btn-primary'>تسجيل الدخول</a>";
echo " <a href='dashboard.php' class='btn btn-success'>لوحة التحكم</a>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background: #f8f9fa;
    direction: rtl;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 500;
    color: white;
}

.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn:hover { opacity: 0.9; text-decoration: none; }

h1 { color: #343a40; }
h3 { color: #495057; }
</style>
