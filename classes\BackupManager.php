<?php
/**
 * SeaSystem - مدير النسخ الاحتياطي
 * Backup Manager Class
 */

class BackupManager {
    private $db;
    private $backupDir;
    
    public function __construct() {
        require_once __DIR__ . '/../config/database.php';
        $database = new Database();
        $this->db = $database->getConnection();
        
        // إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
        $this->backupDir = __DIR__ . '/../storage/backups/';
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
    }
    
    /**
     * إنشاء نسخة احتياطية كاملة من قاعدة البيانات
     */
    public function createFullBackup($description = '') {
        try {
            $timestamp = date('Y-m-d_H-i-s');
            $filename = "seasystem_backup_$timestamp.sql";
            $filepath = $this->backupDir . $filename;
            
            // الحصول على قائمة الجداول
            $tables = $this->getAllTables();
            
            $sql = "-- SeaSystem Database Backup\n";
            $sql .= "-- Created: " . date('Y-m-d H:i:s') . "\n";
            $sql .= "-- Description: $description\n\n";
            $sql .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";
            
            foreach ($tables as $table) {
                $sql .= $this->backupTable($table);
            }
            
            $sql .= "\nSET FOREIGN_KEY_CHECKS = 1;\n";
            
            // حفظ النسخة الاحتياطية
            if (file_put_contents($filepath, $sql)) {
                // تسجيل النسخة الاحتياطية في قاعدة البيانات
                $this->logBackup($filename, filesize($filepath), 'full', $description);
                
                return [
                    'success' => true,
                    'filename' => $filename,
                    'filepath' => $filepath,
                    'size' => filesize($filepath)
                ];
            } else {
                return ['success' => false, 'message' => 'فشل في حفظ النسخة الاحتياطية'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في إنشاء النسخة الاحتياطية: ' . $e->getMessage()];
        }
    }
    
    /**
     * إنشاء نسخة احتياطية من جدول محدد
     */
    public function backupTable($tableName) {
        try {
            $sql = "\n-- Table: $tableName\n";
            
            // الحصول على هيكل الجدول
            $stmt = $this->db->query("SHOW CREATE TABLE `$tableName`");
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $sql .= "DROP TABLE IF EXISTS `$tableName`;\n";
            $sql .= $row['Create Table'] . ";\n\n";
            
            // الحصول على البيانات
            $stmt = $this->db->query("SELECT * FROM `$tableName`");
            $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($rows)) {
                $columns = array_keys($rows[0]);
                $sql .= "INSERT INTO `$tableName` (`" . implode('`, `', $columns) . "`) VALUES\n";
                
                $values = [];
                foreach ($rows as $row) {
                    $rowValues = [];
                    foreach ($row as $value) {
                        if ($value === null) {
                            $rowValues[] = 'NULL';
                        } else {
                            $rowValues[] = "'" . addslashes($value) . "'";
                        }
                    }
                    $values[] = '(' . implode(', ', $rowValues) . ')';
                }
                
                $sql .= implode(",\n", $values) . ";\n\n";
            }
            
            return $sql;
            
        } catch (Exception $e) {
            return "-- خطأ في نسخ الجدول $tableName: " . $e->getMessage() . "\n\n";
        }
    }
    
    /**
     * الحصول على قائمة جميع الجداول
     */
    private function getAllTables() {
        $tables = [];
        $stmt = $this->db->query("SHOW TABLES");
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $tables[] = $row[0];
        }
        return $tables;
    }
    
    /**
     * تسجيل النسخة الاحتياطية في قاعدة البيانات
     */
    private function logBackup($filename, $size, $type, $description) {
        try {
            // إنشاء جدول سجل النسخ الاحتياطي إذا لم يكن موجوداً
            $sql = "CREATE TABLE IF NOT EXISTS backup_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                filename VARCHAR(255) NOT NULL,
                file_size BIGINT NOT NULL,
                backup_type ENUM('full', 'partial', 'table') DEFAULT 'full',
                description TEXT,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                restored_at TIMESTAMP NULL,
                is_deleted BOOLEAN DEFAULT FALSE
            )";
            
            $this->db->exec($sql);
            
            // تسجيل النسخة الاحتياطية
            $stmt = $this->db->prepare("INSERT INTO backup_logs (filename, file_size, backup_type, description, created_by) VALUES (?, ?, ?, ?, ?)");
            $userId = $_SESSION['user_id'] ?? null;
            $stmt->execute([$filename, $size, $type, $description, $userId]);
            
        } catch (Exception $e) {
            // تجاهل أخطاء التسجيل
        }
    }
    
    /**
     * الحصول على قائمة النسخ الاحتياطية
     */
    public function getBackupList() {
        try {
            $stmt = $this->db->query("SELECT * FROM backup_logs WHERE is_deleted = 0 ORDER BY created_at DESC");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * استعادة نسخة احتياطية
     */
    public function restoreBackup($filename) {
        try {
            $filepath = $this->backupDir . $filename;
            
            if (!file_exists($filepath)) {
                return ['success' => false, 'message' => 'ملف النسخة الاحتياطية غير موجود'];
            }
            
            $sql = file_get_contents($filepath);
            
            // تنفيذ النسخة الاحتياطية
            $this->db->exec($sql);
            
            // تحديث سجل الاستعادة
            $stmt = $this->db->prepare("UPDATE backup_logs SET restored_at = NOW() WHERE filename = ?");
            $stmt->execute([$filename]);
            
            return ['success' => true, 'message' => 'تم استعادة النسخة الاحتياطية بنجاح'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في استعادة النسخة الاحتياطية: ' . $e->getMessage()];
        }
    }
    
    /**
     * حذف نسخة احتياطية
     */
    public function deleteBackup($filename) {
        try {
            $filepath = $this->backupDir . $filename;
            
            if (file_exists($filepath)) {
                unlink($filepath);
            }
            
            // تحديث السجل
            $stmt = $this->db->prepare("UPDATE backup_logs SET is_deleted = 1 WHERE filename = ?");
            $stmt->execute([$filename]);
            
            return ['success' => true, 'message' => 'تم حذف النسخة الاحتياطية'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في حذف النسخة الاحتياطية: ' . $e->getMessage()];
        }
    }
    
    /**
     * تنظيف النسخ الاحتياطية القديمة
     */
    public function cleanupOldBackups($daysToKeep = 30) {
        try {
            $cutoffDate = date('Y-m-d H:i:s', strtotime("-$daysToKeep days"));
            
            // الحصول على النسخ القديمة
            $stmt = $this->db->prepare("SELECT filename FROM backup_logs WHERE created_at < ? AND is_deleted = 0");
            $stmt->execute([$cutoffDate]);
            $oldBackups = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $deletedCount = 0;
            foreach ($oldBackups as $filename) {
                $result = $this->deleteBackup($filename);
                if ($result['success']) {
                    $deletedCount++;
                }
            }
            
            return [
                'success' => true,
                'message' => "تم حذف $deletedCount نسخة احتياطية قديمة"
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في تنظيف النسخ الاحتياطية: ' . $e->getMessage()];
        }
    }
    
    /**
     * الحصول على حجم مجلد النسخ الاحتياطي
     */
    public function getBackupDirectorySize() {
        $size = 0;
        $files = glob($this->backupDir . '*');
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $size += filesize($file);
            }
        }
        
        return $size;
    }
    
    /**
     * تحويل الحجم إلى تنسيق قابل للقراءة
     */
    public static function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }
}
?>
