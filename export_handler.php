<?php
/**
 * SeaSystem - معالج التصدير
 * Export Handler
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/ExportManager.php';

// التأكد من تسجيل الدخول
requireLogin();

// التحقق من المعاملات المطلوبة
if (!isset($_GET['type']) || !isset($_GET['format'])) {
    die('معاملات غير صحيحة');
}

$type = $_GET['type'];
$format = $_GET['format'];

try {
    $data = [];
    $headers = [];
    $filename = '';
    $title = '';
    
    switch ($type) {
        case 'customers':
            require_once __DIR__ . '/classes/Customer.php';
            $customer = new Customer();
            $customers = $customer->getAll();
            
            $headers = ['الرقم', 'الكود', 'الاسم', 'البريد الإلكتروني', 'الهاتف', 'العنوان', 'الرصيد الافتتاحي', 'الرصيد الحالي', 'حالة النشاط', 'تاريخ الإنشاء'];
            $fieldsMap = [
                'id' => 'الرقم',
                'customer_code' => 'الكود',
                'name' => 'الاسم',
                'email' => 'البريد الإلكتروني',
                'phone' => 'الهاتف',
                'address' => 'العنوان',
                'opening_balance' => 'الرصيد الافتتاحي',
                'current_balance' => 'الرصيد الحالي',
                'is_active' => 'حالة النشاط',
                'created_at' => 'تاريخ الإنشاء'
            ];
            
            $data = ExportManager::prepareDataForExport($customers, $fieldsMap);
            $filename = ExportManager::getSafeFilename('customers_report');
            $title = 'تقرير العملاء';
            break;
            
        case 'suppliers':
            require_once __DIR__ . '/classes/Supplier.php';
            $supplier = new Supplier();
            $suppliers = $supplier->getAll();
            
            $headers = ['الرقم', 'الكود', 'الاسم', 'البريد الإلكتروني', 'الهاتف', 'العنوان', 'الرقم الضريبي', 'الرصيد الافتتاحي', 'الرصيد الحالي', 'حالة النشاط', 'تاريخ الإنشاء'];
            $fieldsMap = [
                'id' => 'الرقم',
                'supplier_code' => 'الكود',
                'name' => 'الاسم',
                'email' => 'البريد الإلكتروني',
                'phone' => 'الهاتف',
                'address' => 'العنوان',
                'tax_number' => 'الرقم الضريبي',
                'opening_balance' => 'الرصيد الافتتاحي',
                'current_balance' => 'الرصيد الحالي',
                'is_active' => 'حالة النشاط',
                'created_at' => 'تاريخ الإنشاء'
            ];
            
            $data = ExportManager::prepareDataForExport($suppliers, $fieldsMap);
            $filename = ExportManager::getSafeFilename('suppliers_report');
            $title = 'تقرير الموردين';
            break;
            
        case 'products':
            require_once __DIR__ . '/classes/Product.php';
            $product = new Product();
            $products = $product->getAll();
            
            $headers = ['الرقم', 'الكود', 'الاسم', 'الفئة', 'الوحدة', 'سعر الشراء', 'سعر البيع', 'الكمية', 'الحد الأدنى', 'حالة النشاط', 'تاريخ الإنشاء'];
            $fieldsMap = [
                'id' => 'الرقم',
                'product_code' => 'الكود',
                'name' => 'الاسم',
                'category' => 'الفئة',
                'unit' => 'الوحدة',
                'purchase_price' => 'سعر الشراء',
                'sale_price' => 'سعر البيع',
                'quantity' => 'الكمية',
                'min_quantity' => 'الحد الأدنى',
                'is_active' => 'حالة النشاط',
                'created_at' => 'تاريخ الإنشاء'
            ];
            
            $data = ExportManager::prepareDataForExport($products, $fieldsMap);
            $filename = ExportManager::getSafeFilename('products_report');
            $title = 'تقرير المنتجات';
            break;
            
        case 'invoices':
            require_once __DIR__ . '/classes/Invoice.php';
            $invoice = new Invoice();
            $invoices = $invoice->getAll();
            
            $headers = ['الرقم', 'رقم الفاتورة', 'النوع', 'العميل/المورد', 'التاريخ', 'المبلغ الإجمالي', 'الضريبة', 'الخصم', 'الصافي', 'الحالة', 'تاريخ الإنشاء'];
            $fieldsMap = [
                'id' => 'الرقم',
                'invoice_number' => 'رقم الفاتورة',
                'type' => 'النوع',
                'customer_supplier' => 'العميل/المورد',
                'invoice_date' => 'التاريخ',
                'total_amount' => 'المبلغ الإجمالي',
                'tax_amount' => 'الضريبة',
                'discount_amount' => 'الخصم',
                'net_amount' => 'الصافي',
                'status' => 'الحالة',
                'created_at' => 'تاريخ الإنشاء'
            ];
            
            $data = ExportManager::prepareDataForExport($invoices, $fieldsMap);
            $filename = ExportManager::getSafeFilename('invoices_report');
            $title = 'تقرير الفواتير';
            break;
            
        case 'trial_balance':
            require_once __DIR__ . '/classes/Account.php';
            $account = new Account();
            $accounts = $account->getTrialBalance();
            
            $headers = ['رقم الحساب', 'اسم الحساب', 'النوع', 'مدين', 'دائن', 'الرصيد'];
            $fieldsMap = [
                'account_code' => 'رقم الحساب',
                'account_name' => 'اسم الحساب',
                'account_type' => 'النوع',
                'debit' => 'مدين',
                'credit' => 'دائن',
                'balance' => 'الرصيد'
            ];
            
            $data = ExportManager::prepareDataForExport($accounts, $fieldsMap);
            $filename = ExportManager::getSafeFilename('trial_balance');
            $title = 'ميزان المراجعة';
            break;
            
        default:
            die('نوع التقرير غير مدعوم');
    }
    
    // التصدير حسب التنسيق المطلوب
    switch ($format) {
        case 'csv':
            ExportManager::exportToCSV($data, $filename, $headers);
            break;
            
        case 'excel':
            ExportManager::exportToExcel($data, $filename, $headers, $title);
            break;
            
        case 'pdf':
            ExportManager::exportToPDF($data, $filename, $headers, $title);
            break;
            
        default:
            die('تنسيق التصدير غير مدعوم');
    }
    
} catch (Exception $e) {
    die('خطأ في التصدير: ' . $e->getMessage());
}
?>
