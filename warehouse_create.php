<?php
/**
 * SeaSystem - إضافة مستودع جديد
 * Add New Warehouse
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/Inventory.php';
require_once __DIR__ . '/classes/NumberGenerator.php';

// التأكد من تسجيل الدخول
requireLogin();

// إنشاء كائن المخزون
$inventory = new Inventory();

// الحصول على بيانات المستخدم الحالي
$current_user = getCurrentUser();

$message = '';
$message_type = '';

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $warehouse_data = [
        'warehouse_code' => strtoupper(trim($_POST['warehouse_code'])),
        'warehouse_name' => trim($_POST['warehouse_name']),
        'location' => trim($_POST['location']),
        'is_active' => isset($_POST['is_active']) ? 1 : 0,
        'created_by' => $current_user['id']
    ];

    $result = $inventory->addWarehouse($warehouse_data);
    $message = $result['message'];
    $message_type = $result['success'] ? 'success' : 'danger';

    if ($result['success']) {
        // إعادة توجيه إلى صفحة المخزون
        header('Location: inventory.php?success=' . urlencode($message) . '#warehouses-tab');
        exit();
    }
}

// توليد رمز مستودع تلقائي
$numberGenerator = new NumberGenerator();
$next_warehouse_code = $numberGenerator->getNextNumber('warehouse', 'WH', 3);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة مستودع جديد - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">

    <style>
        body { padding-top: 80px !important; }
        .warehouse-card { background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.08); padding: 2rem; margin-bottom: 2rem; }
        .form-floating > label { color: #6c757d; }
        .warehouse-icon { background: linear-gradient(45deg, #667eea, #764ba2); color: white; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12 p-4">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="page-title">
                                <i class="fas fa-warehouse me-2"></i>إضافة مستودع جديد
                            </h1>
                            <p class="page-subtitle">إضافة مستودع جديد لإدارة المخزون</p>
                        </div>
                        <div class="col-auto">
                            <a href="inventory.php#warehouses-tab" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-2"></i>العودة للمستودعات
                            </a>
                        </div>
                    </div>
                </div>

                <!-- الرسائل -->
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- نموذج إضافة المستودع -->
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="warehouse-card">
                            <div class="text-center mb-4">
                                <div class="warehouse-icon">
                                    <i class="fas fa-warehouse fa-2x"></i>
                                </div>
                                <h3>معلومات المستودع الجديد</h3>
                                <p class="text-muted">أدخل بيانات المستودع الجديد</p>
                            </div>

                            <form method="POST" id="warehouseForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="warehouse_code" name="warehouse_code"
                                                   value="<?php echo $next_warehouse_code; ?>" required>
                                            <label for="warehouse_code">رمز المستودع *</label>
                                        </div>
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            رمز فريد للمستودع (مثل: WH001, MAIN, STORE1)
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="warehouse_name" name="warehouse_name" required>
                                            <label for="warehouse_name">اسم المستودع *</label>
                                        </div>
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            الاسم الكامل للمستودع
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-floating">
                                        <textarea class="form-control" id="location" name="location" style="height: 100px"></textarea>
                                        <label for="location">الموقع/العنوان</label>
                                    </div>
                                    <div class="form-text">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        عنوان أو موقع المستودع (اختياري)
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                        <label class="form-check-label" for="is_active">
                                            <i class="fas fa-toggle-on me-2"></i>مستودع نشط
                                        </label>
                                    </div>
                                    <div class="form-text">
                                        المستودعات النشطة فقط تظهر في قوائم الاختيار
                                    </div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="inventory.php#warehouses-tab" class="btn btn-outline-secondary me-md-2">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>حفظ المستودع
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="warehouse-card bg-light">
                            <h5><i class="fas fa-lightbulb me-2"></i>نصائح مهمة:</h5>
                            <ul class="mb-0">
                                <li><strong>رمز المستودع:</strong> يجب أن يكون فريداً ولا يمكن تغييره لاحقاً</li>
                                <li><strong>اسم المستودع:</strong> اختر اسماً واضحاً ومفهوماً</li>
                                <li><strong>الموقع:</strong> يساعد في تحديد المستودع بسهولة</li>
                                <li><strong>الحالة:</strong> يمكن تعطيل المستودع مؤقتاً دون حذفه</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>

    <script>
        // تحسين تجربة المستخدم
        document.getElementById('warehouseForm').addEventListener('submit', function(e) {
            const submitButton = this.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
                submitButton.disabled = true;
            }
        });

        // توليد رمز تلقائي من الاسم
        document.getElementById('warehouse_name').addEventListener('input', function() {
            const name = this.value.trim();
            if (name) {
                // إنشاء رمز من أول حرفين من كل كلمة
                const words = name.split(' ');
                let code = '';
                words.forEach(word => {
                    if (word.length > 0) {
                        code += word.charAt(0).toUpperCase();
                    }
                });

                // إضافة أرقام عشوائية إذا كان الرمز قصيراً
                if (code.length < 2) {
                    code = 'WH' + Math.floor(Math.random() * 100);
                } else if (code.length < 4) {
                    code += Math.floor(Math.random() * 100);
                }

                document.getElementById('warehouse_code').value = code.substring(0, 10);
            }
        });

        // التحقق من صحة الرمز
        document.getElementById('warehouse_code').addEventListener('input', function() {
            this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
        });
    </script>
</body>
</html>
