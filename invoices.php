<?php
/**
 * SeaSystem - صفحة إدارة الفواتير
 * Invoices Management Page
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/includes/sidebar.php';
require_once __DIR__ . '/classes/Invoice.php';
require_once __DIR__ . '/classes/Customer.php';

// التأكد من تسجيل الدخول
requireLogin();

$invoice = new Invoice();
$customer = new Customer();
$current_user = getCurrentUser();

// معالجة العمليات
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'delete_invoice':
                $result = $invoice->delete($_POST['invoice_id']);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;
        }
    }
}

// الحصول على قائمة الفواتير
$filters = [];
if (!empty($_GET['type'])) {
    $filters['invoice_type'] = $_GET['type'];
}
if (!empty($_GET['status'])) {
    $filters['status'] = $_GET['status'];
}
if (!empty($_GET['date_from'])) {
    $filters['date_from'] = $_GET['date_from'];
}
if (!empty($_GET['date_to'])) {
    $filters['date_to'] = $_GET['date_to'];
}

$invoices = $invoice->getAll($filters);

// البحث
$search_term = $_GET['search'] ?? '';
if (!empty($search_term)) {
    $invoices = $invoice->search($search_term, $filters);
}

// إحصائيات الفواتير
$stats = $invoice->getStatistics($filters);

// قائمة العملاء للفلترة
$customers = $customer->getAll();

// أنواع وحالات الفواتير
$invoice_types = [
    'sales' => 'فاتورة مبيعات',
    'purchase' => 'فاتورة مشتريات'
];

$invoice_statuses = [
    'draft' => 'مسودة',
    'sent' => 'مرسلة',
    'paid' => 'مدفوعة',
    'overdue' => 'متأخرة',
    'cancelled' => 'ملغية'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفواتير - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <link href="assets/css/sidebar-only.css" rel="stylesheet">
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي الموحد -->
            <?php renderSidebar('invoices.php'); ?>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="page-title">
                                <i class="fas fa-file-invoice me-2"></i>إدارة الفواتير
                            </h1>
                            <p class="page-subtitle">إنشاء وإدارة فواتير المبيعات والمشتريات</p>
                        </div>
                        <div class="col-auto">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="createInvoice('sales')">
                                    <i class="fas fa-plus me-2"></i>فاتورة مبيعات
                                </button>
                                <button type="button" class="btn btn-outline-info btn-sm" onclick="createInvoice('purchase')">
                                    <i class="fas fa-plus me-2"></i>فاتورة مشتريات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسائل -->
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- إحصائيات سريعة -->
                <div class="row mb-5">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon primary">
                                    <i class="fas fa-file-invoice"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format($stats['total_invoices'] ?? 0); ?></h3>
                                    <p class="text-muted mb-0">إجمالي الفواتير</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon success">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format($stats['total_paid'] ?? 0, 0); ?></h3>
                                    <p class="text-muted mb-0">المبالغ المحصلة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format($stats['total_outstanding'] ?? 0, 0); ?></h3>
                                    <p class="text-muted mb-0">المبالغ المستحقة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon info">
                                    <i class="fas fa-calculator"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format($stats['average_amount'] ?? 0, 0); ?></h3>
                                    <p class="text-muted mb-0">متوسط الفاتورة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- شريط البحث والفلترة -->
                <div class="card mb-4 mt-4">
                    <div class="card-body">
                        <!-- البحث الديناميكي -->
                        <div class="row g-3 mb-3">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="invoiceSearch"
                                           placeholder="البحث السريع في الفواتير..."
                                           value="<?php echo htmlspecialchars($search_term); ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-outline-secondary w-100" id="clearInvoiceSearch">
                                    <i class="fas fa-times me-2"></i>مسح البحث
                                </button>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-outline-info w-100" id="toggleInvoiceFilters">
                                    <i class="fas fa-filter me-2"></i>فلاتر متقدمة
                                </button>
                            </div>
                        </div>

                        <!-- الفلاتر المتقدمة (مخفية افتراضياً) -->
                        <div id="advancedInvoiceFilters" class="border-top pt-3" style="display: none;">
                            <form method="GET" class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">نوع الفاتورة</label>
                                    <select class="form-select" name="type">
                                        <option value="">جميع الأنواع</option>
                                        <?php foreach ($invoice_types as $type => $type_name): ?>
                                            <option value="<?php echo $type; ?>" <?php echo ($_GET['type'] ?? '') == $type ? 'selected' : ''; ?>><?php echo $type_name; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">حالة الفاتورة</label>
                                    <select class="form-select" name="status">
                                        <option value="">جميع الحالات</option>
                                        <?php foreach ($invoice_statuses as $status => $status_name): ?>
                                            <option value="<?php echo $status; ?>" <?php echo ($_GET['status'] ?? '') == $status ? 'selected' : ''; ?>><?php echo $status_name; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" name="date_from"
                                           value="<?php echo $_GET['date_from'] ?? ''; ?>">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" name="date_to"
                                           value="<?php echo $_GET['date_to'] ?? ''; ?>">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="submit" class="btn btn-primary w-100 d-block">
                                        <i class="fas fa-search me-2"></i>تطبيق الفلاتر
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- جدول الفواتير -->
                <div class="table-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-invoice me-2"></i>قائمة الفواتير (<?php echo count($invoices); ?>)
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="invoicesTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>النوع</th>
                                        <th>العميل/المورد</th>
                                        <th>التاريخ</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>المبلغ المدفوع</th>
                                        <th>المتبقي</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($invoices)): ?>
                                        <tr>
                                            <td colspan="9" class="text-center py-4">
                                                <i class="fas fa-file-invoice fa-2x text-muted mb-2"></i>
                                                <p class="text-muted mb-0">لا توجد فواتير حتى الآن</p>
                                                <div class="mt-2">
                                                    <button type="button" class="btn btn-success me-2" onclick="createInvoice('sales')">
                                                        إنشاء فاتورة مبيعات
                                                    </button>
                                                    <button type="button" class="btn btn-info" onclick="createInvoice('purchase')">
                                                        إنشاء فاتورة مشتريات
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($invoices as $inv): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($inv['invoice_number']); ?></strong>
                                                </td>
                                                <td>
                                                    <span class="badge <?php echo $inv['invoice_type'] == 'sales' ? 'bg-success' : 'bg-info'; ?>">
                                                        <?php echo $invoice_types[$inv['invoice_type']]; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars($inv['customer_name'] ?? $inv['supplier_name'] ?? '-'); ?></td>
                                                <td><?php echo date('Y-m-d', strtotime($inv['invoice_date'])); ?></td>
                                                <td><?php echo number_format($inv['total_amount'], 2) . ' ' . CURRENCY_SYMBOL; ?></td>
                                                <td><?php echo number_format($inv['paid_amount'], 2) . ' ' . CURRENCY_SYMBOL; ?></td>
                                                <td>
                                                    <?php
                                                    $remaining = $inv['total_amount'] - $inv['paid_amount'];
                                                    $remaining_class = $remaining > 0 ? 'text-danger' : 'text-success';
                                                    ?>
                                                    <span class="<?php echo $remaining_class; ?>">
                                                        <?php echo number_format($remaining, 2) . ' ' . CURRENCY_SYMBOL; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status_class = '';
                                                    switch ($inv['status']) {
                                                        case 'draft':
                                                            $status_class = 'bg-secondary';
                                                            break;
                                                        case 'sent':
                                                            $status_class = 'bg-warning';
                                                            break;
                                                        case 'paid':
                                                            $status_class = 'bg-success';
                                                            break;
                                                        case 'overdue':
                                                            $status_class = 'bg-danger';
                                                            break;
                                                        case 'cancelled':
                                                            $status_class = 'bg-dark';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>">
                                                        <?php echo $invoice_statuses[$inv['status']]; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="d-flex gap-2">
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                                    onclick="viewInvoice(<?php echo $inv['id']; ?>)">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">عرض</small>
                                                        </div>
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-success btn-sm"
                                                                    onclick="editInvoice(<?php echo $inv['id']; ?>)">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">تعديل</small>
                                                        </div>
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-info btn-sm"
                                                                    onclick="printInvoice(<?php echo $inv['id']; ?>)">
                                                                <i class="fas fa-print"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">طباعة</small>
                                                        </div>
                                                        <?php if ($inv['status'] != 'paid'): ?>
                                                            <div class="text-center">
                                                                <button type="button" class="btn btn-outline-warning btn-sm"
                                                                        onclick="recordPayment(<?php echo $inv['id']; ?>)">
                                                                    <i class="fas fa-money-bill"></i>
                                                                </button>
                                                                <small class="d-block text-muted mt-1">دفعة</small>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        // إنشاء فاتورة جديدة
        function createInvoice(type) {
            window.location.href = `invoice_create.php?type=${type}`;
        }

        // عرض الفاتورة
        function viewInvoice(invoiceId) {
            window.location.href = `invoice_view.php?id=${invoiceId}`;
        }

        // تعديل الفاتورة
        function editInvoice(invoiceId) {
            window.location.href = `invoice_edit.php?id=${invoiceId}`;
        }

        // طباعة الفاتورة
        function printInvoice(invoiceId) {
            window.open(`invoice_print.php?id=${invoiceId}`, '_blank');
        }

        // حذف الفاتورة
        function deleteInvoice(invoiceId) {
            if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_invoice">
                    <input type="hidden" name="invoice_id" value="${invoiceId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // تسجيل دفعة
        function recordPayment(invoiceId) {
            const amount = prompt('أدخل مبلغ الدفعة:');
            if (amount && !isNaN(amount) && parseFloat(amount) > 0) {
                window.location.href = `payment_create.php?invoice_id=${invoiceId}&amount=${amount}`;
            }
        }

        // إظهار/إخفاء الفلاتر المتقدمة
        document.getElementById('toggleInvoiceFilters').addEventListener('click', function() {
            const filtersDiv = document.getElementById('advancedInvoiceFilters');
            const isVisible = filtersDiv.style.display !== 'none';

            if (isVisible) {
                filtersDiv.style.display = 'none';
                this.innerHTML = '<i class="fas fa-filter me-2"></i>فلاتر متقدمة';
            } else {
                filtersDiv.style.display = 'block';
                this.innerHTML = '<i class="fas fa-times me-2"></i>إخفاء الفلاتر';
            }
        });

        // البحث الديناميكي في الفواتير
        const invoiceSearchInput = document.getElementById('invoiceSearch');
        const clearInvoiceSearchBtn = document.getElementById('clearInvoiceSearch');
        const invoiceTableBody = document.querySelector('#invoicesTable tbody');
        let searchTimeout;

        // البحث عند الكتابة
        invoiceSearchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const searchTerm = this.value.trim();

            searchTimeout = setTimeout(() => {
                performInvoiceSearch(searchTerm);
            }, 300); // انتظار 300 ملي ثانية بعد انتهاء الكتابة
        });

        // مسح البحث
        clearInvoiceSearchBtn.addEventListener('click', function() {
            invoiceSearchInput.value = '';
            performInvoiceSearch('');
        });

        // تنفيذ البحث
        function performInvoiceSearch(searchTerm) {
            // إظهار مؤشر التحميل
            invoiceTableBody.innerHTML = '<tr><td colspan="9" class="text-center py-4"><i class="fas fa-spinner fa-spin fa-2x text-muted"></i><p class="text-muted mt-2">جاري البحث...</p></td></tr>';

            // إرسال طلب AJAX
            fetch(`ajax_search_invoices.php?search=${encodeURIComponent(searchTerm)}`)
                .then(response => response.text())
                .then(data => {
                    invoiceTableBody.innerHTML = data;
                })
                .catch(error => {
                    console.error('خطأ في البحث:', error);
                    invoiceTableBody.innerHTML = '<tr><td colspan="9" class="text-center py-4 text-danger"><i class="fas fa-exclamation-triangle fa-2x mb-2"></i><p>حدث خطأ في البحث</p></td></tr>';
                });
        }
    
        // تحديث الوقت والتاريخ
        function updateDateTime() {
            const now = new Date();
            const timeElement = document.getElementById('current-time');
            const dateElement = document.getElementById('current-date');
            
            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString('ar-SA', {
                    hour: '2-digit', minute: '2-digit'
                });
            }
            if (dateElement) {
                dateElement.textContent = now.toLocaleDateString('ar-SA');
            }
        }
        setInterval(updateDateTime, 60000);
        updateDateTime();
    </script>
</body>
</html>
