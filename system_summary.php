<?php
/**
 * ملخص شامل للنظام والبيانات المدخلة
 */

require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/classes/Supplier.php';
require_once __DIR__ . '/classes/Customer.php';
require_once __DIR__ . '/classes/Product.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    echo "<h1>🎯 ملخص شامل لنظام SeaSystem - محدث</h1>";
    echo "<p style='color: #666; font-size: 14px;'>تاريخ التحديث: " . date('Y-m-d H:i:s') . "</p>";
    echo "<hr>";

    // إحصائيات الموردين
    echo "<h2>🏢 الموردين</h2>";
    $supplier = new Supplier();
    $suppliers = $supplier->getAll();
    echo "<strong>عدد الموردين:</strong> " . count($suppliers) . "<br>";

    $total_supplier_balance = 0;
    foreach ($suppliers as $sup) {
        $total_supplier_balance += $sup['current_balance'];
    }
    echo "<strong>إجمالي أرصدة الموردين:</strong> " . number_format($total_supplier_balance, 2) . " ج.م<br>";

    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f8f9fa;'><th>رمز المورد</th><th>الاسم</th><th>الرصيد الحالي</th><th>الحالة</th></tr>";
    foreach ($suppliers as $sup) {
        echo "<tr>";
        echo "<td>" . $sup['supplier_code'] . "</td>";
        echo "<td>" . $sup['name'] . "</td>";
        echo "<td>" . number_format($sup['current_balance'], 2) . " ج.م</td>";
        echo "<td>" . ($sup['is_active'] ? 'نشط' : 'غير نشط') . "</td>";
        echo "</tr>";
    }
    echo "</table>";

    // إحصائيات العملاء
    echo "<h2>👥 العملاء</h2>";
    $customer = new Customer();
    $customers = $customer->getAll();
    echo "<strong>عدد العملاء:</strong> " . count($customers) . "<br>";

    $total_customer_balance = 0;
    foreach ($customers as $cust) {
        $total_customer_balance += $cust['current_balance'];
    }
    echo "<strong>إجمالي أرصدة العملاء:</strong> " . number_format($total_customer_balance, 2) . " ج.م<br>";

    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f8f9fa;'><th>رمز العميل</th><th>الاسم</th><th>الرصيد الحالي</th><th>حد الائتمان</th></tr>";
    foreach ($customers as $cust) {
        echo "<tr>";
        echo "<td>" . $cust['customer_code'] . "</td>";
        echo "<td>" . $cust['name'] . "</td>";
        echo "<td>" . number_format($cust['current_balance'], 2) . " ج.م</td>";
        echo "<td>" . number_format($cust['credit_limit'], 2) . " ج.م</td>";
        echo "</tr>";
    }
    echo "</table>";

    // إحصائيات المنتجات
    echo "<h2>📦 المنتجات</h2>";
    $product = new Product();
    $products = $product->getAll();
    echo "<strong>عدد المنتجات:</strong> " . count($products) . "<br>";

    $total_stock_value = 0;
    $total_quantity = 0;
    foreach ($products as $prod) {
        $total_stock_value += ($prod['cost_price'] * $prod['current_stock']);
        $total_quantity += $prod['current_stock'];
    }
    echo "<strong>إجمالي قيمة المخزون:</strong> " . number_format($total_stock_value, 2) . " ج.م<br>";
    echo "<strong>إجمالي الكميات:</strong> " . $total_quantity . " قطعة<br>";

    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f8f9fa;'><th>رمز المنتج</th><th>الاسم</th><th>الكمية</th><th>سعر الشراء</th><th>سعر البيع</th><th>قيمة المخزون</th></tr>";
    foreach ($products as $prod) {
        $stock_value = $prod['cost_price'] * $prod['current_stock'];
        echo "<tr>";
        echo "<td>" . $prod['product_code'] . "</td>";
        echo "<td>" . $prod['product_name'] . "</td>";
        echo "<td>" . $prod['current_stock'] . "</td>";
        echo "<td>" . number_format($prod['cost_price'], 2) . " ج.م</td>";
        echo "<td>" . number_format($prod['unit_price'], 2) . " ج.م</td>";
        echo "<td>" . number_format($stock_value, 2) . " ج.م</td>";
        echo "</tr>";
    }
    echo "</table>";

    // إحصائيات الفواتير
    echo "<h2>🧾 الفواتير</h2>";
    $sql = "SELECT invoice_type, COUNT(*) as count, SUM(total_amount) as total FROM invoices GROUP BY invoice_type";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $invoice_stats = $stmt->fetchAll();

    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f8f9fa;'><th>نوع الفاتورة</th><th>العدد</th><th>إجمالي القيمة</th></tr>";
    foreach ($invoice_stats as $stat) {
        $type_name = ($stat['invoice_type'] == 'purchase') ? 'فواتير شراء' : 'فواتير بيع';
        echo "<tr>";
        echo "<td>" . $type_name . "</td>";
        echo "<td>" . $stat['count'] . "</td>";
        echo "<td>" . number_format($stat['total'], 2) . " ج.م</td>";
        echo "</tr>";
    }
    echo "</table>";

    // إحصائيات المدفوعات
    echo "<h2>💰 المدفوعات</h2>";
    $sql = "SELECT payment_type, COUNT(*) as count, SUM(amount) as total FROM payments GROUP BY payment_type";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $payment_stats = $stmt->fetchAll();

    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f8f9fa;'><th>نوع المدفوعة</th><th>العدد</th><th>إجمالي المبلغ</th></tr>";
    foreach ($payment_stats as $stat) {
        $type_name = ($stat['payment_type'] == 'payment_voucher') ? 'سندات صرف' : 'سندات قبض';
        echo "<tr>";
        echo "<td>" . $type_name . "</td>";
        echo "<td>" . $stat['count'] . "</td>";
        echo "<td>" . number_format($stat['total'], 2) . " ج.م</td>";
        echo "</tr>";
    }
    echo "</table>";

    // إحصائيات قيود اليومية
    echo "<h2>📋 قيود اليومية</h2>";
    $sql = "SELECT COUNT(*) as count, SUM(total_amount) as total FROM journal_entries";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $journal_stats = $stmt->fetch();

    echo "<strong>عدد القيود:</strong> " . $journal_stats['count'] . "<br>";
    echo "<strong>إجمالي قيمة القيود:</strong> " . number_format($journal_stats['total'], 2) . " ج.م<br>";

    // ملخص مالي
    echo "<h2>📊 الملخص المالي</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f8f9fa;'><th>البيان</th><th>المبلغ (ج.م)</th></tr>";
    echo "<tr><td>إجمالي أرصدة الموردين</td><td>" . number_format($total_supplier_balance, 2) . "</td></tr>";
    echo "<tr><td>إجمالي أرصدة العملاء</td><td>" . number_format($total_customer_balance, 2) . "</td></tr>";
    echo "<tr><td>إجمالي قيمة المخزون</td><td>" . number_format($total_stock_value, 2) . "</td></tr>";

    $total_purchases = 0;
    $total_sales = 0;
    foreach ($invoice_stats as $stat) {
        if ($stat['invoice_type'] == 'purchase') {
            $total_purchases = $stat['total'];
        } elseif ($stat['invoice_type'] == 'sale') {
            $total_sales = $stat['total'];
        }
    }
    echo "<tr><td>إجمالي المشتريات</td><td>" . number_format($total_purchases, 2) . "</td></tr>";
    echo "<tr><td>إجمالي المبيعات</td><td>" . number_format($total_sales, 2) . "</td></tr>";
    echo "<tr style='background-color: #e8f5e8;'><td><strong>صافي الربح</strong></td><td><strong>" . number_format($total_sales - $total_purchases, 2) . "</strong></td></tr>";
    echo "</table>";

    echo "<hr>";
    echo "<h2>✅ حالة النظام</h2>";
    echo "<p style='color: green; font-size: 18px;'><strong>النظام يعمل بشكل مثالي! 🎉</strong></p>";
    echo "<p>تم اختبار جميع الوحدات بنجاح:</p>";
    echo "<ul>";
    echo "<li>✅ إدارة الموردين والعملاء</li>";
    echo "<li>✅ إدارة المنتجات والمخزون</li>";
    echo "<li>✅ فواتير الشراء والبيع</li>";
    echo "<li>✅ سندات القبض والصرف</li>";
    echo "<li>✅ قيود اليومية</li>";
    echo "<li>✅ التقارير المالية</li>";
    echo "</ul>";

    echo "<br><div style='text-align: center;'>";
    echo "<a href='dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة الرئيسية</a> ";
    echo "<a href='reports.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>التقارير</a> ";
    echo "<a href='invoices.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>الفواتير</a>";
    echo "</div>";

} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
