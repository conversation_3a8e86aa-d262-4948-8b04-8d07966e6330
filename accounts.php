<?php
/**
 * SeaSystem - صفحة دليل الحسابات
 * Chart of Accounts Page
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/includes/sidebar.php';
require_once __DIR__ . '/classes/Account.php';

// التأكد من تسجيل الدخول
requireLogin();

$account = new Account();
$current_user = getCurrentUser();

// معالجة العمليات
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $data = [
                    'account_code' => $_POST['account_code'],
                    'account_name' => $_POST['account_name'],
                    'account_type' => $_POST['account_type'],
                    'parent_id' => !empty($_POST['parent_id']) ? $_POST['parent_id'] : null,
                    'description' => $_POST['description']
                ];

                $result = $account->create($data);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;

            case 'edit':
                $data = [
                    'account_code' => $_POST['account_code'],
                    'account_name' => $_POST['account_name'],
                    'account_type' => $_POST['account_type'],
                    'parent_id' => !empty($_POST['parent_id']) ? $_POST['parent_id'] : null,
                    'description' => $_POST['description']
                ];

                $result = $account->update($_POST['account_id'], $data);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;

            case 'delete':
                $result = $account->delete($_POST['account_id']);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;
        }
    }
}

// الحصول على قائمة الحسابات
$accounts = $account->getAll();
$main_accounts = $account->getMainAccounts();

// البحث
$search_term = $_GET['search'] ?? '';
if (!empty($search_term)) {
    $accounts = $account->search($search_term);
}

// تصنيف الحسابات حسب النوع
$account_types = [
    'asset' => 'الأصول',
    'liability' => 'الخصوم',
    'equity' => 'حقوق الملكية',
    'revenue' => 'الإيرادات',
    'expense' => 'المصروفات'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل الحسابات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <link href="assets/css/sidebar-only.css" rel="stylesheet">
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي الموحد -->
            <?php renderSidebar('accounts.php'); ?>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="page-title">
                                <i class="fas fa-list me-2"></i>دليل الحسابات
                            </h1>
                            <p class="page-subtitle">إدارة الحسابات المالية والمحاسبية</p>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAccountModal">
                                <i class="fas fa-plus me-2"></i>إضافة حساب جديد
                            </button>
                        </div>
                    </div>
                </div>

                <!-- الرسائل -->
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <?php foreach ($account_types as $type => $type_name): ?>
                        <?php $type_accounts = $account->getByType($type); ?>
                        <div class="col-md-2 mb-3">
                            <div class="stat-card text-center">
                                <h4 class="text-primary"><?php echo count($type_accounts); ?></h4>
                                <p class="mb-0 small"><?php echo $type_name; ?></p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- جدول الحسابات -->
                <div class="table-card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>قائمة الحسابات (<?php echo count($accounts); ?>)
                                </h5>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex">
                                    <input type="text" class="form-control me-2" id="accountSearch"
                                           placeholder="البحث في الحسابات..."
                                           value="<?php echo htmlspecialchars($search_term); ?>">
                                    <button type="button" class="btn btn-light" id="clearAccountSearch">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="accountsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>رمز الحساب</th>
                                        <th>اسم الحساب</th>
                                        <th>نوع الحساب</th>
                                        <th>الحساب الأب</th>
                                        <th>الرصيد</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($accounts)): ?>
                                        <tr>
                                            <td colspan="7" class="text-center py-4">
                                                <i class="fas fa-list fa-2x text-muted mb-2"></i>
                                                <p class="text-muted mb-0">لا توجد حسابات</p>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($accounts as $acc): ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($acc['account_code']); ?></strong></td>
                                                <td><?php echo htmlspecialchars($acc['account_name']); ?></td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <?php echo $account_types[$acc['account_type']] ?? $acc['account_type']; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars($acc['parent_name'] ?? '-'); ?></td>
                                                <td>
                                                    <?php
                                                    $balance = $account->getBalance($acc['id']);
                                                    $balance_class = $balance >= 0 ? 'text-success' : 'text-danger';
                                                    ?>
                                                    <span class="<?php echo $balance_class; ?>">
                                                        <?php echo number_format($balance, 2) . ' ' . CURRENCY_SYMBOL; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($acc['is_active']): ?>
                                                        <span class="badge bg-success">نشط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">غير نشط</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="d-flex gap-2">
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                                    onclick="editAccount(<?php echo htmlspecialchars(json_encode($acc)); ?>)">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">تعديل</small>
                                                        </div>
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-danger btn-sm btn-delete"
                                                                    onclick="deleteAccount(<?php echo $acc['id']; ?>)">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">حذف</small>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة حساب -->
    <div class="modal fade" id="addAccountModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>إضافة حساب جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="account_code" class="form-label">رمز الحساب *</label>
                                <input type="text" class="form-control" id="account_code" name="account_code" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="account_name" class="form-label">اسم الحساب *</label>
                                <input type="text" class="form-control" id="account_name" name="account_name" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="account_type" class="form-label">نوع الحساب *</label>
                                <select class="form-select" id="account_type" name="account_type" required>
                                    <option value="">اختر نوع الحساب</option>
                                    <?php foreach ($account_types as $type => $type_name): ?>
                                        <option value="<?php echo $type; ?>"><?php echo $type_name; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="parent_id" class="form-label">الحساب الأب</label>
                                <select class="form-select" id="parent_id" name="parent_id">
                                    <option value="">لا يوجد (حساب رئيسي)</option>
                                    <?php foreach ($main_accounts as $main_acc): ?>
                                        <option value="<?php echo $main_acc['id']; ?>">
                                            <?php echo $main_acc['account_code'] . ' - ' . $main_acc['account_name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ الحساب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل حساب -->
    <div class="modal fade" id="editAccountModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>تعديل الحساب
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="account_id" id="edit_account_id">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_account_code" class="form-label">رمز الحساب *</label>
                                <input type="text" class="form-control" id="edit_account_code" name="account_code" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="edit_account_name" class="form-label">اسم الحساب *</label>
                                <input type="text" class="form-control" id="edit_account_name" name="account_name" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_account_type" class="form-label">نوع الحساب *</label>
                                <select class="form-select" id="edit_account_type" name="account_type" required>
                                    <?php foreach ($account_types as $type => $type_name): ?>
                                        <option value="<?php echo $type; ?>"><?php echo $type_name; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="edit_parent_id" class="form-label">الحساب الأب</label>
                                <select class="form-select" id="edit_parent_id" name="parent_id">
                                    <option value="">لا يوجد (حساب رئيسي)</option>
                                    <?php foreach ($main_accounts as $main_acc): ?>
                                        <option value="<?php echo $main_acc['id']; ?>">
                                            <?php echo $main_acc['account_code'] . ' - ' . $main_acc['account_name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        // تعديل حساب
        function editAccount(account) {
            document.getElementById('edit_account_id').value = account.id;
            document.getElementById('edit_account_code').value = account.account_code;
            document.getElementById('edit_account_name').value = account.account_name;
            document.getElementById('edit_account_type').value = account.account_type;
            document.getElementById('edit_parent_id').value = account.parent_id || '';
            document.getElementById('edit_description').value = account.description || '';

            new bootstrap.Modal(document.getElementById('editAccountModal')).show();
        }

        // حذف حساب
        function deleteAccount(accountId) {
            if (confirm('هل أنت متأكد من حذف هذا الحساب؟\nتأكد من عدم وجود قيود محاسبية مرتبطة به.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="account_id" value="${accountId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // البحث الديناميكي
        let searchTimeout;
        const searchInput = document.getElementById('accountSearch');
        const clearButton = document.getElementById('clearAccountSearch');
        const tableBody = document.querySelector('#accountsTable tbody');

        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const searchTerm = this.value.trim();

                // تأخير البحث لمدة 300 ملي ثانية
                searchTimeout = setTimeout(() => {
                    performSearch(searchTerm);
                }, 300);
            });
        }

        if (clearButton) {
            clearButton.addEventListener('click', function() {
                searchInput.value = '';
                performSearch('');
            });
        }

        function performSearch(searchTerm) {
            // إظهار مؤشر التحميل
            tableBody.innerHTML = '<tr><td colspan="7" class="text-center py-4"><i class="fas fa-spinner fa-spin fa-2x text-muted"></i><p class="text-muted mt-2">جاري البحث...</p></td></tr>';

            // إرسال طلب AJAX
            fetch(`ajax_search_accounts.php?search=${encodeURIComponent(searchTerm)}`)
                .then(response => response.text())
                .then(data => {
                    tableBody.innerHTML = data;
                })
                .catch(error => {
                    console.error('خطأ في البحث:', error);
                    tableBody.innerHTML = '<tr><td colspan="7" class="text-center py-4 text-danger"><i class="fas fa-exclamation-triangle fa-2x mb-2"></i><p>حدث خطأ في البحث</p></td></tr>';
                });
        }
    
        // تحديث الوقت والتاريخ
        function updateDateTime() {
            const now = new Date();
            const timeElement = document.getElementById('current-time');
            const dateElement = document.getElementById('current-date');
            
            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString('ar-SA', {
                    hour: '2-digit', minute: '2-digit'
                });
            }
            if (dateElement) {
                dateElement.textContent = now.toLocaleDateString('ar-SA');
            }
        }
        setInterval(updateDateTime, 60000);
        updateDateTime();
    </script>
</body>
</html>
