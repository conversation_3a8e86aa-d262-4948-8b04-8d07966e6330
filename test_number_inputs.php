<?php
/**
 * SeaSystem - اختبار حقول الأرقام المحسنة
 * Test Enhanced Number Inputs
 */

require_once __DIR__ . '/config/config.php';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حقول الأرقام - SeaSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-keyboard text-primary me-2"></i>اختبار حقول الأرقام المحسنة
                </h1>
                
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle me-2"></i>المميزات الجديدة:</h5>
                    <ul class="mb-0">
                        <li>✅ <strong>كتابة مباشرة</strong> - لا توجد أسهم أو أزرار</li>
                        <li>✅ <strong>منع الحروف</strong> - الأرقام والنقطة العشرية فقط</li>
                        <li>✅ <strong>تنسيق تلقائي</strong> - يضبط الرقم عند فقدان التركيز</li>
                        <li>✅ <strong>تحديد النص</strong> - يحدد النص عند التركيز للتسهيل</li>
                        <li>✅ <strong>لصق ذكي</strong> - يقبل الأرقام المنسوخة فقط</li>
                    </ul>
                </div>
                
                <div class="row">
                    <!-- حقول الأرصدة -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-wallet me-2"></i>حقول الأرصدة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="test_opening_balance" class="form-label">الرصيد الافتتاحي</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="test_opening_balance" 
                                               pattern="[0-9]+([.][0-9]+)?" inputmode="decimal" 
                                               placeholder="ادخل الرصيد مثل: 1500.50" value="0.00">
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="test_current_balance" class="form-label">الرصيد الحالي</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="test_current_balance" 
                                               pattern="[0-9]+([.][0-9]+)?" inputmode="decimal" 
                                               placeholder="ادخل الرصيد مثل: 2750.25" value="0.00">
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="test_credit_limit" class="form-label">حد الائتمان</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="test_credit_limit" 
                                               pattern="[0-9]+([.][0-9]+)?" inputmode="decimal" 
                                               placeholder="ادخل الحد مثل: 5000.00" value="0.00">
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- حقول الأسعار -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-tags me-2"></i>حقول الأسعار
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="test_cost_price" class="form-label">سعر التكلفة</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="test_cost_price" 
                                               pattern="[0-9]+([.][0-9]+)?" inputmode="decimal" 
                                               placeholder="مثل: 150.50" value="0.00">
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="test_selling_price" class="form-label">سعر البيع</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="test_selling_price" 
                                               pattern="[0-9]+([.][0-9]+)?" inputmode="decimal" 
                                               placeholder="مثل: 200.75" value="0.00">
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                    <div id="test_profit_info" class="small text-muted mt-1"></div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="test_discount" class="form-label">الخصم</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="test_discount" 
                                               pattern="[0-9]+([.][0-9]+)?" inputmode="decimal" 
                                               placeholder="مثل: 25.00" value="0.00">
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- اختبار التفاعل -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-vial me-2"></i>اختبار التفاعل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6>جرب هذه الأمثلة:</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">اكتب: <code>1500.50</code></li>
                                    <li class="list-group-item">اكتب: <code>2000</code></li>
                                    <li class="list-group-item">اكتب: <code>abc123</code> (سيمنع الحروف)</li>
                                    <li class="list-group-item">اكتب: <code>10.5.5</code> (سيمنع النقطة الثانية)</li>
                                </ul>
                            </div>
                            
                            <div class="col-md-4">
                                <h6>اختبار اللصق:</h6>
                                <div class="mb-2">
                                    <button class="btn btn-outline-primary btn-sm" onclick="copyToClipboard('1234.56')">
                                        انسخ: 1234.56
                                    </button>
                                </div>
                                <div class="mb-2">
                                    <button class="btn btn-outline-primary btn-sm" onclick="copyToClipboard('abc123')">
                                        انسخ: abc123 (سيرفض)
                                    </button>
                                </div>
                                <small class="text-muted">انسخ ثم الصق في أي حقل أعلاه</small>
                            </div>
                            
                            <div class="col-md-4">
                                <h6>النتائج:</h6>
                                <div id="test_results" class="border rounded p-2 bg-light" style="min-height: 100px;">
                                    <small class="text-muted">ستظهر النتائج هنا...</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- مقارنة قبل وبعد -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-balance-scale me-2"></i>مقارنة: قبل وبعد التحسين
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">❌ النوع القديم (number):</h6>
                                <div class="input-group mb-3">
                                    <input type="number" class="form-control" step="0.01" value="0.00" placeholder="حقل number عادي">
                                    <span class="input-group-text">ريال</span>
                                </div>
                                <ul class="small text-muted">
                                    <li>يظهر أسهم للزيادة والنقصان</li>
                                    <li>قد يقبل أرقام غير صحيحة</li>
                                    <li>لا يتحكم في التنسيق</li>
                                </ul>
                            </div>
                            
                            <div class="col-md-6">
                                <h6 class="text-success">✅ النوع الجديد (text محسن):</h6>
                                <div class="input-group mb-3">
                                    <input type="text" class="form-control" pattern="[0-9]+([.][0-9]+)?" inputmode="decimal" 
                                           placeholder="حقل text محسن" value="0.00">
                                    <span class="input-group-text">ريال</span>
                                </div>
                                <ul class="small text-success">
                                    <li>لا توجد أسهم - كتابة مباشرة</li>
                                    <li>يمنع الحروف والرموز</li>
                                    <li>تنسيق تلقائي للأرقام</li>
                                    <li>تجربة مستخدم أفضل</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <a href="customers.php" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-users me-2"></i>اختبار في صفحة العملاء
                    </a>
                    <a href="suppliers.php" class="btn btn-success btn-lg me-3">
                        <i class="fas fa-truck me-2"></i>اختبار في صفحة الموردين
                    </a>
                    <a href="inventory.php" class="btn btn-warning btn-lg me-3">
                        <i class="fas fa-boxes me-2"></i>اختبار في صفحة المخزون
                    </a>
                    <a href="dashboard.php" class="btn btn-secondary btn-lg">
                        <i class="fas fa-home me-2"></i>العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/number-input-enhancement.js"></script>
    <script>
        // حساب هامش الربح للاختبار
        function calculateTestProfit() {
            const costPrice = parseFloat(document.getElementById('test_cost_price').value) || 0;
            const sellingPrice = parseFloat(document.getElementById('test_selling_price').value) || 0;
            
            if (costPrice > 0 && sellingPrice > 0) {
                const profit = sellingPrice - costPrice;
                const profitMargin = ((profit / sellingPrice) * 100).toFixed(2);
                
                document.getElementById('test_profit_info').innerHTML = 
                    `الربح: ${profit.toFixed(2)} ريال | هامش الربح: ${profitMargin}%`;
            } else {
                document.getElementById('test_profit_info').innerHTML = '';
            }
        }
        
        // نسخ النص للحافظة
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                addTestResult(`تم نسخ: "${text}" - جرب لصقه في أي حقل أعلاه`);
            });
        }
        
        // إضافة نتيجة اختبار
        function addTestResult(message) {
            const resultsDiv = document.getElementById('test_results');
            const time = new Date().toLocaleTimeString('ar-SA');
            resultsDiv.innerHTML += `<div class="small mb-1">[${time}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // مراقبة التغييرات في الحقول
        document.addEventListener('DOMContentLoaded', function() {
            // ربط حساب الربح
            document.getElementById('test_cost_price').addEventListener('input', calculateTestProfit);
            document.getElementById('test_selling_price').addEventListener('input', calculateTestProfit);
            
            // مراقبة جميع الحقول للاختبار
            const testInputs = document.querySelectorAll('input[type="text"][pattern*="[0-9]"]');
            testInputs.forEach(function(input) {
                input.addEventListener('input', function() {
                    addTestResult(`تم تغيير ${this.previousElementSibling?.textContent || 'حقل'}: "${this.value}"`);
                });
                
                input.addEventListener('blur', function() {
                    addTestResult(`تم تنسيق ${this.previousElementSibling?.textContent || 'حقل'}: "${this.value}"`);
                });
            });
            
            addTestResult('تم تحميل الصفحة - جرب الكتابة في الحقول أعلاه');
        });
    </script>
</body>
</html>
