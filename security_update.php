<?php
/**
 * SeaSystem - سكريبت تحديث الأمان
 * Security Update Script
 */

require_once __DIR__ . '/config/database.php';

echo "<h1>🔒 تحديث إعدادات الأمان - SeaSystem</h1>";
echo "<hr>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // تحديث كلمة مرور المدير الافتراضية
    echo "<h3>1. تحديث كلمة مرور المدير</h3>";
    
    // إنشاء كلمة مرور قوية جديدة
    $newPassword = 'SeaAdmin@' . date('Y') . '!';
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    $sql = "UPDATE users SET password = :password WHERE username = 'admin'";
    $stmt = $db->prepare($sql);
    $stmt->bindParam(':password', $hashedPassword);
    
    if ($stmt->execute()) {
        echo "✅ تم تحديث كلمة مرور المدير بنجاح<br>";
        echo "🔑 كلمة المرور الجديدة: <strong style='color: red;'>" . $newPassword . "</strong><br>";
        echo "⚠️ <strong>مهم:</strong> احفظ كلمة المرور الجديدة في مكان آمن وقم بتغييرها من لوحة التحكم<br><br>";
    } else {
        echo "❌ فشل في تحديث كلمة المرور<br><br>";
    }
    
    // إنشاء جدول السجلات الأمنية
    echo "<h3>2. إنشاء جدول السجلات الأمنية</h3>";
    
    $securityLogTable = "CREATE TABLE IF NOT EXISTS security_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        action VARCHAR(100) NOT NULL,
        ip_address VARCHAR(45) NOT NULL,
        user_agent TEXT,
        details TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_action (action),
        INDEX idx_ip (ip_address),
        INDEX idx_created_at (created_at)
    )";
    
    if ($db->exec($securityLogTable)) {
        echo "✅ تم إنشاء جدول السجلات الأمنية<br><br>";
    } else {
        echo "✅ جدول السجلات الأمنية موجود مسبقاً<br><br>";
    }
    
    // إنشاء جدول إعدادات النظام
    echo "<h3>3. إنشاء جدول إعدادات النظام</h3>";
    
    $settingsTable = "CREATE TABLE IF NOT EXISTS system_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
        description TEXT,
        is_public BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($db->exec($settingsTable)) {
        echo "✅ تم إنشاء جدول إعدادات النظام<br><br>";
    } else {
        echo "✅ جدول إعدادات النظام موجود مسبقاً<br><br>";
    }
    
    // إدراج الإعدادات الافتراضية
    echo "<h3>4. إدراج الإعدادات الافتراضية</h3>";
    
    $defaultSettings = [
        ['max_login_attempts', '5', 'number', 'الحد الأقصى لمحاولات تسجيل الدخول'],
        ['session_timeout', '3600', 'number', 'مهلة انتهاء الجلسة بالثواني'],
        ['password_min_length', '8', 'number', 'الحد الأدنى لطول كلمة المرور'],
        ['enable_2fa', 'false', 'boolean', 'تفعيل المصادقة الثنائية'],
        ['backup_frequency', 'daily', 'string', 'تكرار النسخ الاحتياطي'],
        ['maintenance_mode', 'false', 'boolean', 'وضع الصيانة'],
        ['site_name', 'SeaSystem', 'string', 'اسم الموقع'],
        ['currency_symbol', 'ر.س', 'string', 'رمز العملة'],
        ['timezone', 'Asia/Riyadh', 'string', 'المنطقة الزمنية']
    ];
    
    foreach ($defaultSettings as $setting) {
        $checkSql = "SELECT COUNT(*) FROM system_settings WHERE setting_key = ?";
        $checkStmt = $db->prepare($checkSql);
        $checkStmt->execute([$setting[0]]);
        
        if ($checkStmt->fetchColumn() == 0) {
            $insertSql = "INSERT INTO system_settings (setting_key, setting_value, setting_type, description) 
                         VALUES (?, ?, ?, ?)";
            $insertStmt = $db->prepare($insertSql);
            $insertStmt->execute($setting);
            echo "✅ تم إدراج إعداد: " . $setting[0] . "<br>";
        } else {
            echo "ℹ️ الإعداد موجود مسبقاً: " . $setting[0] . "<br>";
        }
    }
    
    echo "<br><h3>5. تنظيف البيانات القديمة</h3>";
    
    // تنظيف محاولات تسجيل الدخول القديمة
    $cleanupSql = "DELETE FROM login_attempts WHERE last_attempt < DATE_SUB(NOW(), INTERVAL 7 DAY)";
    $cleanupResult = $db->exec($cleanupSql);
    echo "✅ تم حذف " . $cleanupResult . " محاولة دخول قديمة<br>";
    
    echo "<br><div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>🎉 تم تحديث الأمان بنجاح!</h4>";
    echo "<p style='margin: 0; color: #155724;'>جميع التحديثات الأمنية تمت بنجاح. النظام الآن أكثر أماناً.</p>";
    echo "</div>";
    
    echo "<br><div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>";
    echo "<h4 style='color: #856404; margin: 0 0 10px 0;'>⚠️ تذكيرات مهمة:</h4>";
    echo "<ul style='margin: 0; color: #856404;'>";
    echo "<li>قم بتغيير كلمة مرور المدير من لوحة التحكم</li>";
    echo "<li>احذف هذا الملف (security_update.php) بعد التحديث</li>";
    echo "<li>تأكد من تفعيل HTTPS في الإنتاج</li>";
    echo "<li>قم بعمل نسخة احتياطية من قاعدة البيانات</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h4 style='color: #721c24; margin: 0 0 10px 0;'>❌ خطأ في التحديث</h4>";
    echo "<p style='margin: 0; color: #721c24;'>حدث خطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<br><a href='login.php' class='btn btn-primary'>الذهاب لتسجيل الدخول</a>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background: #f8f9fa;
    direction: rtl;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    margin-top: 20px;
}

.btn:hover {
    background: #0056b3;
    color: white;
    text-decoration: none;
}
</style>
