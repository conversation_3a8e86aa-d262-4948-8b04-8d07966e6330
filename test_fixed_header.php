<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الهيدر الثابت - SeaSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
</head>
<body>
    <!-- الهيدر الثابت المحسن -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <!-- العلامة التجارية -->
            <a class="navbar-brand d-flex align-items-center" href="dashboard.php">
                <i class="fas fa-anchor me-2"></i>
                <span class="brand-text">SeaSystem</span>
                <small class="version-badge ms-2 badge bg-light text-dark">v1.0.0</small>
            </a>
            
            <!-- زر التبديل للموبايل -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- قائمة التنقل -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- القائمة الرئيسية -->
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-line me-1"></i>
                            <span>التقارير</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-chart-bar me-2"></i>التقارير الرئيسية
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-balance-scale me-2"></i>ميزان المراجعة
                            </a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bolt me-1"></i>
                            <span>وصول سريع</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-users me-2"></i>العملاء
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-truck me-2"></i>الموردين
                            </a></li>
                        </ul>
                    </li>
                </ul>
                
                <!-- أدوات الهيدر -->
                <ul class="navbar-nav">
                    <!-- البحث السريع -->
                    <li class="nav-item me-2">
                        <form class="d-flex" role="search">
                            <div class="input-group input-group-sm">
                                <input class="form-control search-input" type="search" placeholder="بحث سريع...">
                                <button class="btn btn-outline-light" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </li>
                    
                    <!-- الإشعارات -->
                    <li class="nav-item dropdown me-2">
                        <a class="nav-link position-relative notification-badge" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">3</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                            <li><h6 class="dropdown-header">الإشعارات الحديثة</h6></li>
                            <li><a class="dropdown-item" href="#">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-info-circle text-info"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-2">
                                        <h6 class="mb-1">فاتورة جديدة</h6>
                                        <p class="mb-1 small">تم إنشاء فاتورة رقم #1001</p>
                                        <small class="text-muted">منذ 5 دقائق</small>
                                    </div>
                                </div>
                            </a></li>
                        </ul>
                    </li>
                    
                    <!-- الوقت والتاريخ -->
                    <li class="nav-item me-3">
                        <span class="navbar-text time-display">
                            <i class="fas fa-clock me-1"></i>
                            <span id="current-time">14:30</span>
                            <br>
                            <small id="current-date">2025-06-24</small>
                        </span>
                    </li>
                    
                    <!-- قائمة المستخدم -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                            <div class="user-avatar me-2">
                                <i class="fas fa-user-circle fa-lg"></i>
                            </div>
                            <div class="user-info d-none d-md-block">
                                <div class="user-name">مدير النظام</div>
                                <small class="user-role text-light opacity-75">admin</small>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end user-dropdown">
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- أزرار التحكم السريع -->
    <div class="quick-controls">
        <button class="btn btn-primary btn-sm" onclick="HeaderUtils.scrollToTop()" title="العودة للأعلى">
            <i class="fas fa-arrow-up"></i>
        </button>
        <button class="btn btn-secondary btn-sm" onclick="HeaderUtils.toggleHeader()" title="إخفاء/إظهار الهيدر">
            <i class="fas fa-eye-slash"></i>
        </button>
    </div>

    <!-- المحتوى للاختبار -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-test-tube me-2"></i>اختبار الهيدر الثابت</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h4>🎯 تعليمات الاختبار:</h4>
                            <ul>
                                <li><strong>قم بالتمرير لأسفل</strong> - سيصبح الهيدر أكثر شفافية وأصغر</li>
                                <li><strong>قم بالتمرير لأعلى</strong> - سيعود الهيدر لحالته الطبيعية</li>
                                <li><strong>التمرير السريع لأسفل</strong> - سيختفي الهيدر مؤقتاً</li>
                                <li><strong>مرر الماوس على الهيدر</strong> - ستظهر تأثيرات إضافية</li>
                                <li><strong>اضغط على الروابط</strong> - ستظهر تأثيرات التموج</li>
                            </ul>
                        </div>
                        
                        <!-- محتوى طويل للاختبار -->
                        <?php for($i = 1; $i <= 50; $i++): ?>
                        <div class="card mb-3">
                            <div class="card-body">
                                <h5 class="card-title">بطاقة اختبار رقم <?php echo $i; ?></h5>
                                <p class="card-text">
                                    هذا نص تجريبي لاختبار التمرير والهيدر الثابت. 
                                    يمكنك التمرير لأعلى وأسفل لرؤية كيف يتفاعل الهيدر مع حركة التمرير.
                                    النظام يتضمن تأثيرات بصرية جميلة وسلسة.
                                </p>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="bg-primary text-white p-3 rounded">
                                            <i class="fas fa-chart-bar fa-2x"></i>
                                            <h6 class="mt-2">إحصائية <?php echo $i; ?></h6>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="bg-success text-white p-3 rounded">
                                            <i class="fas fa-users fa-2x"></i>
                                            <h6 class="mt-2">بيانات <?php echo $i; ?></h6>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="bg-warning text-white p-3 rounded">
                                            <i class="fas fa-cog fa-2x"></i>
                                            <h6 class="mt-2">إعدادات <?php echo $i; ?></h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/fixed-header.js"></script>
    
    <style>
    /* تنسيقات إضافية للاختبار */
    .search-input {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        width: 200px;
    }

    .search-input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .search-input:focus {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.4);
        color: white;
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
    }

    .time-display {
        font-size: 0.85rem;
        text-align: center;
        line-height: 1.2;
    }

    .user-avatar {
        font-size: 1.5rem;
    }

    .user-info {
        text-align: right;
        line-height: 1.2;
    }

    .user-name {
        font-weight: 600;
        font-size: 0.9rem;
    }

    .user-role {
        font-size: 0.75rem;
    }

    .notification-dropdown {
        width: 350px;
        max-height: 400px;
        overflow-y: auto;
    }

    .user-dropdown {
        width: 280px;
    }

    .version-badge {
        font-size: 0.6rem;
        padding: 0.2rem 0.4rem;
    }

    .quick-controls {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1025;
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .quick-controls .btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
    }

    .quick-controls .btn:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
    }

    @media (max-width: 991.98px) {
        .search-input {
            width: 150px;
        }
        
        .time-display {
            display: none;
        }
        
        .quick-controls {
            bottom: 10px;
            right: 10px;
        }
    }
    </style>

    <script>
    // تحديث الوقت والتاريخ
    function updateDateTime() {
        const now = new Date();
        const timeElement = document.getElementById('current-time');
        const dateElement = document.getElementById('current-date');
        
        if (timeElement) {
            timeElement.textContent = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }
        
        if (dateElement) {
            dateElement.textContent = now.toLocaleDateString('ar-SA');
        }
    }

    // تحديث كل دقيقة
    setInterval(updateDateTime, 60000);
    updateDateTime(); // تحديث فوري
    </script>
</body>
</html>
