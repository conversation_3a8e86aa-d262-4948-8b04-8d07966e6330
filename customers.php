<?php
/**
 * SeaSystem - صفحة إدارة العملاء
 * Customers Management Page
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/includes/sidebar.php';
require_once __DIR__ . '/includes/page_template.php';
require_once __DIR__ . '/classes/Customer.php';
require_once __DIR__ . '/classes/NumberGenerator.php';

// التأكد من تسجيل الدخول
requireLogin();
$current_user = getCurrentUser();

// إنشاء كائن العملاء
$customer = new Customer();
$numberGenerator = new NumberGenerator();

// معالجة العمليات
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $data = [
                    'customer_code' => $_POST['customer_code'],
                    'name' => $_POST['name'],
                    'email' => $_POST['email'],
                    'phone' => $_POST['phone'],
                    'address' => $_POST['address'],
                    'tax_number' => $_POST['tax_number'],
                    'credit_limit' => $_POST['credit_limit'] ?? 0,
                    'opening_balance' => !empty($_POST['opening_balance']) ? $_POST['opening_balance'] : 0.00
                ];

                $result = $customer->create($data);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;

            case 'edit':
                $data = [
                    'customer_code' => $_POST['customer_code'],
                    'name' => $_POST['name'],
                    'email' => $_POST['email'],
                    'phone' => $_POST['phone'],
                    'address' => $_POST['address'],
                    'tax_number' => $_POST['tax_number'],
                    'credit_limit' => $_POST['credit_limit'] ?? 0,
                    'opening_balance' => !empty($_POST['opening_balance']) ? $_POST['opening_balance'] : 0.00
                ];

                $result = $customer->update($_POST['customer_id'], $data);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;

            case 'delete':
                $result = $customer->delete($_POST['customer_id']);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;
        }
    }
}

// الحصول على قائمة العملاء
$customers = $customer->getAll();

// البحث
$search_term = $_GET['search'] ?? '';
if (!empty($search_term)) {
    $customers = $customer->search($search_term);
}

// الحصول على الإحصائيات المالية
$total_sales_amount = $customer->getTotalSalesAmount();
$total_outstanding = $customer->getTotalOutstandingAmount();
$total_paid = $customer->getTotalPaidAmount();
$average_order_value = $customer->getAverageOrderValue();

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <link href="assets/css/sidebar-only.css" rel="stylesheet">
    <style>
        .stat-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            min-height: 120px;
            display: flex;
            align-items: center;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            flex-shrink: 0;
        }

        .stat-icon.primary { background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); }
        .stat-icon.success { background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); }
        .stat-icon.warning { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); }
        .stat-icon.info { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); }

        .stat-card h3 {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .stat-card .stat-content {
            flex: 1;
        }

        .stat-card p {
            margin-bottom: 0;
            font-size: 0.9rem;
            line-height: 1.2;
        }

        /* نفس تصميم صفحة الموردين - استخدام CSS من style.css */
        .table-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: none;
        }

        .table-card .card-header {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            border-radius: 10px 10px 0 0;
            padding: 1rem 1.5rem;
        }

        /* تحسين قراءة الجدول */
        .table tbody td {
            color: #212529 !important;
            background-color: white !important;
        }

        .table tbody tr:hover td {
            background-color: #f8f9fa !important;
            color: #212529 !important;
        }



        /* تحسين الشارات والألوان */
        .badge {
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
        }

        .text-success {
            color: #28a745 !important;
            font-weight: 600;
        }

        .text-danger {
            color: #dc3545 !important;
            font-weight: 600;
        }

        /* تحسين أزرار العمليات */
        .btn-sm {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
        }

        .d-flex.gap-2 {
            gap: 0.75rem !important;
        }

        /* ضمان وضوح النص في الجدول */
        .table td, .table th {
            border-color: #e9ecef !important;
        }

        .table tbody tr {
            background-color: white !important;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa !important;
        }

        /* تحسين قراءة النصوص */
        .table td {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .table td strong {
            color: #495057 !important;
        }

        /* تخصيص رأس بطاقة الجدول ليطابق صفحة الموردين */
        .table-card .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            border-bottom: 1px solid #dee2e6;
            border-radius: 10px 10px 0 0;
            padding: 1rem 1.5rem;
        }

        /* تنسيقات الهيدر الثابت */
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }

        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }

        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
        </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي الموحد -->
            <?php renderSidebar('customers.php'); ?>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="page-title">
                                <i class="fas fa-users me-2"></i>إدارة العملاء
                            </h1>
                            <p class="page-subtitle">إضافة وإدارة بيانات العملاء</p>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                                <i class="fas fa-plus me-2"></i>إضافة عميل جديد
                            </button>
                        </div>
                    </div>
                </div>

                <!-- بطاقات الإحصائيات المالية -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon primary me-3">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="stat-content">
                                <h3><?php echo number_format($total_sales_amount, 2); ?></h3>
                                <p class="text-muted">إجمالي قيمة المبيعات</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon success me-3">
                                <i class="fas fa-check-double"></i>
                            </div>
                            <div class="stat-content">
                                <h3><?php echo number_format($total_paid, 2); ?></h3>
                                <p class="text-muted">إجمالي المحصلات</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon warning me-3">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content">
                                <h3><?php echo number_format($total_outstanding, 2); ?></h3>
                                <p class="text-muted">إجمالي المبالغ المستحقة</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon info me-3">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-content">
                                <h3><?php echo number_format($average_order_value, 2); ?></h3>
                                <p class="text-muted">متوسط قيمة الطلب</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسائل -->
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- جدول العملاء -->
                <div class="table-card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-users me-2"></i>قائمة العملاء (<?php echo count($customers); ?>)
                                </h5>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex">
                                    <input type="text" class="form-control me-2" id="customerSearch"
                                           placeholder="البحث في العملاء..."
                                           value="<?php echo htmlspecialchars($search_term); ?>">
                                    <button type="button" class="btn btn-light" id="clearSearch">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>رمز العميل</th>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>رقم الهاتف</th>
                                        <th>الرقم الضريبي</th>
                                        <th>الرصيد الحالي</th>
                                        <th>الحالة</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody id="customersTableBody">
                                    <?php if (empty($customers)): ?>
                                        <tr>
                                            <td colspan="8" class="text-center py-4">
                                                <i class="fas fa-users fa-2x text-muted mb-2"></i>
                                                <p class="text-muted mb-0">لا توجد عملاء حتى الآن</p>
                                                <button type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                                                    إضافة أول عميل
                                                </button>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($customers as $cust): ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($cust['customer_code']); ?></strong></td>
                                                <td><?php echo htmlspecialchars($cust['name']); ?></td>
                                                <td><?php echo htmlspecialchars($cust['email']); ?></td>
                                                <td><?php echo htmlspecialchars($cust['phone']); ?></td>
                                                <td><?php echo htmlspecialchars($cust['tax_number']); ?></td>
                                                <td>
                                                    <span class="<?php echo $cust['current_balance'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                        <?php echo number_format($cust['current_balance'], 2) . ' ' . CURRENCY_SYMBOL; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($cust['is_active']): ?>
                                                        <span class="badge bg-success">نشط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">غير نشط</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="d-flex gap-2">
                                                        <!-- زر العرض أولاً -->
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-info btn-sm"
                                                                    onclick="viewCustomer(<?php echo $cust['id']; ?>)">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">عرض</small>
                                                        </div>
                                                        <!-- زر التعديل ثانياً -->
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                                    onclick="editCustomer(<?php echo htmlspecialchars(json_encode($cust)); ?>)">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">تعديل</small>
                                                        </div>
                                                        <!-- زر الحذف أخيراً -->
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-danger btn-sm btn-delete"
                                                                    onclick="deleteCustomer(<?php echo $cust['id']; ?>)">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">حذف</small>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة عميل -->
    <div class="modal fade" id="addCustomerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="customer_code" class="form-label">رمز العميل *</label>
                                <input type="text" class="form-control" id="customer_code" name="customer_code" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم العميل *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="tax_number" class="form-label">الرقم الضريبي</label>
                                <input type="text" class="form-control" id="tax_number" name="tax_number">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="credit_limit" class="form-label">الحد الائتماني</label>
                                <input type="number" class="form-control" id="credit_limit" name="credit_limit"
                                       step="0.01" min="0" value="0">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="opening_balance" class="form-label">الرصيد الافتتاحي</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="opening_balance" name="opening_balance" value="0.00"
                                           pattern="[0-9]+([.][0-9]+)?" inputmode="decimal"
                                           placeholder="ادخل الرصيد مثل: 1500.50">
                                    <span class="input-group-text">ريال</span>
                                </div>
                                <small class="form-text text-muted">اترك فارغاً إذا لم يكن هناك رصيد افتتاحي</small>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ العميل
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل عميل -->
    <div class="modal fade" id="editCustomerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-edit me-2"></i>تعديل بيانات العميل
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="editCustomerForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="customer_id" id="edit_customer_id">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_customer_code" class="form-label">رمز العميل *</label>
                                <input type="text" class="form-control" id="edit_customer_code" name="customer_code" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="edit_name" class="form-label">اسم العميل *</label>
                                <input type="text" class="form-control" id="edit_name" name="name" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="edit_email" name="email">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="edit_phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="edit_phone" name="phone">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_address" class="form-label">العنوان</label>
                            <textarea class="form-control" id="edit_address" name="address" rows="3"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_tax_number" class="form-label">الرقم الضريبي</label>
                                <input type="text" class="form-control" id="edit_tax_number" name="tax_number">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="edit_credit_limit" class="form-label">الحد الائتماني</label>
                                <input type="number" class="form-control" id="edit_credit_limit" name="credit_limit"
                                       step="0.01" min="0">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_opening_balance" class="form-label">الرصيد الافتتاحي</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="edit_opening_balance" name="opening_balance"
                                           pattern="[0-9]+([.][0-9]+)?" inputmode="decimal"
                                           placeholder="ادخل الرصيد مثل: 1500.50">
                                    <span class="input-group-text">ريال</span>
                                </div>
                                <small class="form-text text-muted">لا يمكن تعديل الرصيد الافتتاحي بعد وجود حركات</small>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
    <script src="assets/js/number-input-enhancement.js"></script>
    <script src="assets/js/duplicate-check.js"></script>
    <script>
        // تعديل عميل
        function editCustomer(customer) {
            document.getElementById('edit_customer_id').value = customer.id;
            document.getElementById('edit_customer_code').value = customer.customer_code;
            document.getElementById('edit_name').value = customer.name;
            document.getElementById('edit_email').value = customer.email || '';
            document.getElementById('edit_phone').value = customer.phone || '';
            document.getElementById('edit_address').value = customer.address || '';
            document.getElementById('edit_tax_number').value = customer.tax_number || '';
            document.getElementById('edit_credit_limit').value = customer.credit_limit;
            document.getElementById('edit_opening_balance').value = customer.opening_balance || '0.00';

            new bootstrap.Modal(document.getElementById('editCustomerModal')).show();
        }

        // عرض تفاصيل العميل
        function viewCustomer(customerId) {
            window.open(`customer_view.php?id=${customerId}`, '_blank');
        }

        // حذف عميل
        function deleteCustomer(customerId) {
            if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="customer_id" value="${customerId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // الحصول على رمز عميل ذكي
        function generateNewCustomerCode() {
            fetch('api_smart_numbers.php?action=get_next&entity_type=customer&prefix=CUS&padding=3')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('customer_code').value = data.number;
                        // عرض رسالة توضيحية
                        showSmartReservationMessage(data);
                    } else {
                        console.error('خطأ في الحصول على رمز ذكي:', data.error);
                        document.getElementById('customer_code').value = 'CUS001';
                    }
                })
                .catch(error => {
                    console.error('خطأ في النظام الذكي:', error);
                    document.getElementById('customer_code').value = 'CUS001';
                });
        }

        // عرض رسالة ذكية عن حالة الرقم
        function showSmartReservationMessage(data) {
            const codeField = document.getElementById('customer_code');
            const existingMessage = codeField.parentElement.querySelector('.smart-message');

            if (existingMessage) {
                existingMessage.remove();
            }

            if (data.success && data.is_reserved) {
                const message = document.createElement('div');
                message.className = 'smart-message small mt-1';

                if (data.is_recycled) {
                    message.className += ' text-success';
                    message.innerHTML = '<i class="fas fa-recycle me-1"></i>تم إعادة استخدام الرقم ' + data.number + ' (معاد تدويره) - سيتم تأكيده عند الحفظ';
                } else {
                    message.className += ' text-info';
                    message.innerHTML = '<i class="fas fa-bookmark me-1"></i>تم حجز الرقم الجديد ' + data.number + ' مؤقتاً - سيتم تأكيده عند الحفظ';
                }

                codeField.parentElement.appendChild(message);
            }
        }

        // إلغاء حجز الرقم الذكي
        function cancelCustomerReservation() {
            fetch('api_smart_numbers.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=cancel&entity_type=customer'
            })
            .then(response => response.json())
            .then(data => {
                console.log('تم إلغاء حجز رقم العميل:', data.message);
                // إزالة الرسالة الذكية
                const smartMessage = document.querySelector('.smart-message');
                if (smartMessage) {
                    smartMessage.remove();
                }
            })
            .catch(error => {
                console.error('خطأ في إلغاء الحجز الذكي:', error);
            });
        }

        // توليد رقم عند فتح النافذة
        document.addEventListener('DOMContentLoaded', function() {
            // توليد رقم عند فتح نافذة إضافة عميل
            const addCustomerModal = document.getElementById('addCustomerModal');
            if (addCustomerModal) {
                addCustomerModal.addEventListener('show.bs.modal', function() {
                    generateNewCustomerCode();
                });

                addCustomerModal.addEventListener('hide.bs.modal', function() {
                    cancelCustomerReservation();
                });
            }

            // إلغاء الحجز عند إغلاق الصفحة
            window.addEventListener('beforeunload', function() {
                cancelCustomerReservation();
            });

            // إلغاء الحجز عند النقر على زر الإلغاء
            const cancelButtons = document.querySelectorAll('[data-bs-dismiss="modal"]');
            cancelButtons.forEach(button => {
                button.addEventListener('click', function() {
                    if (this.closest('#addCustomerModal')) {
                        cancelCustomerReservation();
                    }
                });
            });
        });

        // البحث الديناميكي
        let searchTimeout;
        const searchInput = document.getElementById('customerSearch');
        const clearButton = document.getElementById('clearSearch');
        const tableBody = document.getElementById('customersTableBody');

        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const searchTerm = this.value.trim();

                // تأخير البحث لمدة 300 ملي ثانية
                searchTimeout = setTimeout(() => {
                    performSearch(searchTerm);
                }, 300);
            });
        }

        if (clearButton) {
            clearButton.addEventListener('click', function() {
                searchInput.value = '';
                performSearch('');
            });
        }

        function performSearch(searchTerm) {
            // إظهار مؤشر التحميل
            tableBody.innerHTML = '<tr><td colspan="8" class="text-center py-4"><i class="fas fa-spinner fa-spin fa-2x text-muted"></i><p class="text-muted mt-2">جاري البحث...</p></td></tr>';

            // إرسال طلب AJAX
            fetch(`ajax_search_customers.php?search=${encodeURIComponent(searchTerm)}`)
                .then(response => response.text())
                .then(data => {
                    tableBody.innerHTML = data;
                })
                .catch(error => {
                    console.error('خطأ في البحث:', error);
                    tableBody.innerHTML = '<tr><td colspan="8" class="text-center py-4 text-danger"><i class="fas fa-exclamation-triangle fa-2x mb-2"></i><p>حدث خطأ في البحث</p></td></tr>';
                });
        }

        // تحديث الوقت والتاريخ
        function updateDateTime() {
            const now = new Date();
            const timeElement = document.getElementById('current-time');
            const dateElement = document.getElementById('current-date');

            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString('ar-SA', {
                    hour: '2-digit', minute: '2-digit'
                });
            }
            if (dateElement) {
                dateElement.textContent = now.toLocaleDateString('ar-SA');
            }
        }
        setInterval(updateDateTime, 60000);
        updateDateTime();

        // فتح نافذة إضافة عميل إذا كان هناك معرف في الرابط
        document.addEventListener('DOMContentLoaded', function() {
            if (window.location.hash === '#add-customer') {
                const addCustomerModal = new bootstrap.Modal(document.getElementById('addCustomerModal'));
                addCustomerModal.show();
            }
        });
    </script>
</body>
</html>
