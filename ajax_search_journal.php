<?php
/**
 * البحث الديناميكي في دفتر اليومية
 * Dynamic Journal Search
 */

// تعريف الثابت للوصول
define('SEASYSTEM_ACCESS', true);

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/JournalEntry.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    exit('غير مصرح');
}

// التحقق من وجود مصطلح البحث
$search_term = $_GET['search'] ?? '';

// إنشاء كائن دفتر اليومية
$journal = new JournalEntry($pdo);

// البحث في القيود
if (!empty($search_term)) {
    $entries = $journal->search($search_term);
} else {
    $entries = $journal->getAll();
}

// حالات القيود
$entry_statuses = [
    'draft' => 'مسودة',
    'posted' => 'مرحل'
];

$reference_types = [
    'invoice' => 'فاتورة',
    'payment' => 'دفعة',
    'adjustment' => 'تسوية',
    'opening_balance' => 'رصيد ابتدائي'
];

// عرض النتائج
if (empty($entries)) {
    echo '<tr>
            <td colspan="7" class="text-center py-4">
                <i class="fas fa-search fa-2x text-muted mb-2"></i>
                <p class="text-muted">لا توجد قيود تطابق البحث</p>
            </td>
          </tr>';
} else {
    foreach ($entries as $entry) {
        // تحديد لون الحالة
        $status_class = $entry['status'] == 'posted' ? 'bg-success' : 'bg-warning';
        
        // تحديد لون نوع المرجع
        $ref_type_class = '';
        switch ($entry['reference_type']) {
            case 'invoice':
                $ref_type_class = 'bg-primary';
                break;
            case 'payment':
                $ref_type_class = 'bg-success';
                break;
            case 'adjustment':
                $ref_type_class = 'bg-warning';
                break;
            case 'opening_balance':
                $ref_type_class = 'bg-info';
                break;
            default:
                $ref_type_class = 'bg-secondary';
        }
        
        echo '<tr>
                <td>' . htmlspecialchars($entry['entry_number']) . '</td>
                <td>' . date('Y-m-d', strtotime($entry['entry_date'])) . '</td>
                <td>' . htmlspecialchars($entry['description']) . '</td>
                <td>
                    <span class="badge ' . $ref_type_class . '">' . 
                        ($reference_types[$entry['reference_type']] ?? $entry['reference_type']) . 
                    '</span>
                </td>
                <td>' . htmlspecialchars($entry['reference_number'] ?? '-') . '</td>
                <td class="text-end">' . number_format($entry['total_amount'], 2) . ' ' . CURRENCY_SYMBOL . '</td>
                <td>
                    <span class="badge ' . $status_class . '">' . 
                        ($entry_statuses[$entry['status']] ?? $entry['status']) . 
                    '</span>
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-info btn-sm" 
                                onclick="viewEntry(' . $entry['id'] . ')" title="عرض">
                            <i class="fas fa-eye"></i>
                            <small class="d-block">عرض</small>
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                onclick="editEntry(' . $entry['id'] . ')" title="تعديل">
                            <i class="fas fa-edit"></i>
                            <small class="d-block">تعديل</small>
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" 
                                onclick="printEntry(' . $entry['id'] . ')" title="طباعة">
                            <i class="fas fa-print"></i>
                            <small class="d-block">طباعة</small>
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" 
                                onclick="deleteEntry(' . $entry['id'] . ', \'' . htmlspecialchars($entry['entry_number']) . '\')" title="حذف">
                            <i class="fas fa-trash"></i>
                            <small class="d-block">حذف</small>
                        </button>
                    </div>
                </td>
              </tr>';
    }
}
?>
