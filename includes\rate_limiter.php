<?php
/**
 * SeaSystem - نظام تحديد معدل المحاولات
 * Rate Limiting System
 */

class RateLimiter {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->createTableIfNotExists();
    }
    
    /**
     * إنشاء جدول محاولات تسجيل الدخول إذا لم يكن موجوداً
     */
    private function createTableIfNotExists() {
        $sql = "CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_address VARCHAR(45) NOT NULL,
            username VARCHAR(100),
            attempts INT DEFAULT 1,
            last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            blocked_until TIMESTAMP NULL,
            INDEX idx_ip (ip_address),
            INDEX idx_username (username)
        )";
        
        $this->db->exec($sql);
    }
    
    /**
     * تسجيل محاولة دخول فاشلة
     */
    public function recordFailedAttempt($username = null) {
        $ip = $this->getClientIP();
        
        // البحث عن محاولات سابقة
        $sql = "SELECT * FROM login_attempts 
                WHERE ip_address = :ip 
                AND (username = :username OR username IS NULL)
                AND last_attempt > DATE_SUB(NOW(), INTERVAL 1 HOUR)";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':ip', $ip);
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            // تحديث المحاولات الموجودة
            $attempt = $stmt->fetch();
            $newAttempts = $attempt['attempts'] + 1;
            
            // حساب وقت الحظر
            $blockTime = $this->calculateBlockTime($newAttempts);
            
            $updateSql = "UPDATE login_attempts 
                         SET attempts = :attempts, 
                             blocked_until = :blocked_until,
                             username = :username
                         WHERE id = :id";
            
            $updateStmt = $this->db->prepare($updateSql);
            $updateStmt->bindParam(':attempts', $newAttempts);
            $updateStmt->bindParam(':blocked_until', $blockTime);
            $updateStmt->bindParam(':username', $username);
            $updateStmt->bindParam(':id', $attempt['id']);
            $updateStmt->execute();
            
        } else {
            // إضافة محاولة جديدة
            $insertSql = "INSERT INTO login_attempts (ip_address, username, attempts) 
                         VALUES (:ip, :username, 1)";
            
            $insertStmt = $this->db->prepare($insertSql);
            $insertStmt->bindParam(':ip', $ip);
            $insertStmt->bindParam(':username', $username);
            $insertStmt->execute();
        }
    }
    
    /**
     * التحقق من إمكانية تسجيل الدخول
     */
    public function canAttemptLogin($username = null) {
        $ip = $this->getClientIP();
        
        $sql = "SELECT * FROM login_attempts 
                WHERE ip_address = :ip 
                AND (username = :username OR username IS NULL)
                AND blocked_until > NOW()";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':ip', $ip);
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $attempt = $stmt->fetch();
            return [
                'allowed' => false,
                'blocked_until' => $attempt['blocked_until'],
                'attempts' => $attempt['attempts']
            ];
        }
        
        return ['allowed' => true];
    }
    
    /**
     * مسح المحاولات الناجحة
     */
    public function clearAttempts($username = null) {
        $ip = $this->getClientIP();
        
        $sql = "DELETE FROM login_attempts 
                WHERE ip_address = :ip 
                AND (username = :username OR username IS NULL)";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':ip', $ip);
        $stmt->bindParam(':username', $username);
        $stmt->execute();
    }
    
    /**
     * حساب وقت الحظر بناءً على عدد المحاولات
     */
    private function calculateBlockTime($attempts) {
        if ($attempts >= 10) {
            // حظر لمدة 24 ساعة
            return date('Y-m-d H:i:s', strtotime('+24 hours'));
        } elseif ($attempts >= 7) {
            // حظر لمدة 4 ساعات
            return date('Y-m-d H:i:s', strtotime('+4 hours'));
        } elseif ($attempts >= 5) {
            // حظر لمدة ساعة واحدة
            return date('Y-m-d H:i:s', strtotime('+1 hour'));
        } elseif ($attempts >= 3) {
            // حظر لمدة 15 دقيقة
            return date('Y-m-d H:i:s', strtotime('+15 minutes'));
        }
        
        return null;
    }
    
    /**
     * الحصول على عنوان IP الحقيقي للعميل
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * تنظيف المحاولات القديمة
     */
    public function cleanupOldAttempts() {
        $sql = "DELETE FROM login_attempts 
                WHERE last_attempt < DATE_SUB(NOW(), INTERVAL 24 HOUR)
                AND (blocked_until IS NULL OR blocked_until < NOW())";
        
        $this->db->exec($sql);
    }
}
?>
