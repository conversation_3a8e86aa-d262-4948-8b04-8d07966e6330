<?php
/**
 * SeaSystem - صفحة إدارة المخزون
 * Inventory Management Page
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/includes/sidebar.php';
require_once __DIR__ . '/classes/Inventory.php';

// التأكد من تسجيل الدخول
requireLogin();

$inventory = new Inventory();
$current_user = getCurrentUser();

// معالجة العمليات
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_movement':
                $movement_data = [
                    'movement_type' => $_POST['movement_type'],
                    'product_id' => $_POST['product_id'],
                    'warehouse_id' => $_POST['warehouse_id'],
                    'quantity' => $_POST['quantity'],
                    'unit_cost' => $_POST['unit_cost'],
                    'reference_type' => $_POST['reference_type'],
                    'reference_number' => $_POST['reference_number'],
                    'notes' => $_POST['notes'],
                    'created_by' => $current_user['id']
                ];

                $result = $inventory->addInventoryMovement($movement_data);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;

            case 'add_product':
                $product_data = [
                    'product_code' => $_POST['product_code'],
                    'barcode' => $_POST['barcode'] ?? '',
                    'product_name' => $_POST['product_name'],
                    'description' => $_POST['description'] ?? '',
                    'category_id' => $_POST['category_id'] ?? null,
                    'base_unit_id' => $_POST['base_unit_id'] ?? null,
                    'cost_price' => $_POST['cost_price'],
                    'selling_price' => $_POST['selling_price'],
                    'current_stock' => $_POST['current_stock'] ?? 0,
                    'min_stock_level' => $_POST['min_stock_level'] ?? 0,
                    'max_stock_level' => $_POST['max_stock_level'] ?? 0,
                    'is_active' => isset($_POST['is_active']) ? 1 : 0,
                    'created_by' => $current_user['id']
                ];

                $result = $inventory->addProduct($product_data);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;
        }
    }
}

// الحصول على المرشحات
$filters = [];
if (!empty($_GET['category_id'])) {
    $filters['category_id'] = $_GET['category_id'];
}
if (!empty($_GET['search'])) {
    $filters['search'] = $_GET['search'];
}
if (isset($_GET['low_stock']) && $_GET['low_stock']) {
    $filters['low_stock'] = true;
}

// الحصول على البيانات
$products = $inventory->getAllProducts($filters);
$categories = $inventory->getProductCategories();
$warehouses = $inventory->getWarehouses();
$units = $inventory->getUnitsOfMeasure();
$stats = $inventory->getInventoryStatistics();
$low_stock_products = $inventory->getLowStockProducts();

// البحث
$search_term = $_GET['search'] ?? '';

// أنواع الحركات
$movement_types = [
    'in' => 'وارد',
    'out' => 'صادر',
    'adjustment' => 'تسوية'
];

$reference_types = [
    'purchase' => 'مشتريات',
    'sale' => 'مبيعات',
    'adjustment' => 'تسوية',
    'opening' => 'رصيد ابتدائي'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المخزون - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <link href="assets/css/sidebar-only.css" rel="stylesheet">
    <style>
        .inventory-header {
            background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%) !important;
            color: white !important;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .inventory-header h1,
        .inventory-header p,
        .inventory-header i,
        .inventory-header * {
            color: white !important;
            opacity: 1 !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .inventory-header .btn-outline-light {
            color: white !important;
            border-color: rgba(255, 255, 255, 0.5) !important;
            background-color: transparent !important;
        }

        .inventory-header .btn-outline-light:hover {
            color: #fd7e14 !important;
            background-color: white !important;
            border-color: white !important;
        }

        /* ضمان ظهور جميع عناصر الهيدر */
        .inventory-header .col h1 {
            color: #ffffff !important;
            font-weight: bold !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
        }

        .inventory-header .col p {
            color: rgba(255, 255, 255, 0.95) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
        }

        .inventory-header .fas {
            color: white !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        }

        /* تنسيقات التبويبات الاحترافية */
        .card.mb-4 {
            border: none;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            border-radius: 16px;
            overflow: hidden;
        }

        .card-header.bg-light {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            border: none;
            padding: 0;
            position: relative;
        }

        .card-header.bg-light::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .nav-tabs {
            border: none;
            background: transparent;
            padding: 15px 20px 0;
            position: relative;
            z-index: 2;
        }

        .nav-tabs .nav-link {
            color: rgba(255, 255, 255, 0.7) !important;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px 12px 0 0;
            padding: 14px 24px;
            font-weight: 500;
            font-size: 14px;
            margin-right: 8px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .nav-tabs .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s;
        }

        .nav-tabs .nav-link:hover::before {
            left: 100%;
        }

        .nav-tabs .nav-link:hover {
            color: #ffffff !important;
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .nav-tabs .nav-link.active {
            color: #2c3e50 !important;
            background: #ffffff;
            border-color: rgba(255, 255, 255, 0.8);
            font-weight: 600;
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
            position: relative;
        }

        .nav-tabs .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px 2px 0 0;
        }

        .nav-tabs .nav-link i {
            color: inherit !important;
            margin-left: 8px;
            font-size: 16px;
            transition: transform 0.3s ease;
        }

        .nav-tabs .nav-link:hover i {
            transform: scale(1.1);
        }

        .nav-tabs .nav-link.active i {
            color: #667eea !important;
            transform: scale(1.05);
        }

        /* تأثيرات إضافية */
        @keyframes tabGlow {
            0% { box-shadow: 0 0 5px rgba(102, 126, 234, 0.3); }
            50% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.5); }
            100% { box-shadow: 0 0 5px rgba(102, 126, 234, 0.3); }
        }

        .nav-tabs .nav-link.active {
            animation: tabGlow 2s ease-in-out infinite;
        }

        .low-stock {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }

        .out-of-stock {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }

        .stock-indicator {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }

        .stock-level {
            height: 100%;
            transition: width 0.3s ease;
        }

        .stock-good {
            background: #28a745;
        }

        .stock-warning {
            background: #ffc107;
        }

        .stock-danger {
            background: #dc3545;
        }

        .product-card {
            transition: transform 0.2s ease;
            border-radius: 10px;
        }

        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .quick-actions {
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 1000;
        }

        .fab {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .fab:hover {
            transform: scale(1.1);
        }

        /* تنسيقات الهيدر الثابت */
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }

        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }

        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
        </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي الموحد -->
            <?php renderSidebar('inventory.php'); ?>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- رأس الصفحة -->
                <div class="inventory-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="mb-2">
                                <i class="fas fa-boxes me-3"></i>إدارة المخزون والمنتجات
                            </h1>
                            <p class="mb-0 opacity-75">إدارة شاملة للمنتجات والمخزون مع التتبع المباشر للكميات والحركات والإحصائيات</p>
                        </div>
                        <div class="col-auto">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-light btn-sm" onclick="addProduct()">
                                    <i class="fas fa-plus me-2"></i>إضافة منتج
                                </button>
                                <button type="button" class="btn btn-outline-light btn-sm" onclick="addMovement()">
                                    <i class="fas fa-exchange-alt me-2"></i>حركة مخزون
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسائل -->
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon primary">
                                    <i class="fas fa-boxes"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format($stats['total_products'] ?? 0); ?></h3>
                                    <p class="text-muted mb-0">إجمالي المنتجات</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon success">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format($stats['products_in_stock'] ?? 0); ?></h3>
                                    <p class="text-muted mb-0">منتجات متوفرة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format($stats['low_stock_products'] ?? 0); ?></h3>
                                    <p class="text-muted mb-0">مخزون منخفض</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon info">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format($stats['total_inventory_value'] ?? 0, 0); ?></h3>
                                    <p class="text-muted mb-0">قيمة المخزون</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تنبيهات المخزون المنخفض -->
                <?php if (!empty($low_stock_products)): ?>
                    <div class="alert alert-warning" role="alert">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>تنبيه: منتجات بمخزون منخفض
                        </h6>
                        <div class="row">
                            <?php foreach (array_slice($low_stock_products, 0, 3) as $product): ?>
                                <div class="col-md-4">
                                    <small>
                                        <strong><?php echo htmlspecialchars($product['product_name']); ?></strong><br>
                                        المتوفر: <?php echo $product['current_stock']; ?> | الحد الأدنى: <?php echo $product['min_stock_level']; ?>
                                    </small>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <?php if (count($low_stock_products) > 3): ?>
                            <hr>
                            <small>و <?php echo count($low_stock_products) - 3; ?> منتجات أخرى تحتاج إعادة طلب</small>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <!-- تبويبات إدارة المخزون والمنتجات -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <ul class="nav nav-tabs card-header-tabs" id="inventoryTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="inventory-tab" data-bs-toggle="tab" data-bs-target="#inventory-pane" type="button" role="tab">
                                    <i class="fas fa-boxes me-2"></i>عرض المخزون
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="add-product-tab" data-bs-toggle="tab" data-bs-target="#add-product-pane" type="button" role="tab">
                                    <i class="fas fa-plus me-2"></i>إضافة منتج
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="movements-tab" data-bs-toggle="tab" data-bs-target="#movements-pane" type="button" role="tab">
                                    <i class="fas fa-exchange-alt me-2"></i>حركات المخزون
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="warehouses-tab" data-bs-toggle="tab" data-bs-target="#warehouses-pane" type="button" role="tab">
                                    <i class="fas fa-warehouse me-2"></i>إدارة المستودعات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="reports-tab" data-bs-toggle="tab" data-bs-target="#reports-pane" type="button" role="tab">
                                    <i class="fas fa-chart-bar me-2"></i>التقارير
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- محتوى التبويبات -->
                <div class="tab-content" id="inventoryTabContent">
                    <!-- تبويب عرض المخزون -->
                    <div class="tab-pane fade show active" id="inventory-pane" role="tabpanel">
                        <!-- شريط البحث والفلترة -->
                <div class="card mb-4">
                    <div class="card-body">
                        <!-- البحث الديناميكي -->
                        <div class="row g-3 mb-3">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="inventorySearch"
                                           placeholder="البحث السريع في المخزون..."
                                           value="<?php echo htmlspecialchars($search_term); ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-outline-secondary w-100" id="clearInventorySearch">
                                    <i class="fas fa-times me-2"></i>مسح البحث
                                </button>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-outline-info w-100" id="toggleInventoryFilters">
                                    <i class="fas fa-filter me-2"></i>فلاتر متقدمة
                                </button>
                            </div>
                        </div>

                        <!-- الفلاتر المتقدمة (مخفية افتراضياً) -->
                        <div id="advancedInventoryFilters" class="border-top pt-3" style="display: none;">
                            <form method="GET" class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label">فئة المنتج</label>
                                    <select class="form-select" name="category_id">
                                        <option value="">جميع الفئات</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?php echo $category['id']; ?>"
                                                    <?php echo ($_GET['category_id'] ?? '') == $category['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($category['category_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">حالة المخزون</label>
                                    <select class="form-select" name="stock_status">
                                        <option value="">جميع الحالات</option>
                                        <option value="available" <?php echo ($_GET['stock_status'] ?? '') == 'available' ? 'selected' : ''; ?>>متوفر</option>
                                        <option value="low" <?php echo ($_GET['stock_status'] ?? '') == 'low' ? 'selected' : ''; ?>>مخزون منخفض</option>
                                        <option value="out" <?php echo ($_GET['stock_status'] ?? '') == 'out' ? 'selected' : ''; ?>>نفد المخزون</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">المستودع</label>
                                    <select class="form-select" name="warehouse_id">
                                        <option value="">جميع المستودعات</option>
                                        <?php foreach ($warehouses as $warehouse): ?>
                                            <option value="<?php echo $warehouse['id']; ?>"
                                                    <?php echo ($_GET['warehouse_id'] ?? '') == $warehouse['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($warehouse['warehouse_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="submit" class="btn btn-primary w-100 d-block">
                                        <i class="fas fa-search me-2"></i>تطبيق الفلاتر
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- جدول المنتجات -->
                <div class="table-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>قائمة المنتجات (<?php echo count($products); ?>)
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="inventoryTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>رمز المنتج</th>
                                        <th>اسم المنتج</th>
                                        <th>الفئة</th>
                                        <th>الوحدة</th>
                                        <th>المخزون الحالي</th>
                                        <th>مؤشر المخزون</th>
                                        <th>سعر التكلفة</th>
                                        <th>سعر البيع</th>
                                        <th>قيمة المخزون</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($products)): ?>
                                        <tr>
                                            <td colspan="10" class="text-center py-4">
                                                <i class="fas fa-boxes fa-2x text-muted mb-2"></i>
                                                <p class="text-muted mb-0">لا توجد منتجات حتى الآن</p>
                                                <button type="button" class="btn btn-primary mt-2" onclick="addProduct()">
                                                    إضافة أول منتج
                                                </button>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($products as $product): ?>
                                            <?php
                                            $stock_percentage = $product['max_stock_level'] > 0 ?
                                                ($product['current_stock'] / $product['max_stock_level']) * 100 : 0;

                                            $stock_class = 'stock-good';
                                            $row_class = '';

                                            if ($product['current_stock'] <= 0) {
                                                $stock_class = 'stock-danger';
                                                $row_class = 'out-of-stock';
                                            } elseif ($product['current_stock'] <= $product['min_stock_level']) {
                                                $stock_class = 'stock-warning';
                                                $row_class = 'low-stock';
                                            }
                                            ?>
                                            <tr class="<?php echo $row_class; ?>">
                                                <td>
                                                    <strong><?php echo htmlspecialchars($product['product_code']); ?></strong>
                                                    <?php if ($product['barcode']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($product['barcode']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="fw-bold"><?php echo htmlspecialchars($product['product_name']); ?></div>
                                                    <?php if ($product['description']): ?>
                                                        <small class="text-muted"><?php echo htmlspecialchars(substr($product['description'], 0, 50)); ?>...</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <?php echo htmlspecialchars($product['category_name']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars($product['unit_name']); ?></td>
                                                <td>
                                                    <span class="fw-bold <?php echo $product['current_stock'] <= 0 ? 'text-danger' : ($product['current_stock'] <= $product['min_stock_level'] ? 'text-warning' : 'text-success'); ?>">
                                                        <?php echo number_format($product['current_stock'], 2); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="stock-indicator">
                                                        <div class="stock-level <?php echo $stock_class; ?>"
                                                             style="width: <?php echo min(100, $stock_percentage); ?>%"></div>
                                                    </div>
                                                    <small class="text-muted">
                                                        الحد الأدنى: <?php echo $product['min_stock_level']; ?>
                                                    </small>
                                                </td>
                                                <td><?php echo number_format($product['cost_price'], 2) . ' ' . CURRENCY_SYMBOL; ?></td>
                                                <td><?php echo number_format($product['selling_price'], 2) . ' ' . CURRENCY_SYMBOL; ?></td>
                                                <td>
                                                    <strong><?php echo number_format($product['stock_value'], 2) . ' ' . CURRENCY_SYMBOL; ?></strong>
                                                </td>
                                                <td>
                                                    <div class="d-flex gap-2">
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                                    onclick="viewProduct(<?php echo $product['id']; ?>)">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">عرض</small>
                                                        </div>
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-success btn-sm"
                                                                    onclick="editProduct(<?php echo $product['id']; ?>)">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">تعديل</small>
                                                        </div>
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-info btn-sm"
                                                                    onclick="productMovements(<?php echo $product['id']; ?>)">
                                                                <i class="fas fa-history"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">تاريخ</small>
                                                        </div>
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-warning btn-sm"
                                                                    onclick="adjustStock(<?php echo $product['id']; ?>)">
                                                                <i class="fas fa-adjust"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">تعديل مخزون</small>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                    </div>
                    <!-- نهاية تبويب عرض المخزون -->

                    <!-- تبويب إضافة منتج -->
                    <div class="tab-pane fade" id="add-product-pane" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-plus me-2"></i>إضافة منتج
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" id="addProductForm">
                                    <input type="hidden" name="action" value="add_product">

                                    <div class="row">
                                        <!-- المعلومات الأساسية -->
                                        <div class="col-lg-6">
                                            <h6 class="text-primary mb-3">
                                                <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                                            </h6>

                                            <div class="mb-3">
                                                <label for="product_code" class="form-label">رمز المنتج *</label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control" id="product_code" name="product_code" required>
                                                    <button type="button" class="btn btn-outline-secondary" onclick="generateProductCode()" title="توليد رمز تلقائي">
                                                        <i class="fas fa-sync-alt"></i>
                                                    </button>
                                                </div>
                                                <div class="form-text">رمز فريد للمنتج (سيتم التحقق من عدم التكرار)</div>
                                            </div>

                                            <div class="mb-3">
                                                <label for="barcode" class="form-label">الباركود</label>
                                                <input type="text" class="form-control" id="barcode" name="barcode">
                                            </div>

                                            <div class="mb-3">
                                                <label for="product_name" class="form-label">اسم المنتج *</label>
                                                <input type="text" class="form-control" id="product_name" name="product_name" required>
                                            </div>

                                            <div class="mb-3">
                                                <label for="description" class="form-label">الوصف</label>
                                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="category_id" class="form-label">الفئة</label>
                                                    <select class="form-select" id="category_id" name="category_id">
                                                        <option value="">اختر الفئة</option>
                                                        <?php foreach ($categories as $category): ?>
                                                            <option value="<?php echo $category['id']; ?>">
                                                                <?php echo htmlspecialchars($category['category_name']); ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <label for="base_unit_id" class="form-label">وحدة القياس</label>
                                                    <select class="form-select" id="base_unit_id" name="base_unit_id">
                                                        <option value="">اختر الوحدة</option>
                                                        <?php foreach ($units as $unit): ?>
                                                            <option value="<?php echo $unit['id']; ?>">
                                                                <?php echo htmlspecialchars($unit['unit_name']); ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- الأسعار والمخزون -->
                                        <div class="col-lg-6">
                                            <h6 class="text-success mb-3">
                                                <i class="fas fa-dollar-sign me-2"></i>الأسعار والمخزون
                                            </h6>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="cost_price" class="form-label">سعر التكلفة *</label>
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" id="cost_price" name="cost_price"
                                                               pattern="[0-9]+([.][0-9]+)?" inputmode="decimal"
                                                               placeholder="مثل: 150.50" required>
                                                        <span class="input-group-text"><?php echo CURRENCY_SYMBOL; ?></span>
                                                    </div>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <label for="selling_price" class="form-label">سعر البيع *</label>
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" id="selling_price" name="selling_price"
                                                               pattern="[0-9]+([.][0-9]+)?" inputmode="decimal"
                                                               placeholder="مثل: 200.75" required>
                                                        <span class="input-group-text"><?php echo CURRENCY_SYMBOL; ?></span>
                                                    </div>
                                                    <div id="profit-info" class="small text-muted mt-1"></div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-4 mb-3">
                                                    <label for="current_stock" class="form-label">الرصيد الحالي</label>
                                                    <input type="number" class="form-control" id="current_stock" name="current_stock" step="0.001" min="0" value="0">
                                                </div>

                                                <div class="col-md-4 mb-3">
                                                    <label for="min_stock_level" class="form-label">الحد الأدنى</label>
                                                    <input type="number" class="form-control" id="min_stock_level" name="min_stock_level" step="0.001" min="0" value="0">
                                                </div>

                                                <div class="col-md-4 mb-3">
                                                    <label for="max_stock_level" class="form-label">الحد الأقصى</label>
                                                    <input type="number" class="form-control" id="max_stock_level" name="max_stock_level" step="0.001" min="0" value="0">
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                                    <label class="form-check-label" for="is_active">
                                                        منتج نشط
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="d-grid gap-2">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-save me-2"></i>حفظ المنتج
                                                </button>
                                                <button type="reset" class="btn btn-outline-secondary">
                                                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <!-- نهاية تبويب إضافة منتج -->

                    <!-- تبويب حركات المخزون -->
                    <div class="tab-pane fade" id="movements-pane" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-exchange-alt me-2"></i>حركات المخزون
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">سيتم إضافة قائمة حركات المخزون ونموذج إضافة حركة جديدة قريباً.</p>
                                <div class="text-center">
                                    <button type="button" class="btn btn-primary" onclick="addMovement()">
                                        <i class="fas fa-plus me-2"></i>إضافة حركة مخزون
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- نهاية تبويب حركات المخزون -->

                    <!-- تبويب التقارير -->
                    <div class="tab-pane fade" id="reports-pane" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>تقارير المخزون
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <div class="card border-primary">
                                            <div class="card-body text-center">
                                                <i class="fas fa-boxes fa-2x text-primary mb-2"></i>
                                                <h6>تقرير المخزون</h6>
                                                <button type="button" class="btn btn-primary btn-sm" onclick="inventoryReport()">
                                                    عرض التقرير
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <div class="card border-warning">
                                            <div class="card-body text-center">
                                                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                                                <h6>المخزون المنخفض</h6>
                                                <button type="button" class="btn btn-warning btn-sm" onclick="lowStockReport()">
                                                    عرض التقرير
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <div class="card border-info">
                                            <div class="card-body text-center">
                                                <i class="fas fa-exchange-alt fa-2x text-info mb-2"></i>
                                                <h6>حركات المخزون</h6>
                                                <button type="button" class="btn btn-info btn-sm" onclick="movementsReport()">
                                                    عرض التقرير
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- نهاية تبويب التقارير -->

                    <!-- تبويب إدارة المستودعات -->
                    <div class="tab-pane fade" id="warehouses-pane" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="fas fa-warehouse me-2"></i>إدارة المستودعات
                                    </h5>
                                    <button type="button" class="btn btn-primary btn-sm" onclick="addWarehouse()">
                                        <i class="fas fa-plus me-2"></i>إضافة مستودع جديد
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- قائمة المستودعات -->
                                <div class="row">
                                    <?php foreach ($warehouses as $warehouse): ?>
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="card border-secondary">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="card-title mb-0">
                                                        <i class="fas fa-warehouse text-primary me-2"></i>
                                                        <?php echo htmlspecialchars($warehouse['warehouse_name']); ?>
                                                    </h6>
                                                    <span class="badge bg-<?php echo $warehouse['is_active'] ? 'success' : 'secondary'; ?>">
                                                        <?php echo $warehouse['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                    </span>
                                                </div>
                                                <p class="card-text text-muted small mb-2">
                                                    <strong>الرمز:</strong> <?php echo htmlspecialchars($warehouse['warehouse_code']); ?>
                                                </p>
                                                <?php if (!empty($warehouse['location'])): ?>
                                                <p class="card-text text-muted small mb-2">
                                                    <i class="fas fa-map-marker-alt me-1"></i>
                                                    <?php echo htmlspecialchars($warehouse['location']); ?>
                                                </p>
                                                <?php endif; ?>
                                                <div class="d-flex gap-1">
                                                    <button type="button" class="btn btn-outline-primary btn-sm"
                                                            onclick="editWarehouse(<?php echo $warehouse['id']; ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-info btn-sm"
                                                            onclick="warehouseStock(<?php echo $warehouse['id']; ?>)">
                                                        <i class="fas fa-boxes"></i>
                                                    </button>
                                                    <?php if ($warehouse['warehouse_code'] !== 'MAIN'): ?>
                                                    <button type="button" class="btn btn-outline-danger btn-sm"
                                                            onclick="deleteWarehouse(<?php echo $warehouse['id']; ?>)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>

                                <?php if (empty($warehouses)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-warehouse fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد مستودعات</h5>
                                    <p class="text-muted">ابدأ بإضافة أول مستودع للنظام</p>
                                    <button type="button" class="btn btn-primary" onclick="addWarehouse()">
                                        <i class="fas fa-plus me-2"></i>إضافة مستودع جديد
                                    </button>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <!-- نهاية تبويب إدارة المستودعات -->

                </div>
                <!-- نهاية محتوى التبويبات -->
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات السريعة -->
    <div class="quick-actions">
        <button type="button" class="fab btn btn-primary" onclick="addProduct()" title="إضافة منتج">
            <i class="fas fa-plus"></i>
        </button>
        <button type="button" class="fab btn btn-success" onclick="addMovement()" title="حركة مخزون">
            <i class="fas fa-exchange-alt"></i>
        </button>
        <button type="button" class="fab btn btn-info" onclick="inventoryReport()" title="تقرير المخزون">
            <i class="fas fa-chart-bar"></i>
        </button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        // إضافة منتج جديد
        function addProduct() {
            showAddProductTab();
        }

        // عرض تبويب إضافة منتج
        function showAddProductTab() {
            const addProductTab = new bootstrap.Tab(document.getElementById('add-product-tab'));
            addProductTab.show();

            // تحديث الرابط ليعكس التبويب النشط
            if (history.replaceState) {
                history.replaceState(null, null, '#add-product-tab');
            }

            // التركيز على أول حقل في النموذج
            setTimeout(function() {
                const firstInput = document.querySelector('#add-product-pane input:not([type="hidden"])');
                if (firstInput) {
                    firstInput.focus();
                }
            }, 300);
        }

        // إضافة حركة مخزون
        function addMovement() {
            window.location.href = 'inventory_movement.php';
        }

        // عرض المنتج
        function viewProduct(productId) {
            alert('سيتم إضافة صفحة عرض المنتج قريباً');
            // window.location.href = `product_view.php?id=${productId}`;
        }

        // تعديل المنتج
        function editProduct(productId) {
            alert('سيتم إضافة صفحة تعديل المنتج قريباً');
            // window.location.href = `product_edit.php?id=${productId}`;
        }

        // حركات المنتج
        function productMovements(productId) {
            alert('سيتم إضافة صفحة حركات المنتج قريباً');
            // window.location.href = `product_movements.php?id=${productId}`;
        }

        // تسوية المخزون
        function adjustStock(productId) {
            alert('سيتم إضافة صفحة تسوية المخزون قريباً');
            // window.location.href = `stock_adjustment.php?id=${productId}`;
        }

        // تقرير المخزون
        function inventoryReport() {
            window.open('report_inventory.php', '_blank');
        }

        // تقرير المخزون المنخفض
        function lowStockReport() {
            window.open('report_inventory.php?low_stock=1', '_blank');
        }

        // تقرير حركات المخزون
        function movementsReport() {
            window.open('report_stock_movements.php', '_blank');
        }

        // إدارة المستودعات
        function addWarehouse() {
            window.location.href = 'warehouse_create.php';
        }

        function editWarehouse(warehouseId) {
            window.location.href = `warehouse_edit.php?id=${warehouseId}`;
        }

        function warehouseStock(warehouseId) {
            window.location.href = `warehouse_stock.php?id=${warehouseId}`;
        }

        function deleteWarehouse(warehouseId) {
            if (confirm('هل أنت متأكد من حذف هذا المستودع؟')) {
                window.location.href = `warehouse_delete.php?id=${warehouseId}`;
            }
        }

        // إظهار/إخفاء الفلاتر المتقدمة
        document.getElementById('toggleInventoryFilters').addEventListener('click', function() {
            const filtersDiv = document.getElementById('advancedInventoryFilters');
            const isVisible = filtersDiv.style.display !== 'none';

            if (isVisible) {
                filtersDiv.style.display = 'none';
                this.innerHTML = '<i class="fas fa-filter me-2"></i>فلاتر متقدمة';
            } else {
                filtersDiv.style.display = 'block';
                this.innerHTML = '<i class="fas fa-times me-2"></i>إخفاء الفلاتر';
            }
        });

        // البحث الديناميكي في المخزون
        const inventorySearchInput = document.getElementById('inventorySearch');
        const clearInventorySearchBtn = document.getElementById('clearInventorySearch');
        const inventoryTableBody = document.querySelector('#inventoryTable tbody');
        let searchTimeout;

        // البحث عند الكتابة
        inventorySearchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const searchTerm = this.value.trim();

            searchTimeout = setTimeout(() => {
                performInventorySearch(searchTerm);
            }, 300); // انتظار 300 ملي ثانية بعد انتهاء الكتابة
        });

        // مسح البحث
        clearInventorySearchBtn.addEventListener('click', function() {
            inventorySearchInput.value = '';
            performInventorySearch('');
        });

        // تنفيذ البحث
        function performInventorySearch(searchTerm) {
            // إظهار مؤشر التحميل
            inventoryTableBody.innerHTML = '<tr><td colspan="10" class="text-center py-4"><i class="fas fa-spinner fa-spin fa-2x text-muted"></i><p class="text-muted mt-2">جاري البحث...</p></td></tr>';

            // إرسال طلب AJAX
            fetch(`ajax_search_inventory.php?search=${encodeURIComponent(searchTerm)}`)
                .then(response => response.text())
                .then(data => {
                    inventoryTableBody.innerHTML = data;
                })
                .catch(error => {
                    console.error('خطأ في البحث:', error);
                    inventoryTableBody.innerHTML = '<tr><td colspan="10" class="text-center py-4 text-danger"><i class="fas fa-exclamation-triangle fa-2x mb-2"></i><p>حدث خطأ في البحث</p></td></tr>';
                });
        }

        // توليد رمز منتج تلقائي
        function generateProductCode() {
            fetch('get_next_number.php?type=product')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('product_code').value = data.number;
                    } else {
                        // رقم احتياطي في حالة الخطأ
                        document.getElementById('product_code').value = 'PRD001';
                    }
                })
                .catch(error => {
                    console.error('خطأ في توليد رمز المنتج:', error);
                    document.getElementById('product_code').value = 'PRD001';
                });
        }

        // حساب هامش الربح
        function calculateProfit() {
            const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
            const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;

            if (costPrice > 0 && sellingPrice > 0) {
                const profit = sellingPrice - costPrice;
                const profitMargin = ((profit / costPrice) * 100).toFixed(2);

                // عرض هامش الربح في رسالة صغيرة
                const profitInfo = document.getElementById('profit-info');
                if (profitInfo) {
                    if (profit > 0) {
                        profitInfo.innerHTML = `<i class="fas fa-arrow-up text-success me-1"></i>هامش الربح: ${profit.toFixed(2)} <?php echo CURRENCY_SYMBOL; ?> (${profitMargin}%)`;
                        profitInfo.className = 'text-success small';
                    } else if (profit < 0) {
                        profitInfo.innerHTML = `<i class="fas fa-arrow-down text-danger me-1"></i>خسارة: ${Math.abs(profit).toFixed(2)} <?php echo CURRENCY_SYMBOL; ?> (${profitMargin}%)`;
                        profitInfo.className = 'text-danger small';
                    } else {
                        profitInfo.innerHTML = `<i class="fas fa-minus text-warning me-1"></i>لا يوجد ربح`;
                        profitInfo.className = 'text-warning small';
                    }
                }
            } else {
                const profitInfo = document.getElementById('profit-info');
                if (profitInfo) {
                    profitInfo.innerHTML = '';
                }
            }
        }

        // تفعيل حساب هامش الربح عند تغيير الأسعار
        document.addEventListener('DOMContentLoaded', function() {
            const costPriceInput = document.getElementById('cost_price');
            const sellingPriceInput = document.getElementById('selling_price');

            if (costPriceInput && sellingPriceInput) {
                costPriceInput.addEventListener('input', calculateProfit);
                sellingPriceInput.addEventListener('input', calculateProfit);

                // توليد رمز منتج تلقائي عند تحميل الصفحة
                generateProductCode();
            }

            // تفعيل التبويب عند النقر على زر إضافة منتج
            const addProductButtons = document.querySelectorAll('[onclick="addProduct()"]');
            addProductButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    showAddProductTab();
                });
            });

            // فتح تبويب إضافة منتج تلقائياً عند وجود hash في الرابط
            if (window.location.hash === '#add-product-tab') {
                setTimeout(function() {
                    showAddProductTab();
                }, 100);
            }
        });

        // تحسين تجربة المستخدم عند إرسال نموذج إضافة منتج
        const addProductForm = document.getElementById('addProductForm');
        if (addProductForm) {
            addProductForm.addEventListener('submit', function(e) {
                const submitButton = this.querySelector('button[type="submit"]');
                if (submitButton) {
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
                    submitButton.disabled = true;
                }
            });
        }

        // تحديث الوقت والتاريخ
        function updateDateTime() {
            const now = new Date();
            const timeElement = document.getElementById('current-time');
            const dateElement = document.getElementById('current-date');

            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString('ar-SA', {
                    hour: '2-digit', minute: '2-digit'
                });
            }
            if (dateElement) {
                dateElement.textContent = now.toLocaleDateString('ar-SA');
            }
        }
        setInterval(updateDateTime, 60000);
        updateDateTime();
    </script>

    <script src="assets/js/number-input-enhancement.js"></script>
    <script src="assets/js/duplicate-check.js"></script>
</body>
</html>
