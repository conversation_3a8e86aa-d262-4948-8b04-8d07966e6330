<?php
/**
 * SeaSystem - البحث في المستخدمين
 * Users Search AJAX
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/User.php';

// التأكد من تسجيل الدخول وصلاحيات المدير
if (!isLoggedIn() || !hasPermission('admin')) {
    echo '<tr><td colspan="7" class="text-center py-4 text-danger">غير مصرح لك بالوصول</td></tr>';
    exit();
}

$user = new User();
$current_user = getCurrentUser();

// الحصول على مصطلح البحث
$searchTerm = $_GET['search'] ?? '';

// البحث في المستخدمين
if (!empty($searchTerm)) {
    $users = $user->search($searchTerm);
} else {
    $users = $user->getAll();
}

// أنواع المستخدمين
$user_roles = [
    'admin' => 'مدير النظام',
    'financial_manager' => 'مدير مالي',
    'accountant' => 'محاسب',
    'inventory_manager' => 'مدير مخزون',
    'sales_manager' => 'مدير مبيعات',
    'purchase_manager' => 'مدير مشتريات',
    'cashier' => 'أمين صندوق',
    'data_entry' => 'مدخل بيانات',
    'viewer' => 'مستعرض فقط'
];

// عرض النتائج
if (empty($users)) {
    echo '<tr>
            <td colspan="7" class="text-center py-4">
                <i class="fas fa-search fa-2x text-muted mb-2"></i>
                <p class="text-muted">لا توجد مستخدمين تطابق البحث</p>
            </td>
          </tr>';
} else {
    foreach ($users as $usr) {
        // تحديد لون الدور
        $role_class = '';
        switch ($usr['role']) {
            case 'admin':
                $role_class = 'bg-danger';
                break;
            case 'accountant':
                $role_class = 'bg-warning';
                break;
            case 'user':
                $role_class = 'bg-info';
                break;
        }

        echo '<tr>
                <td><strong>' . htmlspecialchars($usr['username']) . '</strong></td>
                <td>' . htmlspecialchars($usr['full_name']) . '</td>
                <td>' . htmlspecialchars($usr['email']) . '</td>
                <td>
                    <span class="badge ' . $role_class . '">' .
                        $user_roles[$usr['role']] .
                    '</span>
                </td>
                <td>' .
                    ($usr['is_active'] ?
                        '<span class="badge bg-success">نشط</span>' :
                        '<span class="badge bg-secondary">غير نشط</span>') .
                '</td>
                <td>' . date('Y-m-d', strtotime($usr['created_at'])) . '</td>
                <td>
                    <div class="d-flex gap-2">
                        <div class="text-center">
                            <button type="button" class="btn btn-outline-primary btn-sm"
                                    onclick="editUser(' . htmlspecialchars(json_encode($usr)) . ')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <small class="d-block text-muted mt-1">تعديل</small>
                        </div>' .
                        ($usr['id'] != $current_user['id'] ?
                            '<div class="text-center">
                                <button type="button" class="btn btn-outline-warning btn-sm"
                                        onclick="toggleUserStatus(' . $usr['id'] . ', ' . ($usr['is_active'] ? 'false' : 'true') . ')">
                                    <i class="fas fa-' . ($usr['is_active'] ? 'ban' : 'check') . '"></i>
                                </button>
                                <small class="d-block text-muted mt-1">' . ($usr['is_active'] ? 'تعطيل' : 'تفعيل') . '</small>
                            </div>
                            <div class="text-center">
                                <button type="button" class="btn btn-outline-danger btn-sm"
                                        onclick="deleteUser(' . $usr['id'] . ', \'' . htmlspecialchars($usr['username']) . '\')">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <small class="d-block text-muted mt-1">حذف</small>
                            </div>' : '') .
                    '</div>
                </td>
              </tr>';
    }
}
?>
