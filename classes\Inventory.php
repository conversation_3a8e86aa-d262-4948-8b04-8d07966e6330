<?php
/**
 * SeaSystem - فئة إدارة المخزون
 * Inventory Management Class
 */

require_once __DIR__ . '/../config/database.php';

class Inventory {
    private $db;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->createTablesIfNotExist();
    }

    /**
     * إنشاء الجداول المطلوبة إذا لم تكن موجودة
     */
    private function createTablesIfNotExist() {
        try {
            // إنشاء جدول فئات المنتجات
            $sql = "CREATE TABLE IF NOT EXISTS product_categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                category_name VARCHAR(100) NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $this->db->exec($sql);

            // إنشاء جدول وحدات القياس
            $sql = "CREATE TABLE IF NOT EXISTS units_of_measure (
                id INT AUTO_INCREMENT PRIMARY KEY,
                unit_name VARCHAR(50) NOT NULL,
                unit_symbol VARCHAR(10),
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $this->db->exec($sql);

            // إنشاء جدول المنتجات
            $sql = "CREATE TABLE IF NOT EXISTS products (
                id INT AUTO_INCREMENT PRIMARY KEY,
                product_code VARCHAR(50) UNIQUE NOT NULL,
                barcode VARCHAR(100),
                product_name VARCHAR(255) NOT NULL,
                description TEXT,
                category_id INT,
                base_unit_id INT,
                cost_price DECIMAL(15,2) DEFAULT 0.00,
                selling_price DECIMAL(15,2) DEFAULT 0.00,
                current_stock DECIMAL(15,3) DEFAULT 0.000,
                min_stock_level DECIMAL(15,3) DEFAULT 0.000,
                max_stock_level DECIMAL(15,3) DEFAULT 0.000,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES product_categories(id) ON DELETE SET NULL,
                FOREIGN KEY (base_unit_id) REFERENCES units_of_measure(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $this->db->exec($sql);

            // إدراج بيانات افتراضية للفئات
            $sql = "INSERT IGNORE INTO product_categories (id, category_name, description) VALUES
                    (1, 'إلكترونيات', 'أجهزة إلكترونية ومعدات تقنية'),
                    (2, 'مكتبية', 'مستلزمات مكتبية وقرطاسية'),
                    (3, 'أثاث', 'أثاث مكتبي ومنزلي'),
                    (4, 'أخرى', 'منتجات متنوعة')";
            $this->db->exec($sql);

            // إدراج بيانات افتراضية لوحدات القياس
            $sql = "INSERT IGNORE INTO units_of_measure (id, unit_name, unit_symbol) VALUES
                    (1, 'قطعة', 'قطعة'),
                    (2, 'كيلوجرام', 'كجم'),
                    (3, 'متر', 'م'),
                    (4, 'لتر', 'لتر'),
                    (5, 'صندوق', 'صندوق'),
                    (6, 'متر مربع', 'م²')";
            $this->db->exec($sql);

            // إدراج منتجات الجرانيت والرخام
            $sql = "INSERT IGNORE INTO products (id, product_code, product_name, description, category_id, base_unit_id, cost_price, selling_price, current_stock, min_stock_level, max_stock_level, is_active, created_by) VALUES
                    -- منتجات الجرانيت
                    (1, 'GRN001', 'جرانيت نيوحلايب', 'جرانيت نيوحلايب عالي الجودة، مناسب للأرضيات والجدران', 1, 6, 120.00, 180.00, 50.000, 10.000, 200.000, 1, 1),
                    (2, 'GRN002', 'جرانيت جندولا', 'جرانيت جندولا بتصميم أنيق، مقاوم للخدش والبقع', 1, 6, 135.00, 200.00, 45.000, 8.000, 180.000, 1, 1),
                    (3, 'GRN003', 'جرانيت رمادي', 'جرانيت رمادي كلاسيكي، يناسب جميع التصاميم الحديثة', 1, 6, 110.00, 165.00, 60.000, 12.000, 250.000, 1, 1),
                    (4, 'GRN004', 'جرانيت احمر اصواني', 'جرانيت احمر اصواني فاخر، لون مميز وجودة عالية', 1, 6, 150.00, 225.00, 35.000, 7.000, 150.000, 1, 1),
                    (5, 'GRN005', 'جرانيت اسود اصواني', 'جرانيت اسود اصواني أنيق، مثالي للمطابخ الفاخرة', 1, 6, 160.00, 240.00, 40.000, 8.000, 160.000, 1, 1),
                    -- منتجات الرخام
                    (6, 'MRB001', 'رخام تريستا', 'رخام تريستا طبيعي، نعومة فائقة ولمعان طبيعي', 2, 6, 200.00, 300.00, 30.000, 6.000, 120.000, 1, 1),
                    (7, 'MRB002', 'رخام صني منيا', 'رخام صني منيا بلون ذهبي دافئ، مناسب للديكورات الكلاسيكية', 2, 6, 180.00, 270.00, 35.000, 7.000, 140.000, 1, 1),
                    (8, 'MRB003', 'رخام سلفيا', 'رخام سلفيا بعروق طبيعية جميلة، جودة ممتازة', 2, 6, 220.00, 330.00, 25.000, 5.000, 100.000, 1, 1),
                    (9, 'MRB004', 'رخام جلاله', 'رخام جلاله أبيض ناصع، مثالي للحمامات والمطابخ', 2, 6, 190.00, 285.00, 40.000, 8.000, 160.000, 1, 1),
                    (10, 'MRB005', 'رخام مللي جراي', 'رخام مللي جراي بدرجات رمادية أنيقة، عصري وجذاب', 2, 6, 210.00, 315.00, 28.000, 6.000, 110.000, 1, 1),
                    (11, 'MRB006', 'رخام سماحه', 'رخام سماحه بلون كريمي فاتح، يضفي دفء على المكان', 2, 6, 175.00, 260.00, 45.000, 9.000, 180.000, 1, 1),
                    -- منتجات عامة
                    (12, 'ACC001', 'مادة لاصقة للجرانيت', 'مادة لاصقة عالية الجودة مخصصة للجرانيت والرخام', 3, 4, 25.00, 40.00, 100.000, 20.000, 500.000, 1, 1),
                    (13, 'ACC002', 'مادة تلميع الرخام', 'مادة تلميع متخصصة للرخام، تحافظ على اللمعان الطبيعي', 3, 4, 35.00, 55.00, 80.000, 15.000, 300.000, 1, 1),
                    (14, 'TOL001', 'أدوات قطع وتشكيل', 'مجموعة أدوات متخصصة لقطع وتشكيل الجرانيت والرخام', 4, 1, 150.00, 250.00, 20.000, 5.000, 50.000, 1, 1)";
            $this->db->exec($sql);

        } catch (Exception $e) {
            // تجاهل الأخطاء إذا كانت الجداول موجودة
        }
    }

    /**
     * الحصول على جميع المنتجات
     */
    public function getAllProducts($filters = []) {
        try {
            $sql = "SELECT p.*,
                           pc.category_name,
                           u.unit_name,
                           (p.current_stock * p.cost_price) as stock_value
                    FROM products p
                    LEFT JOIN product_categories pc ON p.category_id = pc.id
                    LEFT JOIN units_of_measure u ON p.base_unit_id = u.id
                    WHERE p.is_active = 1";

            $params = [];

            // تطبيق المرشحات
            if (!empty($filters['category_id'])) {
                $sql .= " AND p.category_id = :category_id";
                $params[':category_id'] = $filters['category_id'];
            }

            if (!empty($filters['search'])) {
                $sql .= " AND (p.product_code LIKE :search OR p.product_name LIKE :search OR p.barcode LIKE :search)";
                $params[':search'] = "%{$filters['search']}%";
            }

            if (isset($filters['low_stock']) && $filters['low_stock']) {
                $sql .= " AND p.current_stock <= p.min_stock_level";
            }

            $sql .= " ORDER BY p.product_name";

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على منتج واحد
     */
    public function getProductById($product_id) {
        try {
            $sql = "SELECT p.*,
                           pc.category_name,
                           u.unit_name
                    FROM products p
                    LEFT JOIN product_categories pc ON p.category_id = pc.id
                    LEFT JOIN units_of_measure u ON p.base_unit_id = u.id
                    WHERE p.id = :product_id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':product_id', $product_id);
            $stmt->execute();

            return $stmt->fetch();
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * إضافة منتج جديد
     */
    public function addProduct($product_data) {
        try {
            $this->db->beginTransaction();

            // التحقق من عدم تكرار رمز المنتج
            if ($this->isProductCodeExists($product_data['product_code'])) {
                throw new Exception('رمز المنتج "' . $product_data['product_code'] . '" موجود مسبقاً');
            }

            // التحقق من عدم تكرار اسم المنتج
            if ($this->isProductNameExists($product_data['product_name'])) {
                throw new Exception('اسم المنتج "' . $product_data['product_name'] . '" موجود مسبقاً');
            }

            // التحقق من الباركود إذا تم إدخاله
            if (!empty($product_data['barcode']) && $this->isBarcodeExists($product_data['barcode'])) {
                throw new Exception('الباركود "' . $product_data['barcode'] . '" موجود مسبقاً');
            }

            $sql = "INSERT INTO products (
                        product_code, barcode, product_name, description, category_id, base_unit_id,
                        cost_price, selling_price, current_stock, min_stock_level, max_stock_level,
                        is_active, created_by
                    ) VALUES (
                        :product_code, :barcode, :product_name, :description, :category_id, :base_unit_id,
                        :cost_price, :selling_price, :current_stock, :min_stock_level, :max_stock_level,
                        :is_active, :created_by
                    )";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':product_code', $product_data['product_code']);
            $stmt->bindParam(':barcode', $product_data['barcode']);
            $stmt->bindParam(':product_name', $product_data['product_name']);
            $stmt->bindParam(':description', $product_data['description']);
            $stmt->bindParam(':category_id', $product_data['category_id']);
            $stmt->bindParam(':base_unit_id', $product_data['base_unit_id']);
            $stmt->bindParam(':cost_price', $product_data['cost_price']);
            $stmt->bindParam(':selling_price', $product_data['selling_price']);
            $stmt->bindParam(':current_stock', $product_data['current_stock']);
            $stmt->bindParam(':min_stock_level', $product_data['min_stock_level']);
            $stmt->bindParam(':max_stock_level', $product_data['max_stock_level']);
            $stmt->bindParam(':is_active', $product_data['is_active']);
            $stmt->bindParam(':created_by', $product_data['created_by']);

            if (!$stmt->execute()) {
                throw new Exception('فشل في إضافة المنتج');
            }

            $product_id = $this->db->lastInsertId();

            // إضافة حركة مخزون ابتدائية إذا كان هناك رصيد
            if ($product_data['current_stock'] > 0) {
                $this->addInventoryMovement([
                    'movement_type' => 'in',
                    'product_id' => $product_id,
                    'warehouse_id' => 1, // المستودع الافتراضي
                    'quantity' => $product_data['current_stock'],
                    'unit_cost' => $product_data['cost_price'],
                    'reference_type' => 'opening',
                    'reference_number' => 'رصيد ابتدائي',
                    'notes' => 'رصيد ابتدائي للمنتج',
                    'created_by' => $product_data['created_by']
                ]);
            }

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'تم إضافة المنتج بنجاح',
                'product_id' => $product_id
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'message' => 'خطأ في إضافة المنتج: ' . $e->getMessage()
            ];
        }
    }

    /**
     * تحديث منتج
     */
    public function updateProduct($product_id, $product_data) {
        try {
            $this->db->beginTransaction();

            // التحقق من وجود المنتج
            $existing_product = $this->getProductById($product_id);
            if (!$existing_product) {
                throw new Exception('المنتج غير موجود');
            }

            // التحقق من عدم تكرار رمز المنتج
            if ($product_data['product_code'] != $existing_product['product_code'] &&
                $this->isProductCodeExists($product_data['product_code'])) {
                throw new Exception('رمز المنتج موجود مسبقاً');
            }

            // التحقق من الباركود
            if (!empty($product_data['barcode']) &&
                $product_data['barcode'] != $existing_product['barcode'] &&
                $this->isBarcodeExists($product_data['barcode'])) {
                throw new Exception('الباركود موجود مسبقاً');
            }

            $sql = "UPDATE products SET
                        product_code = :product_code,
                        barcode = :barcode,
                        product_name = :product_name,
                        description = :description,
                        category_id = :category_id,
                        base_unit_id = :base_unit_id,
                        cost_price = :cost_price,
                        selling_price = :selling_price,
                        min_stock_level = :min_stock_level,
                        max_stock_level = :max_stock_level,
                        is_active = :is_active,
                        updated_at = NOW()
                    WHERE id = :product_id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':product_code', $product_data['product_code']);
            $stmt->bindParam(':barcode', $product_data['barcode']);
            $stmt->bindParam(':product_name', $product_data['product_name']);
            $stmt->bindParam(':description', $product_data['description']);
            $stmt->bindParam(':category_id', $product_data['category_id']);
            $stmt->bindParam(':base_unit_id', $product_data['base_unit_id']);
            $stmt->bindParam(':cost_price', $product_data['cost_price']);
            $stmt->bindParam(':selling_price', $product_data['selling_price']);
            $stmt->bindParam(':min_stock_level', $product_data['min_stock_level']);
            $stmt->bindParam(':max_stock_level', $product_data['max_stock_level']);
            $stmt->bindParam(':is_active', $product_data['is_active']);
            $stmt->bindParam(':product_id', $product_id);

            if (!$stmt->execute()) {
                throw new Exception('فشل في تحديث المنتج');
            }

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'تم تحديث المنتج بنجاح'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'message' => 'خطأ في تحديث المنتج: ' . $e->getMessage()
            ];
        }
    }

    /**
     * إضافة حركة مخزون
     */
    public function addInventoryMovement($movement_data) {
        try {
            $this->db->beginTransaction();

            // توليد رقم الحركة
            if (empty($movement_data['movement_number'])) {
                $movement_data['movement_number'] = $this->generateMovementNumber($movement_data['movement_type']);
            }

            // إدراج الحركة
            $sql = "INSERT INTO inventory_movements (
                        movement_number, movement_type, movement_date, product_id, warehouse_id,
                        quantity, unit_cost, total_cost, reference_type, reference_number,
                        notes, created_by
                    ) VALUES (
                        :movement_number, :movement_type, :movement_date, :product_id, :warehouse_id,
                        :quantity, :unit_cost, :total_cost, :reference_type, :reference_number,
                        :notes, :created_by
                    )";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':movement_number', $movement_data['movement_number']);
            $stmt->bindParam(':movement_type', $movement_data['movement_type']);
            $stmt->bindParam(':movement_date', $movement_data['movement_date'] ?? date('Y-m-d'));
            $stmt->bindParam(':product_id', $movement_data['product_id']);
            $stmt->bindParam(':warehouse_id', $movement_data['warehouse_id']);
            $stmt->bindParam(':quantity', $movement_data['quantity']);
            $stmt->bindParam(':unit_cost', $movement_data['unit_cost']);
            $stmt->bindParam(':total_cost', $movement_data['total_cost'] ?? ($movement_data['quantity'] * $movement_data['unit_cost']));
            $stmt->bindParam(':reference_type', $movement_data['reference_type']);
            $stmt->bindParam(':reference_number', $movement_data['reference_number']);
            $stmt->bindParam(':notes', $movement_data['notes']);
            $stmt->bindParam(':created_by', $movement_data['created_by']);

            if (!$stmt->execute()) {
                throw new Exception('فشل في إضافة حركة المخزون');
            }

            // تحديث رصيد المنتج
            $this->updateProductStock($movement_data['product_id'], $movement_data['warehouse_id']);

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'تم إضافة حركة المخزون بنجاح',
                'movement_number' => $movement_data['movement_number']
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'message' => 'خطأ في إضافة حركة المخزون: ' . $e->getMessage()
            ];
        }
    }

    /**
     * الحصول على حركات المخزون
     */
    public function getInventoryMovements($filters = []) {
        try {
            $sql = "SELECT im.*,
                           p.product_code, p.product_name,
                           w.warehouse_name,
                           u.full_name as created_by_name
                    FROM inventory_movements im
                    JOIN products p ON im.product_id = p.id
                    JOIN warehouses w ON im.warehouse_id = w.id
                    LEFT JOIN users u ON im.created_by = u.id
                    WHERE 1=1";

            $params = [];

            // تطبيق المرشحات
            if (!empty($filters['product_id'])) {
                $sql .= " AND im.product_id = :product_id";
                $params[':product_id'] = $filters['product_id'];
            }

            if (!empty($filters['warehouse_id'])) {
                $sql .= " AND im.warehouse_id = :warehouse_id";
                $params[':warehouse_id'] = $filters['warehouse_id'];
            }

            if (!empty($filters['movement_type'])) {
                $sql .= " AND im.movement_type = :movement_type";
                $params[':movement_type'] = $filters['movement_type'];
            }

            if (!empty($filters['date_from'])) {
                $sql .= " AND im.movement_date >= :date_from";
                $params[':date_from'] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $sql .= " AND im.movement_date <= :date_to";
                $params[':date_to'] = $filters['date_to'];
            }

            $sql .= " ORDER BY im.movement_date DESC, im.id DESC";

            if (!empty($filters['limit'])) {
                $sql .= " LIMIT " . intval($filters['limit']);
            }

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على حركات المخزون للتقرير
     */
    public function getStockMovements($filters = []) {
        try {
            // إنشاء جدول حركات وهمي إذا لم يكن موجوداً
            $this->createMovementsTableIfNotExists();

            $sql = "SELECT
                        DATE(created_at) as movement_date,
                        'in' as movement_type,
                        'initial' as reference_type,
                        '' as reference_number,
                        current_stock as quantity,
                        product_code,
                        product_name,
                        'رصيد افتتاحي' as notes
                    FROM products
                    WHERE current_stock > 0";

            $params = [];

            // فلترة بالتاريخ
            if (!empty($filters['date_from'])) {
                $sql .= " AND DATE(created_at) >= :date_from";
                $params[':date_from'] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $sql .= " AND DATE(created_at) <= :date_to";
                $params[':date_to'] = $filters['date_to'];
            }

            // فلترة بالمنتج
            if (!empty($filters['product_id'])) {
                $sql .= " AND id = :product_id";
                $params[':product_id'] = $filters['product_id'];
            }

            // فلترة بنوع الحركة
            if (!empty($filters['movement_type'])) {
                $sql .= " AND 'in' = :movement_type";
                $params[':movement_type'] = $filters['movement_type'];
            }

            // فلترة بنوع المرجع
            if (!empty($filters['reference_type'])) {
                $sql .= " AND 'initial' = :reference_type";
                $params[':reference_type'] = $filters['reference_type'];
            }

            $sql .= " ORDER BY created_at DESC";

            $stmt = $this->db->prepare($sql);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * إنشاء جدول حركات المخزون إذا لم يكن موجوداً
     */
    private function createMovementsTableIfNotExists() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS inventory_movements (
                id INT AUTO_INCREMENT PRIMARY KEY,
                movement_number VARCHAR(50) UNIQUE NOT NULL,
                movement_type ENUM('in', 'out', 'adjustment', 'transfer') NOT NULL,
                movement_date DATE NOT NULL,
                product_id INT NOT NULL,
                warehouse_id INT DEFAULT 1,
                quantity DECIMAL(15,3) NOT NULL,
                unit_cost DECIMAL(15,2) DEFAULT 0,
                total_cost DECIMAL(15,2) DEFAULT 0,
                reference_type VARCHAR(50),
                reference_number VARCHAR(100),
                notes TEXT,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_product_date (product_id, movement_date),
                INDEX idx_movement_type (movement_type),
                INDEX idx_reference (reference_type, reference_number)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $this->db->exec($sql);

            // إنشاء جدول المستودعات إذا لم يكن موجوداً
            $sql = "CREATE TABLE IF NOT EXISTS warehouses (
                id INT AUTO_INCREMENT PRIMARY KEY,
                warehouse_code VARCHAR(20) UNIQUE NOT NULL,
                warehouse_name VARCHAR(100) NOT NULL,
                location VARCHAR(255),
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $this->db->exec($sql);

            // إدراج مستودع افتراضي
            $sql = "INSERT IGNORE INTO warehouses (warehouse_code, warehouse_name, location)
                    VALUES ('MAIN', 'المستودع الرئيسي', 'المقر الرئيسي')";

            $this->db->exec($sql);

        } catch (Exception $e) {
            // تجاهل الأخطاء
        }
    }

    /**
     * الحصول على إحصائيات المخزون
     */
    public function getInventoryStatistics() {
        try {
            $sql = "SELECT
                        COUNT(*) as total_products,
                        COUNT(CASE WHEN current_stock > 0 THEN 1 END) as products_in_stock,
                        COUNT(CASE WHEN current_stock <= min_stock_level THEN 1 END) as low_stock_products,
                        COUNT(CASE WHEN current_stock = 0 THEN 1 END) as out_of_stock_products,
                        COALESCE(SUM(current_stock * cost_price), 0) as total_inventory_value,
                        COALESCE(AVG(current_stock * cost_price), 0) as average_product_value
                    FROM products
                    WHERE is_active = 1";

            $stmt = $this->db->prepare($sql);
            $stmt->execute();

            $result = $stmt->fetch();

            // التأكد من أن جميع القيم رقمية
            return [
                'total_products' => (int)($result['total_products'] ?? 0),
                'products_in_stock' => (int)($result['products_in_stock'] ?? 0),
                'low_stock_products' => (int)($result['low_stock_products'] ?? 0),
                'out_of_stock_products' => (int)($result['out_of_stock_products'] ?? 0),
                'total_inventory_value' => (float)($result['total_inventory_value'] ?? 0),
                'average_product_value' => (float)($result['average_product_value'] ?? 0)
            ];
        } catch (Exception $e) {
            return [
                'total_products' => 0,
                'products_in_stock' => 0,
                'low_stock_products' => 0,
                'out_of_stock_products' => 0,
                'total_inventory_value' => 0.0,
                'average_product_value' => 0.0
            ];
        }
    }

    /**
     * الحصول على المنتجات منخفضة المخزون
     */
    public function getLowStockProducts() {
        try {
            $sql = "SELECT p.*,
                           pc.category_name,
                           u.unit_name,
                           (p.min_stock_level - p.current_stock) as shortage_quantity
                    FROM products p
                    LEFT JOIN product_categories pc ON p.category_id = pc.id
                    LEFT JOIN units_of_measure u ON p.base_unit_id = u.id
                    WHERE p.is_active = 1
                    AND p.current_stock <= p.min_stock_level
                    ORDER BY (p.current_stock / NULLIF(p.min_stock_level, 0)) ASC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على فئات المنتجات
     */
    public function getProductCategories() {
        try {
            $sql = "SELECT * FROM product_categories WHERE is_active = 1 ORDER BY category_name";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على وحدات القياس
     */
    public function getUnitsOfMeasure() {
        try {
            $sql = "SELECT * FROM units_of_measure WHERE is_active = 1 ORDER BY unit_name";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على المستودعات
     */
    public function getWarehouses() {
        try {
            $sql = "SELECT * FROM warehouses WHERE is_active = 1 ORDER BY warehouse_name";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }



    /**
     * إضافة مستودع جديد
     */
    public function addWarehouse($warehouse_data) {
        try {
            // التحقق من عدم تكرار رمز المستودع
            if ($this->isWarehouseCodeExists($warehouse_data['warehouse_code'])) {
                return [
                    'success' => false,
                    'message' => 'رمز المستودع موجود مسبقاً'
                ];
            }

            $sql = "INSERT INTO warehouses (warehouse_code, warehouse_name, location, is_active)
                    VALUES (:warehouse_code, :warehouse_name, :location, :is_active)";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':warehouse_code', $warehouse_data['warehouse_code']);
            $stmt->bindParam(':warehouse_name', $warehouse_data['warehouse_name']);
            $stmt->bindParam(':location', $warehouse_data['location']);
            $stmt->bindParam(':is_active', $warehouse_data['is_active']);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم إضافة المستودع بنجاح',
                    'warehouse_id' => $this->db->lastInsertId()
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في إضافة المستودع'
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في إضافة المستودع: ' . $e->getMessage()
            ];
        }
    }

    /**
     * التحقق من وجود رمز المستودع
     */
    private function isWarehouseCodeExists($warehouse_code, $exclude_id = null) {
        $sql = "SELECT COUNT(*) FROM warehouses WHERE warehouse_code = :warehouse_code";
        $params = [':warehouse_code' => $warehouse_code];

        if ($exclude_id) {
            $sql .= " AND id != :exclude_id";
            $params[':exclude_id'] = $exclude_id;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchColumn() > 0;
    }



    // دوال مساعدة

    private function isProductCodeExists($product_code, $exclude_id = null) {
        $sql = "SELECT COUNT(*) FROM products WHERE product_code = :product_code";
        $params = [':product_code' => $product_code];

        if ($exclude_id) {
            $sql .= " AND id != :exclude_id";
            $params[':exclude_id'] = $exclude_id;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchColumn() > 0;
    }

    private function isBarcodeExists($barcode, $exclude_id = null) {
        $sql = "SELECT COUNT(*) FROM products WHERE barcode = :barcode";
        $params = [':barcode' => $barcode];

        if ($exclude_id) {
            $sql .= " AND id != :exclude_id";
            $params[':exclude_id'] = $exclude_id;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchColumn() > 0;
    }

    private function isProductNameExists($product_name, $exclude_id = null) {
        $sql = "SELECT COUNT(*) FROM products WHERE LOWER(TRIM(product_name)) = LOWER(TRIM(:product_name))";
        $params = [':product_name' => $product_name];

        if ($exclude_id) {
            $sql .= " AND id != :exclude_id";
            $params[':exclude_id'] = $exclude_id;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchColumn() > 0;
    }

    // دوال عامة للتحقق من التكرار (للاستخدام مع AJAX)
    public function checkProductCodeExists($product_code, $exclude_id = null) {
        return $this->isProductCodeExists($product_code, $exclude_id);
    }

    public function checkProductNameExists($product_name, $exclude_id = null) {
        return $this->isProductNameExists($product_name, $exclude_id);
    }

    public function checkBarcodeExists($barcode, $exclude_id = null) {
        return $this->isBarcodeExists($barcode, $exclude_id);
    }

    private function generateMovementNumber($movement_type) {
        $prefix = strtoupper(substr($movement_type, 0, 3));
        $date = date('Ymd');

        // البحث عن آخر رقم
        $sql = "SELECT movement_number FROM inventory_movements
                WHERE movement_number LIKE :pattern
                ORDER BY id DESC LIMIT 1";

        $pattern = $prefix . $date . '%';
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':pattern', $pattern);
        $stmt->execute();

        $result = $stmt->fetch();

        if ($result) {
            $last_number = substr($result['movement_number'], -3);
            $next_number = str_pad(intval($last_number) + 1, 3, '0', STR_PAD_LEFT);
        } else {
            $next_number = '001';
        }

        return $prefix . $date . $next_number;
    }

    private function updateProductStock($product_id, $warehouse_id) {
        // حساب إجمالي الكمية من جميع الحركات
        $sql = "SELECT
                    SUM(CASE WHEN movement_type IN ('in', 'adjustment') THEN quantity ELSE 0 END) as total_in,
                    SUM(CASE WHEN movement_type IN ('out', 'transfer') THEN quantity ELSE 0 END) as total_out
                FROM inventory_movements
                WHERE product_id = :product_id";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->execute();

        $result = $stmt->fetch();
        $current_stock = ($result['total_in'] ?? 0) - ($result['total_out'] ?? 0);

        // تحديث رصيد المنتج
        $sql = "UPDATE products SET current_stock = :current_stock WHERE id = :product_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':current_stock', $current_stock);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->execute();
    }
}
?>
