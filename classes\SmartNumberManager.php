<?php
/**
 * SeaSystem - مدير الأرقام الذكي
 * Smart Number Manager - Complete Number Management System
 */

class SmartNumberManager {
    private $db;
    private $session_key = 'smart_numbers';

    public function __construct() {
        require_once __DIR__ . '/../config/database.php';
        $database = new Database();
        $this->db = $database->getConnection();

        // بدء الجلسة إذا لم تكن مبدوءة
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        // تهيئة مصفوفة الأرقام في الجلسة
        if (!isset($_SESSION[$this->session_key])) {
            $_SESSION[$this->session_key] = [
                'reserved' => [],
                'deleted_pool' => []
            ];
        }

        // تنظيف الحجوزات المنتهية الصلاحية
        $this->cleanupExpiredReservations();
    }

    /**
     * الحصول على الرقم التالي (مع إعادة تدوير الأرقام المحذوفة)
     * Get next number with recycling of deleted numbers
     */
    public function getNextNumber($entity_type, $prefix = '', $padding = 3, $user_id = null) {
        try {
            // 1. البحث عن رقم محجوز مسبقاً
            $existing_reservation = $this->getExistingReservation($entity_type);
            if ($existing_reservation) {
                return [
                    'success' => true,
                    'number' => $existing_reservation['full_code'],
                    'raw_number' => $existing_reservation['raw_number'],
                    'is_recycled' => $existing_reservation['is_recycled'],
                    'is_reserved' => true,
                    'message' => 'رقم محجوز مسبقاً'
                ];
            }

            // 2. البحث عن رقم محذوف لإعادة استخدامه
            $recycled_number = $this->getRecycledNumber($entity_type);
            if ($recycled_number) {
                $full_code = $prefix . str_pad($recycled_number, $padding, '0', STR_PAD_LEFT);

                // حجز الرقم المعاد تدويره
                $this->reserveNumber($entity_type, $recycled_number, $full_code, true, $user_id);

                return [
                    'success' => true,
                    'number' => $full_code,
                    'raw_number' => $recycled_number,
                    'is_recycled' => true,
                    'is_reserved' => true,
                    'message' => 'تم إعادة استخدام رقم محذوف'
                ];
            }

            // 3. توليد رقم جديد
            $next_number = $this->getNextSequentialNumber($entity_type);
            $full_code = $prefix . str_pad($next_number, $padding, '0', STR_PAD_LEFT);

            // حجز الرقم الجديد
            $this->reserveNumber($entity_type, $next_number, $full_code, false, $user_id);

            return [
                'success' => true,
                'number' => $full_code,
                'raw_number' => $next_number,
                'is_recycled' => false,
                'is_reserved' => true,
                'message' => 'رقم جديد محجوز'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'number' => $prefix . str_pad(1, $padding, '0', STR_PAD_LEFT)
            ];
        }
    }

    /**
     * تأكيد استخدام الرقم المحجوز
     * Confirm the reserved number usage
     */
    public function confirmNumber($entity_type, $record_id = null, $user_id = null) {
        try {
            $reservation = $this->getExistingReservation($entity_type);
            if (!$reservation) {
                throw new Exception('لا يوجد رقم محجوز للتأكيد');
            }

            $this->db->beginTransaction();

            // إذا كان الرقم معاد تدويره، احذفه من قائمة المحذوفات
            if ($reservation['is_recycled']) {
                $this->removeFromDeletedPool($entity_type, $reservation['raw_number']);
            } else {
                // إذا كان رقم جديد، حدث العداد
                $this->updateSequentialCounter($entity_type, $reservation['raw_number']);
            }

            // تسجيل استخدام الرقم
            $this->logNumberUsage($entity_type, $reservation['raw_number'], $reservation['full_code'], $record_id, $user_id, $reservation['is_recycled']);

            // إزالة الحجز من الجلسة
            $this->removeReservation($entity_type);

            $this->db->commit();

            return [
                'success' => true,
                'number' => $reservation['full_code'],
                'raw_number' => $reservation['raw_number'],
                'is_recycled' => $reservation['is_recycled'],
                'message' => 'تم تأكيد استخدام الرقم'
            ];

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * إلغاء حجز الرقم
     * Cancel number reservation
     */
    public function cancelReservation($entity_type) {
        $reservation = $this->getExistingReservation($entity_type);
        if ($reservation) {
            // إذا كان رقم معاد تدويره، أعده لقائمة المحذوفات
            if ($reservation['is_recycled']) {
                $this->addToDeletedPool($entity_type, $reservation['raw_number'], $reservation['full_code']);
            }

            $this->removeReservation($entity_type);

            return [
                'success' => true,
                'message' => 'تم إلغاء حجز الرقم'
            ];
        }

        return [
            'success' => false,
            'message' => 'لا يوجد رقم محجوز للإلغاء'
        ];
    }

    /**
     * حذف رقم وإضافته لقائمة إعادة التدوير
     * Delete a number and add it to recycling pool
     */
    public function deleteNumber($entity_type, $number_code, $record_id = null, $user_id = null, $reason = '') {
        try {
            // استخراج الرقم الخام من الكود
            $raw_number = $this->extractRawNumber($number_code);

            $this->db->beginTransaction();

            // إضافة الرقم لقائمة المحذوفات
            $this->addToDeletedPool($entity_type, $raw_number, $number_code, $reason);

            // تسجيل عملية الحذف
            $this->logNumberDeletion($entity_type, $raw_number, $number_code, $record_id, $user_id, $reason);

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'تم حذف الرقم وإضافته لإعادة التدوير'
            ];

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * الحصول على إحصائيات الأرقام
     * Get numbering statistics
     */
    public function getStatistics($entity_type = null) {
        try {
            $stats = [];

            // إحصائيات عامة
            $sql = "SELECT
                        entity_type,
                        COUNT(*) as total_used,
                        MAX(raw_number) as highest_number,
                        MIN(raw_number) as lowest_number
                    FROM number_usage_log";

            if ($entity_type) {
                $sql .= " WHERE entity_type = :entity_type";
            }

            $sql .= " GROUP BY entity_type ORDER BY entity_type";

            $stmt = $this->db->prepare($sql);
            if ($entity_type) {
                $stmt->bindParam(':entity_type', $entity_type);
            }
            $stmt->execute();

            $usage_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // إحصائيات المحذوفات
            $sql = "SELECT
                        entity_type,
                        COUNT(*) as deleted_count,
                        GROUP_CONCAT(raw_number ORDER BY raw_number) as deleted_numbers
                    FROM deleted_numbers_pool";

            if ($entity_type) {
                $sql .= " WHERE entity_type = :entity_type";
            }

            $sql .= " GROUP BY entity_type";

            $stmt = $this->db->prepare($sql);
            if ($entity_type) {
                $stmt->bindParam(':entity_type', $entity_type);
            }
            $stmt->execute();

            $deleted_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // دمج الإحصائيات
            foreach ($usage_stats as $usage) {
                $type = $usage['entity_type'];
                $stats[$type] = $usage;
                $stats[$type]['deleted_count'] = 0;
                $stats[$type]['deleted_numbers'] = '';

                foreach ($deleted_stats as $deleted) {
                    if ($deleted['entity_type'] == $type) {
                        $stats[$type]['deleted_count'] = $deleted['deleted_count'];
                        $stats[$type]['deleted_numbers'] = $deleted['deleted_numbers'];
                        break;
                    }
                }

                // حساب الفجوات
                $stats[$type]['gaps_count'] = $stats[$type]['deleted_count'];
                $stats[$type]['efficiency'] = $stats[$type]['total_used'] > 0 ?
                    round((($stats[$type]['total_used'] - $stats[$type]['deleted_count']) / $stats[$type]['total_used']) * 100, 2) : 100;
            }

            return [
                'success' => true,
                'statistics' => $stats,
                'current_reservations' => $this->getCurrentReservations()
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * إنشاء الجداول المطلوبة
     * Create required tables
     */
    public function createTables() {
        try {
            // جدول قائمة الأرقام المحذوفة
            $sql = "CREATE TABLE IF NOT EXISTS deleted_numbers_pool (
                id INT AUTO_INCREMENT PRIMARY KEY,
                entity_type VARCHAR(50) NOT NULL,
                raw_number INT NOT NULL,
                original_code VARCHAR(50) NOT NULL,
                deleted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                deleted_by INT NULL,
                reason TEXT,
                INDEX idx_entity_type (entity_type),
                INDEX idx_raw_number (raw_number),
                UNIQUE KEY unique_entity_number (entity_type, raw_number)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $this->db->exec($sql);

            // جدول سجل استخدام الأرقام
            $sql = "CREATE TABLE IF NOT EXISTS number_usage_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                entity_type VARCHAR(50) NOT NULL,
                raw_number INT NOT NULL,
                full_code VARCHAR(50) NOT NULL,
                record_id INT NULL,
                user_id INT NULL,
                is_recycled BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_entity_type (entity_type),
                INDEX idx_raw_number (raw_number),
                INDEX idx_record_id (record_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $this->db->exec($sql);

            // جدول سجل حذف الأرقام
            $sql = "CREATE TABLE IF NOT EXISTS number_deletion_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                entity_type VARCHAR(50) NOT NULL,
                raw_number INT NOT NULL,
                original_code VARCHAR(50) NOT NULL,
                record_id INT NULL,
                user_id INT NULL,
                reason TEXT,
                deleted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_entity_type (entity_type),
                INDEX idx_deleted_at (deleted_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $this->db->exec($sql);

            return ['success' => true, 'message' => 'تم إنشاء الجداول بنجاح'];

        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    // ==================== الدوال المساعدة الخاصة ====================

    private function getExistingReservation($entity_type) {
        return $_SESSION[$this->session_key]['reserved'][$entity_type] ?? null;
    }

    private function reserveNumber($entity_type, $raw_number, $full_code, $is_recycled, $user_id) {
        $_SESSION[$this->session_key]['reserved'][$entity_type] = [
            'raw_number' => $raw_number,
            'full_code' => $full_code,
            'is_recycled' => $is_recycled,
            'user_id' => $user_id,
            'timestamp' => time()
        ];
    }

    private function removeReservation($entity_type) {
        unset($_SESSION[$this->session_key]['reserved'][$entity_type]);
    }

    private function getRecycledNumber($entity_type) {
        $sql = "SELECT number_value FROM deleted_numbers_pool
                WHERE entity_type = :entity_type AND is_reused = 0
                ORDER BY number_value ASC
                LIMIT 1";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':entity_type', $entity_type);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['number_value'] : null;
    }

    private function getNextSequentialNumber($entity_type) {
        $sql = "SELECT current_max FROM smart_number_settings WHERE entity_type = :entity_type";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':entity_type', $entity_type);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? ($result['current_max'] + 1) : 1;
    }

    private function updateSequentialCounter($entity_type, $number) {
        $sql = "UPDATE smart_number_settings
                SET current_max = GREATEST(current_max, :number),
                    updated_at = NOW()
                WHERE entity_type = :entity_type";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':entity_type', $entity_type);
        $stmt->bindParam(':number', $number);
        $stmt->execute();
    }

    private function addToDeletedPool($entity_type, $raw_number, $original_code = '', $reason = '') {
        $sql = "INSERT IGNORE INTO deleted_numbers_pool
                (entity_type, number_value, deleted_by)
                VALUES (:entity_type, :number_value, :user_id)";

        $stmt = $this->db->prepare($sql);
        $user_id = $_SESSION['user_id'] ?? null;

        $stmt->bindParam(':entity_type', $entity_type);
        $stmt->bindParam(':number_value', $raw_number);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
    }

    private function removeFromDeletedPool($entity_type, $raw_number) {
        $sql = "UPDATE deleted_numbers_pool
                SET is_reused = 1, reused_at = NOW(), reused_by = :user_id
                WHERE entity_type = :entity_type AND number_value = :number_value";

        $stmt = $this->db->prepare($sql);
        $user_id = $_SESSION['user_id'] ?? null;
        $stmt->bindParam(':entity_type', $entity_type);
        $stmt->bindParam(':number_value', $raw_number);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
    }

    private function logNumberUsage($entity_type, $raw_number, $full_code, $record_id, $user_id, $is_recycled) {
        $sql = "INSERT INTO number_usage_log
                (entity_type, raw_number, full_code, record_id, user_id, is_recycled)
                VALUES (:entity_type, :raw_number, :full_code, :record_id, :user_id, :is_recycled)";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':entity_type', $entity_type);
        $stmt->bindParam(':raw_number', $raw_number);
        $stmt->bindParam(':full_code', $full_code);
        $stmt->bindParam(':record_id', $record_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':is_recycled', $is_recycled, PDO::PARAM_BOOL);
        $stmt->execute();
    }

    private function logNumberDeletion($entity_type, $raw_number, $original_code, $record_id, $user_id, $reason) {
        $sql = "INSERT INTO number_deletion_log
                (entity_type, raw_number, original_code, record_id, user_id, reason)
                VALUES (:entity_type, :raw_number, :original_code, :record_id, :user_id, :reason)";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':entity_type', $entity_type);
        $stmt->bindParam(':raw_number', $raw_number);
        $stmt->bindParam(':original_code', $original_code);
        $stmt->bindParam(':record_id', $record_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':reason', $reason);
        $stmt->execute();
    }

    private function extractRawNumber($code) {
        // استخراج الرقم من الكود (مثل CUS005 -> 5)
        return (int) preg_replace('/[^0-9]/', '', $code);
    }

    private function getCurrentReservations() {
        return $_SESSION[$this->session_key]['reserved'] ?? [];
    }

    private function cleanupExpiredReservations() {
        $current_time = time();
        $expired_time = 1800; // 30 دقيقة

        if (isset($_SESSION[$this->session_key]['reserved'])) {
            foreach ($_SESSION[$this->session_key]['reserved'] as $entity_type => $reservation) {
                if (($current_time - $reservation['timestamp']) > $expired_time) {
                    // إذا كان رقم معاد تدويره، أعده للقائمة
                    if ($reservation['is_recycled']) {
                        $this->addToDeletedPool($entity_type, $reservation['raw_number'], $reservation['full_code'], 'انتهاء صلاحية الحجز');
                    }
                    unset($_SESSION[$this->session_key]['reserved'][$entity_type]);
                }
            }
        }
    }
}
?>
