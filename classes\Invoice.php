<?php
/**
 * SeaSystem - فئة إدارة الفواتير
 * Invoice Management Class
 */

require_once __DIR__ . '/../config/database.php';

class Invoice {
    private $db;
    private $table_name = "invoices";
    private $items_table = "invoice_items";

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }

    /**
     * إنشاء فاتورة جديدة
     */
    public function create($invoice_data, $items_data) {
        try {
            $this->db->beginTransaction();

            // التحقق من صحة نمط رقم الفاتورة
            if (!$this->validateInvoiceNumber($invoice_data['invoice_number'], $invoice_data['invoice_type'])) {
                throw new Exception('رقم الفاتورة غير صحيح. يجب أن يبدأ بـ ' . ($invoice_data['invoice_type'] == 'sales' ? 'SALES-' : 'PURCHASE-'));
            }

            // التحقق من عدم تكرار رقم الفاتورة
            if ($this->isInvoiceNumberExists($invoice_data['invoice_number'])) {
                throw new Exception('رقم الفاتورة موجود مسبقاً');
            }

            // إدراج بيانات الفاتورة الأساسية
            $sql = "INSERT INTO " . $this->table_name . "
                    (invoice_number, invoice_type, customer_id, supplier_id, invoice_date, due_date,
                     subtotal, tax_amount, discount_amount, total_amount, notes, created_by)
                    VALUES (:invoice_number, :invoice_type, :customer_id, :supplier_id, :invoice_date, :due_date,
                            :subtotal, :tax_amount, :discount_amount, :total_amount, :notes, :created_by)";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':invoice_number', $invoice_data['invoice_number']);
            $stmt->bindParam(':invoice_type', $invoice_data['invoice_type']);
            $stmt->bindParam(':customer_id', $invoice_data['customer_id']);
            $stmt->bindParam(':supplier_id', $invoice_data['supplier_id']);
            $stmt->bindParam(':invoice_date', $invoice_data['invoice_date']);
            $stmt->bindParam(':due_date', $invoice_data['due_date']);
            $stmt->bindParam(':subtotal', $invoice_data['subtotal']);
            $stmt->bindParam(':tax_amount', $invoice_data['tax_amount']);
            $stmt->bindParam(':discount_amount', $invoice_data['discount_amount']);
            $stmt->bindParam(':total_amount', $invoice_data['total_amount']);
            $stmt->bindParam(':notes', $invoice_data['notes']);
            $stmt->bindParam(':created_by', $invoice_data['created_by']);

            if (!$stmt->execute()) {
                throw new Exception('فشل في إنشاء الفاتورة');
            }

            $invoice_id = $this->db->lastInsertId();

            // إدراج عناصر الفاتورة
            foreach ($items_data as $item) {
                $this->addInvoiceItem($invoice_id, $item);
            }

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'تم إنشاء الفاتورة بنجاح',
                'invoice_id' => $invoice_id
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'message' => 'خطأ في إنشاء الفاتورة: ' . $e->getMessage()
            ];
        }
    }

    /**
     * تحديث الفاتورة
     */
    public function update($invoice_id, $invoice_data, $items_data) {
        try {
            $this->db->beginTransaction();

            // التحقق من وجود الفاتورة
            if (!$this->exists($invoice_id)) {
                throw new Exception('الفاتورة غير موجودة');
            }

            // التحقق من عدم تكرار رقم الفاتورة
            if ($this->isInvoiceNumberExists($invoice_data['invoice_number'], $invoice_id)) {
                throw new Exception('رقم الفاتورة موجود مسبقاً');
            }

            // تحديث بيانات الفاتورة الأساسية
            $sql = "UPDATE " . $this->table_name . "
                    SET invoice_number = :invoice_number,
                        invoice_type = :invoice_type,
                        customer_id = :customer_id,
                        supplier_id = :supplier_id,
                        invoice_date = :invoice_date,
                        due_date = :due_date,
                        subtotal = :subtotal,
                        tax_amount = :tax_amount,
                        discount_amount = :discount_amount,
                        total_amount = :total_amount,
                        notes = :notes,
                        updated_at = NOW()
                    WHERE id = :invoice_id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':invoice_number', $invoice_data['invoice_number']);
            $stmt->bindParam(':invoice_type', $invoice_data['invoice_type']);
            $stmt->bindParam(':customer_id', $invoice_data['customer_id']);
            $stmt->bindParam(':supplier_id', $invoice_data['supplier_id']);
            $stmt->bindParam(':invoice_date', $invoice_data['invoice_date']);
            $stmt->bindParam(':due_date', $invoice_data['due_date']);
            $stmt->bindParam(':subtotal', $invoice_data['subtotal']);
            $stmt->bindParam(':tax_amount', $invoice_data['tax_amount']);
            $stmt->bindParam(':discount_amount', $invoice_data['discount_amount']);
            $stmt->bindParam(':total_amount', $invoice_data['total_amount']);
            $stmt->bindParam(':notes', $invoice_data['notes']);
            $stmt->bindParam(':invoice_id', $invoice_id);

            if (!$stmt->execute()) {
                throw new Exception('فشل في تحديث الفاتورة');
            }

            // حذف العناصر القديمة
            $this->deleteInvoiceItems($invoice_id);

            // إضافة العناصر الجديدة
            foreach ($items_data as $item) {
                $this->addInvoiceItem($invoice_id, $item);
            }

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'تم تحديث الفاتورة بنجاح'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'message' => 'خطأ في تحديث الفاتورة: ' . $e->getMessage()
            ];
        }
    }

    /**
     * حذف الفاتورة
     */
    public function delete($invoice_id) {
        try {
            $this->db->beginTransaction();

            // التحقق من وجود الفاتورة
            if (!$this->exists($invoice_id)) {
                throw new Exception('الفاتورة غير موجودة');
            }

            // التحقق من عدم وجود مدفوعات
            if ($this->hasPayments($invoice_id)) {
                throw new Exception('لا يمكن حذف الفاتورة لوجود مدفوعات مرتبطة بها');
            }

            // حذف عناصر الفاتورة
            $this->deleteInvoiceItems($invoice_id);

            // حذف الفاتورة
            $sql = "DELETE FROM " . $this->table_name . " WHERE id = :invoice_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':invoice_id', $invoice_id);

            if (!$stmt->execute()) {
                throw new Exception('فشل في حذف الفاتورة');
            }

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'تم حذف الفاتورة بنجاح'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'message' => 'خطأ في حذف الفاتورة: ' . $e->getMessage()
            ];
        }
    }

    /**
     * تغيير حالة الفاتورة
     */
    public function updateStatus($invoice_id, $status) {
        try {
            $sql = "UPDATE " . $this->table_name . "
                    SET status = :status, updated_at = NOW()
                    WHERE id = :invoice_id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':invoice_id', $invoice_id);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم تحديث حالة الفاتورة بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في تحديث حالة الفاتورة'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * تسجيل دفعة للفاتورة
     */
    public function recordPayment($invoice_id, $amount) {
        try {
            $this->db->beginTransaction();

            // الحصول على بيانات الفاتورة
            $invoice = $this->getById($invoice_id);
            if (!$invoice) {
                throw new Exception('الفاتورة غير موجودة');
            }

            $new_paid_amount = $invoice['paid_amount'] + $amount;

            // تحديد الحالة الجديدة
            $new_status = 'sent';
            if ($new_paid_amount >= $invoice['total_amount']) {
                $new_status = 'paid';
                $new_paid_amount = $invoice['total_amount']; // تجنب الدفع الزائد
            }

            // تحديث المبلغ المدفوع والحالة
            $sql = "UPDATE " . $this->table_name . "
                    SET paid_amount = :paid_amount, status = :status, updated_at = NOW()
                    WHERE id = :invoice_id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':paid_amount', $new_paid_amount);
            $stmt->bindParam(':status', $new_status);
            $stmt->bindParam(':invoice_id', $invoice_id);

            if (!$stmt->execute()) {
                throw new Exception('فشل في تحديث الفاتورة');
            }

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'تم تسجيل الدفعة بنجاح',
                'new_status' => $new_status,
                'remaining_amount' => $invoice['total_amount'] - $new_paid_amount
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'message' => 'خطأ في تسجيل الدفعة: ' . $e->getMessage()
            ];
        }
    }

    /**
     * الحصول على فاتورة واحدة
     */
    public function getById($invoice_id) {
        try {
            $sql = "SELECT i.*,
                           c.name as customer_name, c.email as customer_email,
                           s.name as supplier_name, s.email as supplier_email,
                           u.full_name as created_by_name
                    FROM " . $this->table_name . " i
                    LEFT JOIN customers c ON i.customer_id = c.id
                    LEFT JOIN suppliers s ON i.supplier_id = s.id
                    LEFT JOIN users u ON i.created_by = u.id
                    WHERE i.id = :invoice_id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':invoice_id', $invoice_id);
            $stmt->execute();

            return $stmt->fetch();
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * الحصول على عناصر الفاتورة
     */
    public function getInvoiceItems($invoice_id) {
        try {
            $sql = "SELECT ii.*, a.account_name
                    FROM " . $this->items_table . " ii
                    LEFT JOIN chart_of_accounts a ON ii.account_id = a.id
                    WHERE ii.invoice_id = :invoice_id
                    ORDER BY ii.id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':invoice_id', $invoice_id);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على جميع الفواتير
     */
    public function getAll($filters = []) {
        try {
            $sql = "SELECT i.*,
                           c.name as customer_name,
                           s.name as supplier_name,
                           u.full_name as created_by_name
                    FROM " . $this->table_name . " i
                    LEFT JOIN customers c ON i.customer_id = c.id
                    LEFT JOIN suppliers s ON i.supplier_id = s.id
                    LEFT JOIN users u ON i.created_by = u.id
                    WHERE 1=1";

            $params = [];

            // تطبيق المرشحات
            if (!empty($filters['invoice_type'])) {
                $sql .= " AND i.invoice_type = :invoice_type";
                $params[':invoice_type'] = $filters['invoice_type'];
            }

            if (!empty($filters['status'])) {
                $sql .= " AND i.status = :status";
                $params[':status'] = $filters['status'];
            }

            if (!empty($filters['customer_id'])) {
                $sql .= " AND i.customer_id = :customer_id";
                $params[':customer_id'] = $filters['customer_id'];
            }

            if (!empty($filters['supplier_id'])) {
                $sql .= " AND i.supplier_id = :supplier_id";
                $params[':supplier_id'] = $filters['supplier_id'];
            }

            if (!empty($filters['date_from'])) {
                $sql .= " AND i.invoice_date >= :date_from";
                $params[':date_from'] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $sql .= " AND i.invoice_date <= :date_to";
                $params[':date_to'] = $filters['date_to'];
            }

            $sql .= " ORDER BY i.invoice_date DESC, i.id DESC";

            // تطبيق الحد الأقصى للنتائج
            if (!empty($filters['limit'])) {
                $sql .= " LIMIT " . intval($filters['limit']);
            }

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * البحث في الفواتير
     */
    public function search($keyword, $filters = []) {
        try {
            $sql = "SELECT i.*,
                           c.name as customer_name,
                           s.name as supplier_name,
                           u.full_name as created_by_name
                    FROM " . $this->table_name . " i
                    LEFT JOIN customers c ON i.customer_id = c.id
                    LEFT JOIN suppliers s ON i.supplier_id = s.id
                    LEFT JOIN users u ON i.created_by = u.id
                    WHERE (i.invoice_number LIKE :keyword
                           OR c.name LIKE :keyword
                           OR s.name LIKE :keyword
                           OR i.notes LIKE :keyword)";

            $params = [':keyword' => "%$keyword%"];

            // تطبيق المرشحات الإضافية
            if (!empty($filters['invoice_type'])) {
                $sql .= " AND i.invoice_type = :invoice_type";
                $params[':invoice_type'] = $filters['invoice_type'];
            }

            if (!empty($filters['status'])) {
                $sql .= " AND i.status = :status";
                $params[':status'] = $filters['status'];
            }

            $sql .= " ORDER BY i.invoice_date DESC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * إحصائيات الفواتير
     */
    public function getStatistics($filters = []) {
        try {
            $sql = "SELECT
                        COUNT(*) as total_invoices,
                        SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as total_paid,
                        SUM(CASE WHEN status != 'paid' AND status != 'cancelled' THEN total_amount ELSE 0 END) as total_outstanding,
                        SUM(CASE WHEN status != 'cancelled' THEN total_amount ELSE 0 END) as total_amount,
                        AVG(CASE WHEN status != 'cancelled' THEN total_amount ELSE NULL END) as average_amount
                    FROM " . $this->table_name . "
                    WHERE 1=1";

            $params = [];

            // تطبيق المرشحات
            if (!empty($filters['invoice_type'])) {
                $sql .= " AND invoice_type = :invoice_type";
                $params[':invoice_type'] = $filters['invoice_type'];
            }

            if (!empty($filters['date_from'])) {
                $sql .= " AND invoice_date >= :date_from";
                $params[':date_from'] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $sql .= " AND invoice_date <= :date_to";
                $params[':date_to'] = $filters['date_to'];
            }

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetch();
        } catch (Exception $e) {
            return [
                'total_invoices' => 0,
                'total_paid' => 0,
                'total_outstanding' => 0,
                'total_amount' => 0,
                'average_amount' => 0
            ];
        }
    }

    /**
     * إضافة عنصر للفاتورة
     */
    private function addInvoiceItem($invoice_id, $item_data) {
        $sql = "INSERT INTO " . $this->items_table . "
                (invoice_id, item_description, quantity, unit_price, total_price, account_id)
                VALUES (:invoice_id, :item_description, :quantity, :unit_price, :total_price, :account_id)";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':invoice_id', $invoice_id);
        $stmt->bindParam(':item_description', $item_data['item_description']);
        $stmt->bindParam(':quantity', $item_data['quantity']);
        $stmt->bindParam(':unit_price', $item_data['unit_price']);
        $stmt->bindParam(':total_price', $item_data['total_price']);
        $stmt->bindParam(':account_id', $item_data['account_id']);

        return $stmt->execute();
    }

    /**
     * حذف عناصر الفاتورة
     */
    private function deleteInvoiceItems($invoice_id) {
        $sql = "DELETE FROM " . $this->items_table . " WHERE invoice_id = :invoice_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':invoice_id', $invoice_id);

        return $stmt->execute();
    }

    /**
     * التحقق من وجود الفاتورة
     */
    private function exists($invoice_id) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE id = :invoice_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':invoice_id', $invoice_id);
            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من تكرار رقم الفاتورة
     */
    private function isInvoiceNumberExists($invoice_number, $exclude_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE invoice_number = :invoice_number";

            if ($exclude_id) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':invoice_number', $invoice_number);

            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }

            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من وجود مدفوعات للفاتورة
     */
    private function hasPayments($invoice_id) {
        try {
            $sql = "SELECT COUNT(*) FROM payments WHERE invoice_id = :invoice_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':invoice_id', $invoice_id);
            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * توليد رقم فاتورة تلقائي محسن
     */
    public function generateInvoiceNumber($invoice_type = 'sales') {
        try {
            // تحديد البادئة حسب نوع الفاتورة
            $prefix = ($invoice_type == 'sales') ? 'SALES-' : 'PURCHASE-';
            $year = date('Y');
            $month = date('m');

            // نمط البحث الجديد
            $search_pattern = $prefix . $year . $month . '%';

            // البحث عن آخر رقم فاتورة في الشهر الحالي
            $sql = "SELECT invoice_number
                    FROM " . $this->table_name . "
                    WHERE invoice_type = :invoice_type
                    AND invoice_number LIKE :pattern
                    ORDER BY id DESC
                    LIMIT 1";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':invoice_type', $invoice_type);
            $stmt->bindParam(':pattern', $search_pattern);
            $stmt->execute();

            $result = $stmt->fetch();

            if ($result) {
                // استخراج الرقم التسلسلي من آخر فاتورة
                $last_invoice = $result['invoice_number'];
                $last_number = intval(substr($last_invoice, -4));
                $next_number = $last_number + 1;
            } else {
                // أول فاتورة في الشهر
                $next_number = 1;
            }

            // توليد الرقم الجديد
            $new_invoice_number = $prefix . $year . $month . '-' . str_pad($next_number, 4, '0', STR_PAD_LEFT);

            // التأكد من عدم وجود الرقم (أمان إضافي)
            while ($this->isInvoiceNumberExists($new_invoice_number)) {
                $next_number++;
                $new_invoice_number = $prefix . $year . $month . '-' . str_pad($next_number, 4, '0', STR_PAD_LEFT);
            }

            return $new_invoice_number;

        } catch (Exception $e) {
            // في حالة الخطأ، إرجاع رقم افتراضي
            $prefix = ($invoice_type == 'sales') ? 'SALES-' : 'PURCHASE-';
            return $prefix . date('Ymd') . '-0001';
        }
    }

    /**
     * التحقق من صحة نظام الترقيم
     */
    public function validateInvoiceNumber($invoice_number, $invoice_type) {
        $expected_prefix = ($invoice_type == 'sales') ? 'SALES-' : 'PURCHASE-';

        // التحقق من وجود البادئة الصحيحة
        if (strpos($invoice_number, $expected_prefix) !== 0) {
            return false;
        }

        // التحقق من النمط العام: PREFIX-YYYYMM-NNNN
        $pattern = '/^' . preg_quote($expected_prefix) . '\d{6}-\d{4}$/';
        return preg_match($pattern, $invoice_number);
    }

    /**
     * الحصول على إحصائيات الترقيم
     */
    public function getNumberingStats() {
        try {
            $sql = "SELECT
                        invoice_type,
                        COUNT(*) as total_count,
                        MAX(invoice_number) as last_number,
                        MIN(invoice_number) as first_number
                    FROM " . $this->table_name . "
                    GROUP BY invoice_type";

            $stmt = $this->db->prepare($sql);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على تقرير المبيعات
     */
    public function getSalesReport($filters = []) {
        try {
            $sql = "SELECT i.*, c.name as customer_name
                    FROM " . $this->table_name . " i
                    LEFT JOIN customers c ON i.customer_id = c.id
                    WHERE i.invoice_type = 'sales'";

            $params = [];

            // فلترة بالتاريخ
            if (!empty($filters['date_from'])) {
                $sql .= " AND i.invoice_date >= :date_from";
                $params[':date_from'] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $sql .= " AND i.invoice_date <= :date_to";
                $params[':date_to'] = $filters['date_to'];
            }

            // فلترة بالعميل
            if (!empty($filters['customer_id'])) {
                $sql .= " AND i.customer_id = :customer_id";
                $params[':customer_id'] = $filters['customer_id'];
            }

            // فلترة بالحالة
            if (!empty($filters['status'])) {
                $sql .= " AND i.status = :status";
                $params[':status'] = $filters['status'];
            }

            $sql .= " ORDER BY i.invoice_date DESC, i.id DESC";

            $stmt = $this->db->prepare($sql);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على تقرير المشتريات
     */
    public function getPurchasesReport($filters = []) {
        try {
            $sql = "SELECT i.*, s.name as supplier_name
                    FROM " . $this->table_name . " i
                    LEFT JOIN suppliers s ON i.supplier_id = s.id
                    WHERE i.invoice_type = 'purchase'";

            $params = [];

            // فلترة بالتاريخ
            if (!empty($filters['date_from'])) {
                $sql .= " AND i.invoice_date >= :date_from";
                $params[':date_from'] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $sql .= " AND i.invoice_date <= :date_to";
                $params[':date_to'] = $filters['date_to'];
            }

            // فلترة بالمورد
            if (!empty($filters['supplier_id'])) {
                $sql .= " AND i.supplier_id = :supplier_id";
                $params[':supplier_id'] = $filters['supplier_id'];
            }

            // فلترة بالحالة
            if (!empty($filters['status'])) {
                $sql .= " AND i.status = :status";
                $params[':status'] = $filters['status'];
            }

            $sql .= " ORDER BY i.invoice_date DESC, i.id DESC";

            $stmt = $this->db->prepare($sql);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }
}
?>
