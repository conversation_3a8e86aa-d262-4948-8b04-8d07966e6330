<?php
/**
 * SeaSystem - فئة إدارة المستخدمين
 * User Management Class
 */

require_once __DIR__ . '/../config/database.php';

class User {
    private $db;
    private $table = 'users';

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }

    /**
     * إنشاء مستخدم جديد
     */
    public function create($data) {
        try {
            // التحقق من عدم تكرار اسم المستخدم أو البريد الإلكتروني
            if ($this->usernameExists($data['username'])) {
                return [
                    'success' => false,
                    'message' => 'اسم المستخدم موجود بالفعل'
                ];
            }

            if ($this->emailExists($data['email'])) {
                return [
                    'success' => false,
                    'message' => 'البريد الإلكتروني موجود بالفعل'
                ];
            }

            // تشفير كلمة المرور
            $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);

            $sql = "INSERT INTO {$this->table} (username, email, password, full_name, role) 
                    VALUES (:username, :email, :password, :full_name, :role)";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':username', $data['username']);
            $stmt->bindParam(':email', $data['email']);
            $stmt->bindParam(':password', $hashedPassword);
            $stmt->bindParam(':full_name', $data['full_name']);
            $stmt->bindParam(':role', $data['role']);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم إنشاء المستخدم بنجاح',
                    'user_id' => $this->db->lastInsertId()
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في إنشاء المستخدم'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * تحديث بيانات المستخدم
     */
    public function update($id, $data) {
        try {
            // التحقق من عدم تكرار اسم المستخدم أو البريد الإلكتروني (باستثناء المستخدم الحالي)
            if ($this->usernameExists($data['username'], $id)) {
                return [
                    'success' => false,
                    'message' => 'اسم المستخدم موجود بالفعل'
                ];
            }

            if ($this->emailExists($data['email'], $id)) {
                return [
                    'success' => false,
                    'message' => 'البريد الإلكتروني موجود بالفعل'
                ];
            }

            // بناء الاستعلام
            $fields = [];
            $params = [':id' => $id];

            if (isset($data['username'])) {
                $fields[] = 'username = :username';
                $params[':username'] = $data['username'];
            }

            if (isset($data['email'])) {
                $fields[] = 'email = :email';
                $params[':email'] = $data['email'];
            }

            if (isset($data['full_name'])) {
                $fields[] = 'full_name = :full_name';
                $params[':full_name'] = $data['full_name'];
            }

            if (isset($data['role'])) {
                $fields[] = 'role = :role';
                $params[':role'] = $data['role'];
            }

            if (isset($data['password']) && !empty($data['password'])) {
                $fields[] = 'password = :password';
                $params[':password'] = password_hash($data['password'], PASSWORD_DEFAULT);
            }

            if (empty($fields)) {
                return [
                    'success' => false,
                    'message' => 'لا توجد بيانات للتحديث'
                ];
            }

            $sql = "UPDATE {$this->table} SET " . implode(', ', $fields) . " WHERE id = :id";
            $stmt = $this->db->prepare($sql);

            if ($stmt->execute($params)) {
                return [
                    'success' => true,
                    'message' => 'تم تحديث بيانات المستخدم بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في تحديث بيانات المستخدم'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * حذف مستخدم
     */
    public function delete($id) {
        try {
            // التحقق من وجود المستخدم
            if (!$this->exists($id)) {
                return [
                    'success' => false,
                    'message' => 'المستخدم غير موجود'
                ];
            }

            $sql = "DELETE FROM {$this->table} WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم حذف المستخدم بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في حذف المستخدم'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * تغيير حالة المستخدم (تفعيل/تعطيل)
     */
    public function toggleStatus($id) {
        try {
            // الحصول على الحالة الحالية
            $sql = "SELECT is_active FROM {$this->table} WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                return [
                    'success' => false,
                    'message' => 'المستخدم غير موجود'
                ];
            }

            $user = $stmt->fetch();
            $newStatus = !$user['is_active'];

            // تحديث الحالة
            $sql = "UPDATE {$this->table} SET is_active = :status WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':status', $newStatus, PDO::PARAM_BOOL);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                $statusText = $newStatus ? 'تم تفعيل' : 'تم تعطيل';
                return [
                    'success' => true,
                    'message' => $statusText . ' المستخدم بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في تغيير حالة المستخدم'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * جلب جميع المستخدمين
     */
    public function getAll() {
        try {
            $sql = "SELECT id, username, email, full_name, role, is_active, created_at 
                    FROM {$this->table} 
                    ORDER BY created_at DESC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * جلب مستخدم بالمعرف
     */
    public function getById($id) {
        try {
            $sql = "SELECT id, username, email, full_name, role, is_active, created_at 
                    FROM {$this->table} 
                    WHERE id = :id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();

            return $stmt->fetch();
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * البحث في المستخدمين
     */
    public function search($searchTerm) {
        try {
            $sql = "SELECT id, username, email, full_name, role, is_active, created_at 
                    FROM {$this->table} 
                    WHERE username LIKE :search 
                       OR email LIKE :search 
                       OR full_name LIKE :search
                    ORDER BY created_at DESC";

            $stmt = $this->db->prepare($sql);
            $searchParam = '%' . $searchTerm . '%';
            $stmt->bindParam(':search', $searchParam);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * التحقق من وجود اسم المستخدم
     */
    private function usernameExists($username, $excludeId = null) {
        try {
            $sql = "SELECT id FROM {$this->table} WHERE username = :username";
            if ($excludeId) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':username', $username);
            if ($excludeId) {
                $stmt->bindParam(':exclude_id', $excludeId);
            }
            $stmt->execute();

            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من وجود البريد الإلكتروني
     */
    private function emailExists($email, $excludeId = null) {
        try {
            $sql = "SELECT id FROM {$this->table} WHERE email = :email";
            if ($excludeId) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':email', $email);
            if ($excludeId) {
                $stmt->bindParam(':exclude_id', $excludeId);
            }
            $stmt->execute();

            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من وجود المستخدم
     */
    private function exists($id) {
        try {
            $sql = "SELECT id FROM {$this->table} WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();

            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * إحصائيات المستخدمين
     */
    public function getStatistics() {
        try {
            $stats = [];

            // إجمالي المستخدمين
            $sql = "SELECT COUNT(*) as total FROM {$this->table}";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $stats['total'] = $stmt->fetch()['total'];

            // المستخدمين النشطين
            $sql = "SELECT COUNT(*) as active FROM {$this->table} WHERE is_active = 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $stats['active'] = $stmt->fetch()['active'];

            // المستخدمين حسب الدور
            $sql = "SELECT role, COUNT(*) as count FROM {$this->table} GROUP BY role";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $roleStats = $stmt->fetchAll();

            foreach ($roleStats as $role) {
                $stats['by_role'][$role['role']] = $role['count'];
            }

            return $stats;
        } catch (Exception $e) {
            return [];
        }
    }
}
?>
