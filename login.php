<?php
/**
 * SeaSystem - صفحة تسجيل الدخول
 * Login Page
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/includes/csrf.php';

// إعادة توجيه المستخدمين المسجلين إلى لوحة التحكم
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit();
}

$error_message = '';
$success_message = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['login'])) {
    // التحقق من CSRF Token
    if (!CSRF::validateToken($_POST[CSRF_TOKEN_NAME] ?? '')) {
        $error_message = 'خطأ في التحقق من الأمان';
    } else {
        $username = trim($_POST['username']);
        $password = $_POST['password'];

        if (empty($username)) {
            $error_message = 'يرجى إدخال اسم المستخدم';
        } else {
            $result = $auth->login($username, $password);

            if ($result['success']) {
                header('Location: dashboard.php');
                exit();
            } else {
                $error_message = $result['message'];
            }
        }
    }
}

// رسائل من URL
if (isset($_GET['message'])) {
    switch ($_GET['message']) {
        case 'logout':
            $success_message = 'تم تسجيل الخروج بنجاح';
            break;
        case 'session_expired':
            $error_message = 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى';
            break;
        case 'access_denied':
            $error_message = 'يجب تسجيل الدخول للوصول إلى هذه الصفحة';
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .login-body {
            padding: 2rem;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-left: none;
            border-radius: 0 10px 10px 0;
        }

        .form-control.with-icon {
            border-left: none;
            border-radius: 10px 0 0 10px;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .system-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem 2.5rem;
            margin-top: 1rem;
            font-size: 0.9rem;
            color: #6c757d;
            line-height: 2;
            border: 1px solid #e9ecef;
        }

        .demo-credentials {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 2rem 2.5rem;
            margin-top: 1rem;
            border-left: 4px solid #2196f3;
            line-height: 2;
            border: 1px solid #bbdefb;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1 class="mb-2">
                <i class="fas fa-calculator fa-2x mb-3"></i><br>
                <?php echo SITE_NAME; ?>
            </h1>
            <p class="mb-0 opacity-75"><?php echo SITE_DESCRIPTION; ?></p>
        </div>

        <div class="login-body">
            <!-- الرسائل -->
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <!-- تنبيه وضع التطوير -->
            <div class="alert alert-warning" role="alert">
                <i class="fas fa-code me-2"></i>
                <strong>وضع التطوير:</strong> يمكنك الدخول باسم المستخدم فقط (بدون كلمة مرور)
            </div>

            <!-- نموذج تسجيل الدخول -->
            <form method="POST" action="">
                <div class="mb-3">
                    <label for="username" class="form-label">اسم المستخدم أو البريد الإلكتروني</label>
                    <div class="input-group">
                        <input type="text" class="form-control with-icon" id="username" name="username"
                               required placeholder="أدخل اسم المستخدم"
                               value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="password" class="form-label">كلمة المرور <small class="text-muted">(اختياري في وضع التطوير)</small></label>
                    <div class="input-group">
                        <input type="password" class="form-control with-icon" id="password" name="password"
                               placeholder="اتركه فارغاً في وضع التطوير">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                    </div>
                </div>

                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                    <label class="form-check-label" for="remember_me">
                        تذكرني
                    </label>
                </div>

                <?php echo CSRF::getHiddenField(); ?>

                <button type="submit" name="login" class="btn btn-primary btn-login w-100">
                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                </button>
            </form>

            <!-- بيانات تجريبية -->
            <div class="demo-credentials">
                <h6 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>بيانات تجريبية
                </h6>
                <div class="row">
                    <div class="col-6 mb-2">
                        <strong>المستخدم:</strong><br>
                        <span class="text-primary">admin</span>
                    </div>
                    <div class="col-6 mb-2">
                        <strong>كلمة المرور:</strong><br>
                        <span class="text-primary">admin123</span>
                    </div>
                </div>
            </div>

            <!-- معلومات النظام -->
            <div class="system-info text-center">
                <div class="mb-3">
                    <i class="fas fa-shield-alt me-2"></i>
                    نظام آمن ومحمي
                </div>
                <div>
                    الإصدار <?php echo SITE_VERSION; ?> |
                    <i class="fas fa-clock me-2"></i>
                    <?php echo date('Y'); ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تركيز على حقل اسم المستخدم عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });

        // إظهار/إخفاء كلمة المرور
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>
