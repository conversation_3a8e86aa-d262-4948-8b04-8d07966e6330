<?php
/**
 * SeaSystem - سجلات المراجعة
 * Audit Log Class
 */

class AuditLog {
    private $db;
    
    public function __construct() {
        require_once __DIR__ . '/../config/database.php';
        $database = new Database();
        $this->db = $database->getConnection();
        
        // إنشاء جدول سجلات المراجعة إذا لم يكن موجوداً
        $this->createAuditTable();
    }
    
    /**
     * إنشاء جدول سجلات المراجعة
     */
    private function createAuditTable() {
        $sql = "CREATE TABLE IF NOT EXISTS audit_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            action VARCHAR(100) NOT NULL,
            table_name VARCHAR(100),
            record_id INT,
            old_values JSON,
            new_values JSON,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_action (action),
            INDEX idx_table_name (table_name),
            INDEX idx_record_id (record_id),
            INDEX idx_created_at (created_at)
        )";
        
        try {
            $this->db->exec($sql);
        } catch (Exception $e) {
            // تجاهل الأخطاء إذا كان الجدول موجوداً
        }
    }
    
    /**
     * تسجيل عملية في سجل المراجعة
     */
    public static function log($action, $tableName = null, $recordId = null, $oldValues = null, $newValues = null) {
        try {
            $auditLog = new self();
            
            $userId = $_SESSION['user_id'] ?? null;
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            // تحويل القيم إلى JSON
            $oldValuesJson = $oldValues ? json_encode($oldValues, JSON_UNESCAPED_UNICODE) : null;
            $newValuesJson = $newValues ? json_encode($newValues, JSON_UNESCAPED_UNICODE) : null;
            
            $sql = "INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $auditLog->db->prepare($sql);
            $stmt->execute([
                $userId,
                $action,
                $tableName,
                $recordId,
                $oldValuesJson,
                $newValuesJson,
                $ipAddress,
                $userAgent
            ]);
            
            return true;
            
        } catch (Exception $e) {
            // تجاهل أخطاء التسجيل لعدم تعطيل النظام
            error_log("Audit Log Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على سجلات المراجعة
     */
    public function getLogs($limit = 100, $offset = 0, $filters = []) {
        try {
            $sql = "SELECT a.*, u.username, u.full_name 
                    FROM audit_logs a 
                    LEFT JOIN users u ON a.user_id = u.id 
                    WHERE 1=1";
            
            $params = [];
            
            // تطبيق الفلاتر
            if (!empty($filters['user_id'])) {
                $sql .= " AND a.user_id = ?";
                $params[] = $filters['user_id'];
            }
            
            if (!empty($filters['action'])) {
                $sql .= " AND a.action LIKE ?";
                $params[] = '%' . $filters['action'] . '%';
            }
            
            if (!empty($filters['table_name'])) {
                $sql .= " AND a.table_name = ?";
                $params[] = $filters['table_name'];
            }
            
            if (!empty($filters['date_from'])) {
                $sql .= " AND DATE(a.created_at) >= ?";
                $params[] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $sql .= " AND DATE(a.created_at) <= ?";
                $params[] = $filters['date_to'];
            }
            
            $sql .= " ORDER BY a.created_at DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * الحصول على عدد سجلات المراجعة
     */
    public function getLogsCount($filters = []) {
        try {
            $sql = "SELECT COUNT(*) FROM audit_logs WHERE 1=1";
            $params = [];
            
            // تطبيق نفس الفلاتر
            if (!empty($filters['user_id'])) {
                $sql .= " AND user_id = ?";
                $params[] = $filters['user_id'];
            }
            
            if (!empty($filters['action'])) {
                $sql .= " AND action LIKE ?";
                $params[] = '%' . $filters['action'] . '%';
            }
            
            if (!empty($filters['table_name'])) {
                $sql .= " AND table_name = ?";
                $params[] = $filters['table_name'];
            }
            
            if (!empty($filters['date_from'])) {
                $sql .= " AND DATE(created_at) >= ?";
                $params[] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $sql .= " AND DATE(created_at) <= ?";
                $params[] = $filters['date_to'];
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchColumn();
            
        } catch (Exception $e) {
            return 0;
        }
    }
    
    /**
     * الحصول على إحصائيات سجلات المراجعة
     */
    public function getStatistics() {
        try {
            $stats = [];
            
            // إجمالي السجلات
            $stmt = $this->db->query("SELECT COUNT(*) FROM audit_logs");
            $stats['total_logs'] = $stmt->fetchColumn();
            
            // السجلات اليوم
            $stmt = $this->db->query("SELECT COUNT(*) FROM audit_logs WHERE DATE(created_at) = CURDATE()");
            $stats['today_logs'] = $stmt->fetchColumn();
            
            // السجلات هذا الأسبوع
            $stmt = $this->db->query("SELECT COUNT(*) FROM audit_logs WHERE WEEK(created_at) = WEEK(NOW())");
            $stats['week_logs'] = $stmt->fetchColumn();
            
            // أكثر المستخدمين نشاطاً
            $stmt = $this->db->query("
                SELECT u.username, u.full_name, COUNT(*) as log_count 
                FROM audit_logs a 
                LEFT JOIN users u ON a.user_id = u.id 
                WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY a.user_id 
                ORDER BY log_count DESC 
                LIMIT 5
            ");
            $stats['top_users'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // أكثر العمليات تكراراً
            $stmt = $this->db->query("
                SELECT action, COUNT(*) as action_count 
                FROM audit_logs 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY action 
                ORDER BY action_count DESC 
                LIMIT 5
            ");
            $stats['top_actions'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return $stats;
            
        } catch (Exception $e) {
            return [
                'total_logs' => 0,
                'today_logs' => 0,
                'week_logs' => 0,
                'top_users' => [],
                'top_actions' => []
            ];
        }
    }
    
    /**
     * تنظيف السجلات القديمة
     */
    public function cleanupOldLogs($daysToKeep = 90) {
        try {
            $sql = "DELETE FROM audit_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$daysToKeep]);
            
            return $stmt->rowCount();
            
        } catch (Exception $e) {
            return 0;
        }
    }
    
    /**
     * تصدير سجلات المراجعة
     */
    public function exportLogs($filters = [], $format = 'csv') {
        try {
            $logs = $this->getLogs(10000, 0, $filters); // تصدير حتى 10000 سجل
            
            $exportData = [];
            foreach ($logs as $log) {
                $exportData[] = [
                    'ID' => $log['id'],
                    'المستخدم' => $log['full_name'] ?: $log['username'] ?: 'غير معروف',
                    'العملية' => $log['action'],
                    'الجدول' => $log['table_name'] ?: 'غير محدد',
                    'معرف السجل' => $log['record_id'] ?: 'غير محدد',
                    'عنوان IP' => $log['ip_address'],
                    'التاريخ والوقت' => $log['created_at']
                ];
            }
            
            require_once __DIR__ . '/ExportManager.php';
            
            $filename = 'audit_logs_' . date('Y-m-d_H-i-s');
            $title = 'سجلات المراجعة';
            
            switch ($format) {
                case 'excel':
                    ExportManager::exportToExcel($exportData, $filename, [], $title);
                    break;
                case 'pdf':
                    ExportManager::exportToPDF($exportData, $filename, [], $title);
                    break;
                default:
                    ExportManager::exportToCSV($exportData, $filename);
                    break;
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في التصدير: ' . $e->getMessage()];
        }
    }
    
    /**
     * الحصول على تفاصيل سجل معين
     */
    public function getLogDetails($logId) {
        try {
            $sql = "SELECT a.*, u.username, u.full_name 
                    FROM audit_logs a 
                    LEFT JOIN users u ON a.user_id = u.id 
                    WHERE a.id = ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$logId]);
            
            $log = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($log) {
                // تحويل JSON إلى مصفوفات
                $log['old_values_array'] = $log['old_values'] ? json_decode($log['old_values'], true) : null;
                $log['new_values_array'] = $log['new_values'] ? json_decode($log['new_values'], true) : null;
            }
            
            return $log;
            
        } catch (Exception $e) {
            return null;
        }
    }
}
?>
