<?php
/**
 * SeaSystem - صفحة حركة المخزون
 * Inventory Movement Page
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/Inventory.php';

// التأكد من تسجيل الدخول
requireLogin();

$inventory = new Inventory();
$current_user = getCurrentUser();

// معالجة إضافة الحركة
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_movement'])) {
    $movement_data = [
        'movement_type' => $_POST['movement_type'],
        'product_id' => $_POST['product_id'],
        'warehouse_id' => $_POST['warehouse_id'],
        'quantity' => $_POST['quantity'],
        'unit_cost' => $_POST['unit_cost'],
        'reference_type' => $_POST['reference_type'],
        'reference_number' => $_POST['reference_number'],
        'notes' => $_POST['notes'],
        'created_by' => $current_user['id']
    ];
    
    $result = $inventory->addInventoryMovement($movement_data);
    $message = $result['message'];
    $message_type = $result['success'] ? 'success' : 'danger';
    
    if ($result['success']) {
        // إعادة توجيه إلى صفحة المخزون
        header('Location: inventory.php?success=' . urlencode($message));
        exit();
    }
}

// الحصول على البيانات المساعدة
$products = $inventory->getAllProducts();
$warehouses = $inventory->getWarehouses();

// أنواع الحركات
$movement_types = [
    'in' => 'وارد',
    'out' => 'صادر',
    'adjustment' => 'تسوية'
];

$reference_types = [
    'purchase' => 'مشتريات',
    'sale' => 'مبيعات',
    'adjustment' => 'تسوية',
    'opening' => 'رصيد ابتدائي'
];

// توليد رقم حركة تلقائي
$movement_number = 'MOV' . date('Ymd') . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حركة المخزون - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <style>
        .movement-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .movement-form {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 2rem;
        }
        
        .movement-type-selector {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .movement-type-card {
            flex: 1;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .movement-type-card:hover {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .movement-type-card.active {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .movement-type-card input[type="radio"] {
            display: none;
        }
        
        .movement-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .in-movement {
            color: #28a745;
        }
        
        .out-movement {
            color: #dc3545;
        }
        
        .adjustment-movement {
            color: #ffc107;
        }
        
        .product-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .current-stock {
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .stock-warning {
            color: #ffc107;
        }
        
        .stock-danger {
            color: #dc3545;
        }
        
        .calculation-summary {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 1rem;
        }
    </style>

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }
        
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->
    
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-lg-8 p-4">
                <!-- رأس الصفحة -->
                <div class="movement-header">
                    <h1 class="mb-2">
                        <i class="fas fa-exchange-alt me-3"></i>حركة المخزون
                    </h1>
                    <p class="mb-0 opacity-75">إضافة حركة وارد أو صادر أو تسوية للمخزون</p>
                </div>
                
                <!-- أزرار التنقل -->
                <div class="row mb-3">
                    <div class="col">
                        <a href="inventory.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للمخزون
                        </a>
                    </div>
                </div>
                
                <!-- الرسائل -->
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- نموذج حركة المخزون -->
                <div class="movement-form">
                    <form method="POST" id="movementForm">
                        <!-- اختيار نوع الحركة -->
                        <div class="movement-type-selector">
                            <div class="movement-type-card" onclick="selectMovementType('in')">
                                <input type="radio" name="movement_type" value="in" id="type_in">
                                <div class="movement-icon in-movement">
                                    <i class="fas fa-arrow-down"></i>
                                </div>
                                <h5>وارد</h5>
                                <p class="text-muted mb-0">إضافة مخزون</p>
                            </div>
                            
                            <div class="movement-type-card" onclick="selectMovementType('out')">
                                <input type="radio" name="movement_type" value="out" id="type_out">
                                <div class="movement-icon out-movement">
                                    <i class="fas fa-arrow-up"></i>
                                </div>
                                <h5>صادر</h5>
                                <p class="text-muted mb-0">خصم مخزون</p>
                            </div>
                            
                            <div class="movement-type-card" onclick="selectMovementType('adjustment')">
                                <input type="radio" name="movement_type" value="adjustment" id="type_adjustment">
                                <div class="movement-icon adjustment-movement">
                                    <i class="fas fa-adjust"></i>
                                </div>
                                <h5>تسوية</h5>
                                <p class="text-muted mb-0">تصحيح مخزون</p>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- معلومات الحركة -->
                            <div class="col-md-6">
                                <h5 class="mb-3">
                                    <i class="fas fa-info-circle me-2"></i>معلومات الحركة
                                </h5>
                                
                                <div class="mb-3">
                                    <label for="movement_number" class="form-label">رقم الحركة</label>
                                    <input type="text" class="form-control" id="movement_number" 
                                           value="<?php echo $movement_number; ?>" readonly>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="product_id" class="form-label">المنتج *</label>
                                    <select class="form-select" id="product_id" name="product_id" required onchange="loadProductInfo()">
                                        <option value="">اختر المنتج</option>
                                        <?php foreach ($products as $product): ?>
                                            <option value="<?php echo $product['id']; ?>" 
                                                    data-stock="<?php echo $product['current_stock']; ?>"
                                                    data-cost="<?php echo $product['cost_price']; ?>"
                                                    data-unit="<?php echo $product['unit_name']; ?>"
                                                    data-min="<?php echo $product['min_stock_level']; ?>">
                                                <?php echo htmlspecialchars($product['product_code'] . ' - ' . $product['product_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="warehouse_id" class="form-label">المستودع *</label>
                                    <select class="form-select" id="warehouse_id" name="warehouse_id" required>
                                        <option value="">اختر المستودع</option>
                                        <?php foreach ($warehouses as $warehouse): ?>
                                            <option value="<?php echo $warehouse['id']; ?>" 
                                                    <?php echo $warehouse['is_default'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($warehouse['warehouse_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="reference_type" class="form-label">نوع المرجع *</label>
                                    <select class="form-select" id="reference_type" name="reference_type" required>
                                        <option value="">اختر نوع المرجع</option>
                                        <?php foreach ($reference_types as $key => $value): ?>
                                            <option value="<?php echo $key; ?>"><?php echo $value; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="reference_number" class="form-label">رقم المرجع</label>
                                    <input type="text" class="form-control" id="reference_number" 
                                           name="reference_number" placeholder="رقم الفاتورة أو المرجع">
                                </div>
                            </div>
                            
                            <!-- الكمية والتكلفة -->
                            <div class="col-md-6">
                                <h5 class="mb-3">
                                    <i class="fas fa-calculator me-2"></i>الكمية والتكلفة
                                </h5>
                                
                                <div class="mb-3">
                                    <label for="quantity" class="form-label">الكمية *</label>
                                    <input type="number" class="form-control" id="quantity" name="quantity" 
                                           step="0.01" min="0.01" required placeholder="0.00" 
                                           onchange="calculateTotal()">
                                    <div class="form-text">الكمية المراد إضافتها أو خصمها</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="unit_cost" class="form-label">سعر الوحدة *</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="unit_cost" name="unit_cost" 
                                               step="0.01" min="0" required placeholder="0.00" 
                                               onchange="calculateTotal()">
                                        <span class="input-group-text"><?php echo CURRENCY_SYMBOL; ?></span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" 
                                              rows="3" placeholder="ملاحظات إضافية"></textarea>
                                </div>
                                
                                <!-- ملخص الحساب -->
                                <div class="calculation-summary">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="text-muted small">إجمالي التكلفة</div>
                                            <div class="fw-bold fs-5" id="total_cost">0.00</div>
                                        </div>
                                        <div class="col-6">
                                            <div class="text-muted small">الرصيد الجديد</div>
                                            <div class="fw-bold fs-5" id="new_balance">0.00</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- معلومات المنتج -->
                        <div id="product_info" class="product-info" style="display: none;">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-muted small">المخزون الحالي</div>
                                    <div class="current-stock" id="current_stock_display">0</div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-muted small">الوحدة</div>
                                    <div id="unit_display">-</div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-muted small">سعر التكلفة</div>
                                    <div id="cost_price_display">0.00</div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-muted small">الحد الأدنى</div>
                                    <div id="min_stock_display">0</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- أزرار الحفظ -->
                        <div class="text-center mt-4">
                            <button type="submit" name="add_movement" class="btn btn-success btn-lg me-3">
                                <i class="fas fa-save me-2"></i>حفظ الحركة
                            </button>
                            <a href="inventory.php" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        let currentStock = 0;
        let selectedMovementType = '';
        
        // اختيار نوع الحركة
        function selectMovementType(type) {
            // إزالة التحديد من جميع البطاقات
            document.querySelectorAll('.movement-type-card').forEach(card => {
                card.classList.remove('active');
            });
            
            // تحديد البطاقة المختارة
            event.target.closest('.movement-type-card').classList.add('active');
            document.getElementById('type_' + type).checked = true;
            selectedMovementType = type;
            
            // تحديث حساب الرصيد الجديد
            calculateNewBalance();
        }
        
        // تحميل معلومات المنتج
        function loadProductInfo() {
            const productSelect = document.getElementById('product_id');
            const selectedOption = productSelect.options[productSelect.selectedIndex];
            
            if (selectedOption.value) {
                currentStock = parseFloat(selectedOption.dataset.stock) || 0;
                const costPrice = parseFloat(selectedOption.dataset.cost) || 0;
                const unit = selectedOption.dataset.unit || '';
                const minStock = parseFloat(selectedOption.dataset.min) || 0;
                
                // عرض معلومات المنتج
                document.getElementById('product_info').style.display = 'block';
                document.getElementById('current_stock_display').textContent = currentStock.toFixed(2);
                document.getElementById('unit_display').textContent = unit;
                document.getElementById('cost_price_display').textContent = costPrice.toFixed(2);
                document.getElementById('min_stock_display').textContent = minStock.toFixed(2);
                
                // تعيين سعر التكلفة الافتراضي
                document.getElementById('unit_cost').value = costPrice.toFixed(2);
                
                // تحديد لون المخزون الحالي
                const stockDisplay = document.getElementById('current_stock_display');
                if (currentStock <= 0) {
                    stockDisplay.className = 'current-stock stock-danger';
                } else if (currentStock <= minStock) {
                    stockDisplay.className = 'current-stock stock-warning';
                } else {
                    stockDisplay.className = 'current-stock';
                }
                
                calculateTotal();
            } else {
                document.getElementById('product_info').style.display = 'none';
                currentStock = 0;
            }
        }
        
        // حساب الإجمالي
        function calculateTotal() {
            const quantity = parseFloat(document.getElementById('quantity').value) || 0;
            const unitCost = parseFloat(document.getElementById('unit_cost').value) || 0;
            const totalCost = quantity * unitCost;
            
            document.getElementById('total_cost').textContent = totalCost.toFixed(2);
            
            calculateNewBalance();
        }
        
        // حساب الرصيد الجديد
        function calculateNewBalance() {
            const quantity = parseFloat(document.getElementById('quantity').value) || 0;
            let newBalance = currentStock;
            
            if (selectedMovementType === 'in' || selectedMovementType === 'adjustment') {
                newBalance = currentStock + quantity;
            } else if (selectedMovementType === 'out') {
                newBalance = currentStock - quantity;
            }
            
            const balanceDisplay = document.getElementById('new_balance');
            balanceDisplay.textContent = newBalance.toFixed(2);
            
            // تحديد لون الرصيد الجديد
            if (newBalance < 0) {
                balanceDisplay.className = 'fw-bold fs-5 text-danger';
            } else if (newBalance <= parseFloat(document.getElementById('min_stock_display').textContent)) {
                balanceDisplay.className = 'fw-bold fs-5 text-warning';
            } else {
                balanceDisplay.className = 'fw-bold fs-5 text-success';
            }
        }
        
        // التحقق من صحة النموذج
        document.getElementById('movementForm').addEventListener('submit', function(e) {
            if (!selectedMovementType) {
                alert('يرجى اختيار نوع الحركة');
                e.preventDefault();
                return false;
            }
            
            const quantity = parseFloat(document.getElementById('quantity').value) || 0;
            
            if (selectedMovementType === 'out' && quantity > currentStock) {
                if (!confirm('الكمية المطلوب خصمها أكبر من المخزون الحالي. هل تريد المتابعة؟')) {
                    e.preventDefault();
                    return false;
                }
            }
            
            if (quantity <= 0) {
                alert('يرجى إدخال كمية صحيحة');
                e.preventDefault();
                return false;
            }
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
