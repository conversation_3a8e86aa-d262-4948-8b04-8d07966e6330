<?php
/**
 * SeaSystem - تقرير حركات المخزون
 * Stock Movements Report
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/Inventory.php';
require_once __DIR__ . '/classes/Product.php';

// التأكد من تسجيل الدخول
requireLogin();

$inventory = new Inventory();
$product = new Product();
$current_user = getCurrentUser();

// الحصول على المرشحات
$filters = [
    'date_from' => $_GET['date_from'] ?? date('Y-m-01'),
    'date_to' => $_GET['date_to'] ?? date('Y-m-d'),
    'product_id' => $_GET['product_id'] ?? '',
    'movement_type' => $_GET['movement_type'] ?? '',
    'reference_type' => $_GET['reference_type'] ?? ''
];

// الحصول على بيانات حركات المخزون
$movements_data = $inventory->getStockMovements($filters);
$products = $product->getAll();

// حساب الإحصائيات
$total_movements = count($movements_data);
$total_in = 0;
$total_out = 0;
$net_movement = 0;

foreach ($movements_data as $movement) {
    if ($movement['movement_type'] == 'in') {
        $total_in += $movement['quantity'];
    } else {
        $total_out += $movement['quantity'];
    }
}
$net_movement = $total_in - $total_out;

// أنواع الحركات
$movement_types = [
    'in' => 'وارد',
    'out' => 'صادر'
];

// أنواع المراجع
$reference_types = [
    'purchase' => 'مشتريات',
    'sale' => 'مبيعات',
    'adjustment' => 'تسوية',
    'transfer' => 'تحويل',
    'return' => 'مرتجع',
    'initial' => 'رصيد افتتاحي'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير حركات المخزون - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <style>
        .report-header {
            background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #fd7e14;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #fd7e14;
        }
        
        .filter-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .movement-in {
            color: #28a745;
            font-weight: bold;
        }
        
        .movement-out {
            color: #dc3545;
            font-weight: bold;
        }
        
        .movement-badge-in {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .movement-badge-out {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            .report-header {
                background: #f8f9fa !important;
                color: #333 !important;
                border: 2px solid #dee2e6;
            }
        }
    </style>

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }
        
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->
    
    <div class="container-fluid">
        <div class="row">
            <!-- المحتوى الرئيسي -->
            <div class="col-12 p-4">
                <!-- أزرار الإجراءات -->
                <div class="row mb-3 no-print">
                    <div class="col">
                        <a href="reports.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                        </a>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.print()">
                                <i class="fas fa-print me-2"></i>طباعة
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-2"></i>تصدير Excel
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                            </button>
                        </div>
                    </div>
                </div>

                <!-- رأس التقرير -->
                <div class="report-header">
                    <h1 class="mb-2">
                        <i class="fas fa-exchange-alt me-3"></i>تقرير حركات المخزون
                    </h1>
                    <p class="mb-0">
                        من <?php echo date('d/m/Y', strtotime($filters['date_from'])); ?> 
                        إلى <?php echo date('d/m/Y', strtotime($filters['date_to'])); ?>
                    </p>
                    <small class="opacity-75">تم إنشاؤه في: <?php echo date('Y-m-d H:i'); ?></small>
                </div>

                <!-- مرشحات البحث -->
                <div class="filter-card no-print">
                    <h5 class="mb-3">
                        <i class="fas fa-filter me-2"></i>مرشحات التقرير
                    </h5>
                    <form method="GET" class="row g-3">
                        <div class="col-md-2">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" name="date_from" 
                                   value="<?php echo $filters['date_from']; ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" name="date_to" 
                                   value="<?php echo $filters['date_to']; ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">المنتج</label>
                            <select class="form-select" name="product_id">
                                <option value="">جميع المنتجات</option>
                                <?php foreach ($products as $prod): ?>
                                    <option value="<?php echo $prod['id']; ?>" 
                                            <?php echo $filters['product_id'] == $prod['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($prod['product_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">نوع الحركة</label>
                            <select class="form-select" name="movement_type">
                                <option value="">جميع الأنواع</option>
                                <?php foreach ($movement_types as $type => $type_name): ?>
                                    <option value="<?php echo $type; ?>" 
                                            <?php echo $filters['movement_type'] == $type ? 'selected' : ''; ?>>
                                        <?php echo $type_name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">نوع المرجع</label>
                            <select class="form-select" name="reference_type">
                                <option value="">جميع المراجع</option>
                                <?php foreach ($reference_types as $ref => $ref_name): ?>
                                    <option value="<?php echo $ref; ?>" 
                                            <?php echo $filters['reference_type'] == $ref ? 'selected' : ''; ?>>
                                        <?php echo $ref_name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- إحصائيات الحركات -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="text-muted mb-1">إجمالي الحركات</h6>
                                    <div class="stats-number">
                                        <?php echo $total_movements; ?>
                                        <small class="text-muted">حركة</small>
                                    </div>
                                </div>
                                <div class="text-warning">
                                    <i class="fas fa-exchange-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="text-muted mb-1">الكمية الواردة</h6>
                                    <div class="stats-number text-success">
                                        <?php echo number_format($total_in); ?>
                                        <small class="text-muted">وحدة</small>
                                    </div>
                                </div>
                                <div class="text-success">
                                    <i class="fas fa-arrow-down fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="text-muted mb-1">الكمية الصادرة</h6>
                                    <div class="stats-number text-danger">
                                        <?php echo number_format($total_out); ?>
                                        <small class="text-muted">وحدة</small>
                                    </div>
                                </div>
                                <div class="text-danger">
                                    <i class="fas fa-arrow-up fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="text-muted mb-1">صافي الحركة</h6>
                                    <div class="stats-number <?php echo $net_movement >= 0 ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo $net_movement >= 0 ? '+' : ''; ?><?php echo number_format($net_movement); ?>
                                        <small class="text-muted">وحدة</small>
                                    </div>
                                </div>
                                <div class="<?php echo $net_movement >= 0 ? 'text-success' : 'text-danger'; ?>">
                                    <i class="fas fa-<?php echo $net_movement >= 0 ? 'plus' : 'minus'; ?>-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول حركات المخزون -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>تفاصيل حركات المخزون
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>المنتج</th>
                                        <th>نوع الحركة</th>
                                        <th>الكمية</th>
                                        <th>نوع المرجع</th>
                                        <th>رقم المرجع</th>
                                        <th>الملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($movements_data)): ?>
                                        <tr>
                                            <td colspan="7" class="text-center py-4">
                                                <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                                <p class="text-muted mb-0">لا توجد حركات مخزون في الفترة المحددة</p>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($movements_data as $movement): ?>
                                            <tr>
                                                <td><?php echo date('d/m/Y H:i', strtotime($movement['movement_date'])); ?></td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($movement['product_name']); ?></strong>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($movement['product_code']); ?></small>
                                                </td>
                                                <td>
                                                    <span class="badge <?php echo $movement['movement_type'] == 'in' ? 'movement-badge-in' : 'movement-badge-out'; ?>">
                                                        <i class="fas fa-arrow-<?php echo $movement['movement_type'] == 'in' ? 'down' : 'up'; ?> me-1"></i>
                                                        <?php echo $movement_types[$movement['movement_type']]; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="<?php echo $movement['movement_type'] == 'in' ? 'movement-in' : 'movement-out'; ?>">
                                                        <?php echo $movement['movement_type'] == 'in' ? '+' : '-'; ?><?php echo number_format($movement['quantity']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <?php echo $reference_types[$movement['reference_type']] ?? $movement['reference_type']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if (!empty($movement['reference_number'])): ?>
                                                        <strong><?php echo htmlspecialchars($movement['reference_number']); ?></strong>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (!empty($movement['notes'])): ?>
                                                        <small><?php echo htmlspecialchars($movement['notes']); ?></small>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                                <?php if (!empty($movements_data)): ?>
                                    <tfoot class="table-light">
                                        <tr>
                                            <th colspan="3">الإجمالي</th>
                                            <th>
                                                <span class="text-success">+<?php echo number_format($total_in); ?></span> |
                                                <span class="text-danger">-<?php echo number_format($total_out); ?></span>
                                            </th>
                                            <th colspan="3">
                                                <strong>صافي: 
                                                    <span class="<?php echo $net_movement >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                        <?php echo $net_movement >= 0 ? '+' : ''; ?><?php echo number_format($net_movement); ?>
                                                    </span>
                                                </strong>
                                            </th>
                                        </tr>
                                    </tfoot>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>ملاحظات
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>الحركات الواردة تزيد من المخزون</li>
                                    <li><i class="fas fa-check text-success me-2"></i>الحركات الصادرة تقلل من المخزون</li>
                                    <li><i class="fas fa-check text-success me-2"></i>صافي الحركة = الوارد - الصادر</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>توزيع أنواع الحركات
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php
                                $type_counts = [];
                                foreach ($movements_data as $movement) {
                                    $type_counts[$movement['movement_type']] = ($type_counts[$movement['movement_type']] ?? 0) + 1;
                                }
                                ?>
                                <?php foreach ($type_counts as $type => $count): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span><?php echo $movement_types[$type]; ?></span>
                                        <span class="badge <?php echo $type == 'in' ? 'bg-success' : 'bg-danger'; ?>"><?php echo $count; ?></span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function exportToExcel() {
            alert('سيتم إضافة وظيفة تصدير Excel قريباً');
        }
        
        function exportToPDF() {
            alert('سيتم إضافة وظيفة تصدير PDF قريباً');
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
