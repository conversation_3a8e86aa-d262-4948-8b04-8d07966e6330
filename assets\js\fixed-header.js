/**
 * SeaSystem - نظام الهيدر الثابت التفاعلي
 * Interactive Fixed Header System
 */

class FixedHeader {
    constructor() {
        this.navbar = document.querySelector('.navbar');
        this.lastScrollTop = 0;
        this.scrollThreshold = 10;
        this.hideThreshold = 100;
        this.isScrolling = false;
        this.scrollTimer = null;

        this.init();
    }

    init() {
        if (!this.navbar) return;

        // إضافة الفئات الأساسية
        this.navbar.classList.add('navbar-fixed', 'navbar-default', 'navbar-optimized');

        // ربط الأحداث
        this.bindEvents();

        // تطبيق الحالة الأولية
        this.updateNavbarState();

        console.log('🚀 Fixed Header System initialized');
    }

    bindEvents() {
        // حدث التمرير مع تحسين الأداء
        let ticking = false;

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    this.handleScroll();
                    ticking = false;
                });
                ticking = true;
            }
        }, { passive: true });

        // حدث تغيير حجم النافذة
        window.addEventListener('resize', this.debounce(() => {
            this.updateNavbarState();
        }, 250));

        // أحداث الماوس للتأثيرات الإضافية
        this.navbar.addEventListener('mouseenter', () => {
            this.navbar.classList.add('navbar-enhanced');
        });

        this.navbar.addEventListener('mouseleave', () => {
            this.navbar.classList.remove('navbar-enhanced');
        });

        // حدث تحميل الصفحة
        window.addEventListener('load', () => {
            this.navbar.classList.remove('navbar-loading');
        });

        // إضافة تأثير التحميل
        this.navbar.classList.add('navbar-loading');
    }

    handleScroll() {
        const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollDirection = currentScrollTop > this.lastScrollTop ? 'down' : 'up';
        const scrollDistance = Math.abs(currentScrollTop - this.lastScrollTop);

        // تحديث حالة التمرير
        this.isScrolling = true;
        this.clearScrollTimer();
        this.scrollTimer = setTimeout(() => {
            this.isScrolling = false;
        }, 150);

        // تطبيق التأثيرات بناءً على اتجاه ومسافة التمرير
        this.updateNavbarAppearance(currentScrollTop, scrollDirection, scrollDistance);

        this.lastScrollTop = currentScrollTop;
    }

    updateNavbarAppearance(scrollTop, direction, distance) {
        // الهيدر ثابت دائماً - لا تغيير في المظهر
        this.navbar.classList.remove(
            'navbar-scrolled-up',
            'navbar-scrolled-down',
            'navbar-hidden',
            'navbar-glass'
        );

        // إبقاء الهيدر بنفس المظهر دائماً
        this.navbar.classList.add('navbar-default');

        // إبقاء المسافة العلوية ثابتة
        document.body.classList.remove('navbar-compact', 'navbar-minimal');

        // تحديث مؤشر التقدم (اختياري)
        this.updateProgressIndicator(scrollTop);
    }

    updateProgressIndicator(scrollTop) {
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;
        const progress = (scrollTop / (documentHeight - windowHeight)) * 100;

        // إنشاء أو تحديث شريط التقدم
        let progressBar = document.querySelector('.scroll-progress');
        if (!progressBar) {
            progressBar = document.createElement('div');
            progressBar.className = 'scroll-progress';
            progressBar.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 0%;
                height: 3px;
                background: linear-gradient(90deg, #ffd700 0%, #ff6b6b 100%);
                z-index: 1031;
                transition: width 0.1s ease;
                border-radius: 0 3px 3px 0;
            `;
            document.body.appendChild(progressBar);
        }

        progressBar.style.width = Math.min(progress, 100) + '%';
    }

    updateNavbarState() {
        // تحديث حالة الهيدر بناءً على حجم الشاشة
        const isMobile = window.innerWidth < 992;

        if (isMobile) {
            this.navbar.classList.add('navbar-mobile');
        } else {
            this.navbar.classList.remove('navbar-mobile');
        }
    }

    clearScrollTimer() {
        if (this.scrollTimer) {
            clearTimeout(this.scrollTimer);
            this.scrollTimer = null;
        }
    }

    // دالة مساعدة للتأخير
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // إضافة تأثيرات خاصة
    addSpecialEffects() {
        // تأثير الجسيمات (اختياري)
        this.createParticleEffect();

        // تأثير التموج عند النقر
        this.addRippleEffect();
    }

    createParticleEffect() {
        // تأثير جسيمات بسيط للخلفية
        const particles = document.createElement('div');
        particles.className = 'navbar-particles';
        particles.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        `;

        this.navbar.style.position = 'relative';
        this.navbar.appendChild(particles);

        // إنشاء جسيمات متحركة
        for (let i = 0; i < 5; i++) {
            setTimeout(() => {
                this.createParticle(particles);
            }, i * 1000);
        }
    }

    createParticle(container) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: float 6s infinite linear;
            left: ${Math.random() * 100}%;
            animation-delay: ${Math.random() * 6}s;
        `;

        // إضافة CSS للحركة
        if (!document.querySelector('#particle-styles')) {
            const style = document.createElement('style');
            style.id = 'particle-styles';
            style.textContent = `
                @keyframes float {
                    0% {
                        transform: translateY(100px) translateX(0px);
                        opacity: 0;
                    }
                    10% {
                        opacity: 1;
                    }
                    90% {
                        opacity: 1;
                    }
                    100% {
                        transform: translateY(-100px) translateX(${Math.random() * 100 - 50}px);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        container.appendChild(particle);

        // إزالة الجسيم بعد انتهاء الحركة
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 6000);
    }

    addRippleEffect() {
        const navLinks = this.navbar.querySelectorAll('.nav-link, .navbar-brand');

        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                const ripple = document.createElement('span');
                const rect = link.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                `;

                // إضافة CSS للتموج
                if (!document.querySelector('#ripple-styles')) {
                    const style = document.createElement('style');
                    style.id = 'ripple-styles';
                    style.textContent = `
                        @keyframes ripple {
                            to {
                                transform: scale(2);
                                opacity: 0;
                            }
                        }
                    `;
                    document.head.appendChild(style);
                }

                link.style.position = 'relative';
                link.style.overflow = 'hidden';
                link.appendChild(ripple);

                setTimeout(() => {
                    if (ripple.parentNode) {
                        ripple.parentNode.removeChild(ripple);
                    }
                }, 600);
            });
        });
    }

    // دوال للتحكم اليدوي
    show() {
        this.navbar.classList.remove('navbar-hidden');
    }

    hide() {
        this.navbar.classList.add('navbar-hidden');
    }

    setTheme(theme) {
        this.navbar.classList.remove('navbar-light', 'navbar-dark');
        this.navbar.classList.add(`navbar-${theme}`);
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    const fixedHeader = new FixedHeader();

    // إضافة التأثيرات الخاصة (اختياري)
    setTimeout(() => {
        fixedHeader.addSpecialEffects();
    }, 1000);

    // جعل الكائن متاحاً عالمياً للتحكم اليدوي
    window.FixedHeaderInstance = fixedHeader;
});

// دوال مساعدة عامة
window.HeaderUtils = {
    scrollToTop: () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    },

    toggleHeader: () => {
        const navbar = document.querySelector('.navbar');
        navbar.classList.toggle('navbar-hidden');
    },

    setHeaderStyle: (style) => {
        const navbar = document.querySelector('.navbar');
        navbar.className = navbar.className.replace(/navbar-\w+/g, '');
        navbar.classList.add(`navbar-${style}`);
    }
};
