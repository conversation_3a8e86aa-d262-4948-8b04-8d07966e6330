<?php
/**
 * SeaSystem - صفحة إضافة منتج جديد
 * Create Product Page
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/Inventory.php';

// التأكد من تسجيل الدخول
requireLogin();

$inventory = new Inventory();
$current_user = getCurrentUser();

// معالجة إضافة المنتج
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_product'])) {
    $product_data = [
        'product_code' => $_POST['product_code'],
        'barcode' => $_POST['barcode'],
        'product_name' => $_POST['product_name'],
        'description' => $_POST['description'],
        'category_id' => $_POST['category_id'],
        'base_unit_id' => $_POST['base_unit_id'],
        'cost_price' => $_POST['cost_price'],
        'selling_price' => $_POST['selling_price'],
        'current_stock' => $_POST['current_stock'],
        'min_stock_level' => $_POST['min_stock_level'],
        'max_stock_level' => $_POST['max_stock_level'],
        'is_active' => isset($_POST['is_active']) ? 1 : 0,
        'created_by' => $current_user['id']
    ];
    
    $result = $inventory->addProduct($product_data);
    $message = $result['message'];
    $message_type = $result['success'] ? 'success' : 'danger';
    
    if ($result['success']) {
        // إعادة توجيه إلى صفحة المخزون
        header('Location: inventory.php?success=' . urlencode($message));
        exit();
    }
}

// الحصول على البيانات المساعدة
$categories = $inventory->getProductCategories();
$units = $inventory->getUnitsOfMeasure();

// توليد رقم منتج تلقائي
$next_product_code = 'PRD' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة منتج جديد - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <style>
        .product-form-header {
            background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .form-section {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .section-title {
            color: #fd7e14;
            border-bottom: 2px solid #fd7e14;
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
        }
        
        .price-input {
            position: relative;
        }
        
        .currency-symbol {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            z-index: 10;
        }
        
        .price-field {
            padding-left: 40px;
        }
        
        .stock-indicator {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .barcode-scanner {
            position: relative;
        }
        
        .scan-btn {
            position: absolute;
            left: 5px;
            top: 50%;
            transform: translateY(-50%);
            border: none;
            background: none;
            color: #6c757d;
            font-size: 1.2rem;
        }
        
        .scan-btn:hover {
            color: #fd7e14;
        }
    </style>

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }
        
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->
    
    <div class="container-fluid">
        <div class="row">
            <!-- المحتوى الرئيسي -->
            <div class="col-12 p-4">
                <!-- رأس الصفحة -->
                <div class="product-form-header">
                    <h1 class="mb-2">
                        <i class="fas fa-plus me-3"></i>إضافة منتج جديد
                    </h1>
                    <p class="mb-0 opacity-75">إضافة منتج جديد إلى المخزون مع تحديد المعلومات الأساسية والأسعار</p>
                </div>
                
                <!-- أزرار التنقل -->
                <div class="row mb-3">
                    <div class="col">
                        <a href="inventory.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للمخزون
                        </a>
                    </div>
                </div>
                
                <!-- الرسائل -->
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- نموذج إضافة المنتج -->
                <form method="POST" id="productForm">
                    <div class="row">
                        <!-- المعلومات الأساسية -->
                        <div class="col-lg-6">
                            <div class="form-section">
                                <h4 class="section-title">
                                    <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                                </h4>
                                
                                <div class="mb-3">
                                    <label for="product_code" class="form-label">رمز المنتج *</label>
                                    <input type="text" class="form-control" id="product_code" name="product_code" 
                                           value="<?php echo $next_product_code; ?>" required>
                                    <div class="form-text">رمز فريد للمنتج (سيتم التحقق من عدم التكرار)</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="barcode" class="form-label">الباركود</label>
                                    <div class="barcode-scanner">
                                        <input type="text" class="form-control" id="barcode" name="barcode" 
                                               placeholder="اختياري - يمكن مسحه بالماسح الضوئي">
                                        <button type="button" class="scan-btn" onclick="scanBarcode()">
                                            <i class="fas fa-barcode"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="product_name" class="form-label">اسم المنتج *</label>
                                    <input type="text" class="form-control" id="product_name" name="product_name" 
                                           required placeholder="أدخل اسم المنتج">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="description" class="form-label">الوصف</label>
                                    <textarea class="form-control" id="description" name="description" 
                                              rows="3" placeholder="وصف تفصيلي للمنتج"></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="category_id" class="form-label">الفئة *</label>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value="">اختر الفئة</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo $category['id']; ?>">
                                                    <?php echo htmlspecialchars($category['category_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="base_unit_id" class="form-label">وحدة القياس *</label>
                                        <select class="form-select" id="base_unit_id" name="base_unit_id" required>
                                            <option value="">اختر الوحدة</option>
                                            <?php foreach ($units as $unit): ?>
                                                <option value="<?php echo $unit['id']; ?>">
                                                    <?php echo htmlspecialchars($unit['unit_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                    <label class="form-check-label" for="is_active">
                                        منتج نشط
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- الأسعار والمخزون -->
                        <div class="col-lg-6">
                            <div class="form-section">
                                <h4 class="section-title">
                                    <i class="fas fa-dollar-sign me-2"></i>الأسعار والمخزون
                                </h4>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="cost_price" class="form-label">سعر التكلفة *</label>
                                        <div class="price-input">
                                            <input type="number" class="form-control price-field" id="cost_price" 
                                                   name="cost_price" step="0.01" min="0" required 
                                                   placeholder="0.00" onchange="calculateProfit()">
                                            <span class="currency-symbol"><?php echo CURRENCY_SYMBOL; ?></span>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="selling_price" class="form-label">سعر البيع *</label>
                                        <div class="price-input">
                                            <input type="number" class="form-control price-field" id="selling_price" 
                                                   name="selling_price" step="0.01" min="0" required 
                                                   placeholder="0.00" onchange="calculateProfit()">
                                            <span class="currency-symbol"><?php echo CURRENCY_SYMBOL; ?></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="stock-indicator">
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <div class="text-muted small">هامش الربح</div>
                                                <div class="fw-bold" id="profit_margin">0%</div>
                                            </div>
                                            <div class="col-4">
                                                <div class="text-muted small">مبلغ الربح</div>
                                                <div class="fw-bold" id="profit_amount">0.00</div>
                                            </div>
                                            <div class="col-4">
                                                <div class="text-muted small">نسبة الربح</div>
                                                <div class="fw-bold" id="profit_percentage">0%</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="current_stock" class="form-label">المخزون الحالي</label>
                                    <input type="number" class="form-control" id="current_stock" name="current_stock" 
                                           step="0.01" min="0" value="0" placeholder="0.00">
                                    <div class="form-text">الكمية المتوفرة حالياً في المخزون</div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="min_stock_level" class="form-label">الحد الأدنى للمخزون</label>
                                        <input type="number" class="form-control" id="min_stock_level" 
                                               name="min_stock_level" step="0.01" min="0" value="0" 
                                               placeholder="0.00">
                                        <div class="form-text">تنبيه عند الوصول لهذا الحد</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="max_stock_level" class="form-label">الحد الأقصى للمخزون</label>
                                        <input type="number" class="form-control" id="max_stock_level" 
                                               name="max_stock_level" step="0.01" min="0" value="0" 
                                               placeholder="0.00">
                                        <div class="form-text">الحد الأقصى المسموح تخزينه</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="row">
                        <div class="col-12">
                            <div class="form-section text-center">
                                <button type="submit" name="create_product" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-save me-2"></i>حفظ المنتج
                                </button>
                                <button type="button" class="btn btn-success btn-lg me-3" onclick="saveAndAddAnother()">
                                    <i class="fas fa-plus me-2"></i>حفظ وإضافة آخر
                                </button>
                                <a href="inventory.php" class="btn btn-secondary btn-lg">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        // حساب هامش الربح
        function calculateProfit() {
            const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
            const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;
            
            const profitAmount = sellingPrice - costPrice;
            const profitMargin = costPrice > 0 ? (profitAmount / costPrice) * 100 : 0;
            const profitPercentage = sellingPrice > 0 ? (profitAmount / sellingPrice) * 100 : 0;
            
            document.getElementById('profit_amount').textContent = profitAmount.toFixed(2);
            document.getElementById('profit_margin').textContent = profitMargin.toFixed(1) + '%';
            document.getElementById('profit_percentage').textContent = profitPercentage.toFixed(1) + '%';
            
            // تغيير لون المؤشرات
            const profitElements = ['profit_amount', 'profit_margin', 'profit_percentage'];
            profitElements.forEach(id => {
                const element = document.getElementById(id);
                if (profitAmount > 0) {
                    element.className = 'fw-bold text-success';
                } else if (profitAmount < 0) {
                    element.className = 'fw-bold text-danger';
                } else {
                    element.className = 'fw-bold text-muted';
                }
            });
        }
        
        // مسح الباركود
        function scanBarcode() {
            alert('سيتم إضافة وظيفة مسح الباركود قريباً');
            // يمكن إضافة مكتبة مسح الباركود هنا
        }
        
        // حفظ وإضافة آخر
        function saveAndAddAnother() {
            // إضافة حقل مخفي للإشارة إلى الرغبة في إضافة منتج آخر
            const form = document.getElementById('productForm');
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'add_another';
            input.value = '1';
            form.appendChild(input);
            
            form.submit();
        }
        
        // توليد رمز منتج تلقائي
        function generateProductCode() {
            const categorySelect = document.getElementById('category_id');
            const categoryCode = categorySelect.options[categorySelect.selectedIndex].text.substring(0, 3).toUpperCase();
            const randomNumber = Math.floor(Math.random() * 9999) + 1;
            const productCode = categoryCode + String(randomNumber).padStart(4, '0');
            
            document.getElementById('product_code').value = productCode;
        }
        
        // تحديث رمز المنتج عند تغيير الفئة
        document.getElementById('category_id').addEventListener('change', function() {
            if (this.value && confirm('هل تريد توليد رمز منتج جديد بناءً على الفئة المختارة؟')) {
                generateProductCode();
            }
        });
        
        // التحقق من صحة النموذج
        document.getElementById('productForm').addEventListener('submit', function(e) {
            const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
            const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;
            const minStock = parseFloat(document.getElementById('min_stock_level').value) || 0;
            const maxStock = parseFloat(document.getElementById('max_stock_level').value) || 0;
            
            if (sellingPrice < costPrice) {
                if (!confirm('سعر البيع أقل من سعر التكلفة. هل تريد المتابعة؟')) {
                    e.preventDefault();
                    return false;
                }
            }
            
            if (maxStock > 0 && minStock > maxStock) {
                alert('الحد الأدنى للمخزون لا يمكن أن يكون أكبر من الحد الأقصى');
                e.preventDefault();
                return false;
            }
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
