<?php
/**
 * SeaSystem - API مدير الأرقام الذكي
 * Smart Number Manager API
 */

// تعريف الثابت للوصول
define('SEASYSTEM_ACCESS', true);

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/SmartNumberManager.php';
require_once 'includes/auth.php';

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'غير مصرح']);
    exit();
}

// إنشاء مدير الأرقام
$numberManager = new SmartNumberManager();

// تحديد نوع الطلب
$method = $_SERVER['REQUEST_METHOD'];
$action = $_REQUEST['action'] ?? '';
$entity_type = $_REQUEST['entity_type'] ?? '';

// معالجة الطلبات
try {
    switch ($action) {
        case 'get_next':
            // الحصول على الرقم التالي
            if (empty($entity_type)) {
                throw new Exception('نوع الكيان مطلوب');
            }
            
            $prefix = $_REQUEST['prefix'] ?? '';
            $padding = (int)($_REQUEST['padding'] ?? 3);
            $user_id = $_SESSION['user_id'] ?? null;
            
            $result = $numberManager->getNextNumber($entity_type, $prefix, $padding, $user_id);
            echo json_encode($result);
            break;
            
        case 'confirm':
            // تأكيد استخدام الرقم
            if (empty($entity_type)) {
                throw new Exception('نوع الكيان مطلوب');
            }
            
            $record_id = $_REQUEST['record_id'] ?? null;
            $user_id = $_SESSION['user_id'] ?? null;
            
            $result = $numberManager->confirmNumber($entity_type, $record_id, $user_id);
            echo json_encode($result);
            break;
            
        case 'cancel':
            // إلغاء حجز الرقم
            if (empty($entity_type)) {
                throw new Exception('نوع الكيان مطلوب');
            }
            
            $result = $numberManager->cancelReservation($entity_type);
            echo json_encode($result);
            break;
            
        case 'delete':
            // حذف رقم وإضافته لإعادة التدوير
            if (empty($entity_type) || empty($_REQUEST['number_code'])) {
                throw new Exception('نوع الكيان ورمز الرقم مطلوبان');
            }
            
            $number_code = $_REQUEST['number_code'];
            $record_id = $_REQUEST['record_id'] ?? null;
            $user_id = $_SESSION['user_id'] ?? null;
            $reason = $_REQUEST['reason'] ?? '';
            
            $result = $numberManager->deleteNumber($entity_type, $number_code, $record_id, $user_id, $reason);
            echo json_encode($result);
            break;
            
        case 'statistics':
            // الحصول على الإحصائيات
            $specific_type = $_REQUEST['specific_type'] ?? null;
            $result = $numberManager->getStatistics($specific_type);
            echo json_encode($result);
            break;
            
        case 'setup':
            // إنشاء الجداول المطلوبة
            $result = $numberManager->createTables();
            echo json_encode($result);
            break;
            
        case 'test':
            // اختبار النظام
            $test_results = [];
            
            // اختبار الحصول على رقم
            $test_results['get_number'] = $numberManager->getNextNumber('test', 'TEST', 3, 1);
            
            // اختبار التأكيد
            if ($test_results['get_number']['success']) {
                $test_results['confirm'] = $numberManager->confirmNumber('test', 999, 1);
            }
            
            // اختبار الحذف
            if (isset($test_results['confirm']) && $test_results['confirm']['success']) {
                $test_results['delete'] = $numberManager->deleteNumber('test', $test_results['confirm']['number'], 999, 1, 'اختبار النظام');
            }
            
            // اختبار إعادة التدوير
            $test_results['recycle'] = $numberManager->getNextNumber('test', 'TEST', 3, 1);
            
            echo json_encode([
                'success' => true,
                'test_results' => $test_results,
                'message' => 'تم اختبار النظام بنجاح'
            ]);
            break;
            
        default:
            throw new Exception('إجراء غير صحيح');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
