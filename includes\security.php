<?php
/**
 * SeaSystem - مكتبة الأمان الشاملة
 * Comprehensive Security Library
 */

class Security {

    /**
     * تنظيف المدخلات من XSS
     */
    public static function cleanInput($input, $allowHtml = false) {
        if (is_array($input)) {
            return array_map(function($item) use ($allowHtml) {
                return self::cleanInput($item, $allowHtml);
            }, $input);
        }

        if (!is_string($input)) {
            return $input;
        }

        // إزالة المسافات الزائدة
        $input = trim($input);

        if ($allowHtml) {
            // السماح ببعض HTML الآمن فقط
            $allowedTags = '<p><br><strong><em><u><ol><ul><li><h1><h2><h3><h4><h5><h6>';
            $input = strip_tags($input, $allowedTags);
        } else {
            // إزالة جميع HTML tags
            $input = strip_tags($input);
        }

        // تحويل الأحرف الخاصة
        $input = htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        return $input;
    }

    /**
     * التحقق من صحة البريد الإلكتروني
     */
    public static function validateEmail($email) {
        $email = filter_var($email, FILTER_SANITIZE_EMAIL);
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * التحقق من قوة كلمة المرور
     */
    public static function validatePassword($password, $minLength = 8) {
        $errors = [];

        if (strlen($password) < $minLength) {
            $errors[] = "كلمة المرور يجب أن تكون $minLength أحرف على الأقل";
        }

        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل";
        }

        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل";
        }

        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل";
        }

        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = "كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل";
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'strength' => self::calculatePasswordStrength($password)
        ];
    }

    /**
     * حساب قوة كلمة المرور
     */
    private static function calculatePasswordStrength($password) {
        $score = 0;
        $length = strlen($password);

        // النقاط بناءً على الطول
        if ($length >= 8) $score += 1;
        if ($length >= 12) $score += 1;
        if ($length >= 16) $score += 1;

        // النقاط بناءً على التنوع
        if (preg_match('/[a-z]/', $password)) $score += 1;
        if (preg_match('/[A-Z]/', $password)) $score += 1;
        if (preg_match('/[0-9]/', $password)) $score += 1;
        if (preg_match('/[^A-Za-z0-9]/', $password)) $score += 1;

        // تحديد مستوى القوة
        if ($score < 3) return 'ضعيف';
        if ($score < 5) return 'متوسط';
        if ($score < 7) return 'قوي';
        return 'قوي جداً';
    }

    /**
     * التحقق من صحة رقم الهاتف
     */
    public static function validatePhone($phone) {
        // إزالة المسافات والرموز
        $phone = preg_replace('/[^0-9+]/', '', $phone);

        // التحقق من الأرقام السعودية
        if (preg_match('/^(\+966|966|0)?[5][0-9]{8}$/', $phone)) {
            return true;
        }

        // التحقق من الأرقام الدولية
        if (preg_match('/^\+[1-9]\d{1,14}$/', $phone)) {
            return true;
        }

        return false;
    }

    /**
     * التحقق من الرقم الضريبي السعودي
     */
    public static function validateSaudiTaxNumber($taxNumber) {
        // إزالة المسافات والرموز
        $taxNumber = preg_replace('/[^0-9]/', '', $taxNumber);

        // يجب أن يكون 15 رقم
        if (strlen($taxNumber) !== 15) {
            return false;
        }

        // خوارزمية التحقق من الرقم الضريبي السعودي
        $sum = 0;
        for ($i = 0; $i < 14; $i++) {
            $digit = (int)$taxNumber[$i];
            if ($i % 2 === 0) {
                $digit *= 2;
                if ($digit > 9) {
                    $digit = ($digit % 10) + 1;
                }
            }
            $sum += $digit;
        }

        $checkDigit = (10 - ($sum % 10)) % 10;
        return $checkDigit == (int)$taxNumber[14];
    }

    /**
     * تشفير البيانات الحساسة
     */
    public static function encrypt($data, $key = null) {
        if ($key === null) {
            $key = defined('SECRET_KEY') ? SECRET_KEY : 'default_key';
        }

        $method = 'AES-256-CBC';
        $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length($method));
        $encrypted = openssl_encrypt($data, $method, $key, 0, $iv);

        return base64_encode($iv . $encrypted);
    }

    /**
     * فك تشفير البيانات
     */
    public static function decrypt($encryptedData, $key = null) {
        if ($key === null) {
            $key = defined('SECRET_KEY') ? SECRET_KEY : 'default_key';
        }

        $method = 'AES-256-CBC';
        $data = base64_decode($encryptedData);
        $ivLength = openssl_cipher_iv_length($method);
        $iv = substr($data, 0, $ivLength);
        $encrypted = substr($data, $ivLength);

        return openssl_decrypt($encrypted, $method, $key, 0, $iv);
    }

    /**
     * إنشاء رمز عشوائي آمن
     */
    public static function generateSecureToken($length = 32) {
        return bin2hex(random_bytes($length));
    }

    /**
     * التحقق من عنوان IP
     */
    public static function validateIP($ip) {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }

    /**
     * التحقق من URL
     */
    public static function validateURL($url) {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }

    /**
     * تسجيل الأحداث الأمنية
     */
    public static function logSecurityEvent($action, $details = '', $userId = null) {
        try {
            $database = new Database();
            $db = $database->getConnection();

            $sql = "INSERT INTO security_logs (user_id, action, ip_address, user_agent, details)
                    VALUES (:user_id, :action, :ip_address, :user_agent, :details)";

            $stmt = $db->prepare($sql);
            $ip_address = self::getClientIP();
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

            $stmt->bindParam(':user_id', $userId);
            $stmt->bindParam(':action', $action);
            $stmt->bindParam(':ip_address', $ip_address);
            $stmt->bindParam(':user_agent', $user_agent);
            $stmt->bindParam(':details', $details);

            $stmt->execute();
        } catch (Exception $e) {
            error_log("خطأ في تسجيل الحدث الأمني: " . $e->getMessage());
        }
    }

    /**
     * الحصول على عنوان IP الحقيقي
     */
    public static function getClientIP() {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];

        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * التحقق من الملفات المرفوعة
     */
    public static function validateUploadedFile($file, $allowedTypes = [], $maxSize = 5242880) {
        $errors = [];

        if (!isset($file['error']) || is_array($file['error'])) {
            $errors[] = 'معاملات الملف غير صحيحة';
            return ['valid' => false, 'errors' => $errors];
        }

        switch ($file['error']) {
            case UPLOAD_ERR_OK:
                break;
            case UPLOAD_ERR_NO_FILE:
                $errors[] = 'لم يتم اختيار ملف';
                break;
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                $errors[] = 'حجم الملف كبير جداً';
                break;
            default:
                $errors[] = 'خطأ غير معروف في رفع الملف';
                break;
        }

        if ($file['size'] > $maxSize) {
            $errors[] = 'حجم الملف يتجاوز الحد المسموح (' . ($maxSize / 1024 / 1024) . ' ميجابايت)';
        }

        if (!empty($allowedTypes)) {
            $finfo = new finfo(FILEINFO_MIME_TYPE);
            $mimeType = $finfo->file($file['tmp_name']);

            if (!in_array($mimeType, $allowedTypes)) {
                $errors[] = 'نوع الملف غير مسموح';
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}

/**
 * دوال مساعدة سريعة
 */
function clean_input($input, $allowHtml = false) {
    return Security::cleanInput($input, $allowHtml);
}

function validate_email($email) {
    return Security::validateEmail($email);
}

function validate_password($password, $minLength = 8) {
    return Security::validatePassword($password, $minLength);
}

function log_security($action, $details = '', $userId = null) {
    return Security::logSecurityEvent($action, $details, $userId);
}
?>
