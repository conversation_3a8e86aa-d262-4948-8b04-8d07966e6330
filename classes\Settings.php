<?php
/**
 * SeaSystem - فئة إدارة إعدادات النظام
 * System Settings Management Class
 */

require_once __DIR__ . '/../config/database.php';

class Settings {
    private $db;
    private $table_name = "system_settings";
    private static $cache = [];

    public function __construct() {
        try {
            $database = new Database();
            $this->db = $database->getConnection();

            if (!$this->db) {
                throw new Exception('فشل في الاتصال بقاعدة البيانات');
            }

            // إنشاء الجدول إذا لم يكن موجوداً
            $this->createTableIfNotExists();

        } catch (Exception $e) {
            error_log('خطأ في إنشاء فئة الإعدادات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إنشاء جدول الإعدادات إذا لم يكن موجوداً
     */
    private function createTableIfNotExists() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS " . $this->table_name . " (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) NOT NULL UNIQUE,
                setting_value TEXT,
                setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
                description TEXT,
                is_public BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_setting_key (setting_key),
                INDEX idx_is_public (is_public)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $this->db->exec($sql);

            // إضافة إعدادات افتراضية
            $this->insertDefaultSettings();

        } catch (Exception $e) {
            error_log('خطأ في إنشاء جدول الإعدادات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إضافة إعدادات افتراضية
     */
    private function insertDefaultSettings() {
        $defaults = [
            ['site_name', 'SeaSystem', 'string', 'اسم النظام', true],
            ['currency_symbol', 'ر.س', 'string', 'رمز العملة', true],
            ['timezone', 'Asia/Riyadh', 'string', 'المنطقة الزمنية', false],
            ['max_login_attempts', '5', 'number', 'عدد محاولات تسجيل الدخول المسموحة', false],
            ['session_timeout', '3600', 'number', 'مدة انتهاء الجلسة (ثانية)', false],
            ['maintenance_mode', 'false', 'boolean', 'وضع الصيانة', false],
            ['enable_2fa', 'false', 'boolean', 'تفعيل المصادقة الثنائية', false],
            ['company_name', 'شركة البحر', 'string', 'اسم الشركة', true],
            ['company_address', '', 'string', 'عنوان الشركة', true],
            ['company_phone', '', 'string', 'هاتف الشركة', true],
            ['company_email', '', 'string', 'بريد الشركة', true],
            ['tax_number', '', 'string', 'الرقم الضريبي', true],
            ['backup_frequency', 'daily', 'string', 'تكرار النسخ الاحتياطي', false],
            ['auto_backup', 'true', 'boolean', 'النسخ الاحتياطي التلقائي', false]
        ];

        foreach ($defaults as $default) {
            try {
                $checkSql = "SELECT id FROM " . $this->table_name . " WHERE setting_key = ?";
                $checkStmt = $this->db->prepare($checkSql);
                $checkStmt->execute([$default[0]]);

                if ($checkStmt->rowCount() == 0) {
                    $insertSql = "INSERT INTO " . $this->table_name . "
                                 (setting_key, setting_value, setting_type, description, is_public)
                                 VALUES (?, ?, ?, ?, ?)";
                    $insertStmt = $this->db->prepare($insertSql);
                    $insertStmt->execute($default);
                }
            } catch (Exception $e) {
                error_log('خطأ في إضافة إعداد افتراضي: ' . $e->getMessage());
            }
        }
    }

    /**
     * الحصول على قيمة إعداد
     */
    public function get($key, $default = null) {
        // التحقق من الكاش أولاً
        if (isset(self::$cache[$key])) {
            return self::$cache[$key];
        }

        try {
            // التحقق من وجود الاتصال
            if (!$this->db) {
                error_log('لا يوجد اتصال بقاعدة البيانات');
                return $default;
            }

            $sql = "SELECT setting_value, setting_type FROM " . $this->table_name . "
                    WHERE setting_key = :key";

            $stmt = $this->db->prepare($sql);
            if (!$stmt) {
                error_log('فشل في تحضير استعلام SQL');
                return $default;
            }

            $stmt->bindParam(':key', $key, PDO::PARAM_STR);

            if (!$stmt->execute()) {
                error_log('فشل في تنفيذ استعلام الحصول على الإعداد');
                return $default;
            }

            if ($stmt->rowCount() > 0) {
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                if ($result && isset($result['setting_value']) && isset($result['setting_type'])) {
                    $value = $this->convertValue($result['setting_value'], $result['setting_type']);

                    // حفظ في الكاش
                    self::$cache[$key] = $value;

                    return $value;
                }
            }

            return $default;
        } catch (PDOException $e) {
            error_log("خطأ PDO في الحصول على الإعداد $key: " . $e->getMessage());
            return $default;
        } catch (Exception $e) {
            error_log("خطأ عام في الحصول على الإعداد $key: " . $e->getMessage());
            return $default;
        }
    }

    /**
     * تعيين قيمة إعداد
     */
    public function set($key, $value, $type = 'string', $description = null) {
        try {
            // التحقق من وجود الإعداد
            $checkSql = "SELECT id FROM " . $this->table_name . " WHERE setting_key = :key";
            $checkStmt = $this->db->prepare($checkSql);
            $checkStmt->bindParam(':key', $key);
            $checkStmt->execute();

            $stringValue = $this->convertToString($value, $type);

            if ($checkStmt->rowCount() > 0) {
                // تحديث الإعداد الموجود
                $sql = "UPDATE " . $this->table_name . "
                        SET setting_value = :value, setting_type = :type";

                if ($description !== null) {
                    $sql .= ", description = :description";
                }

                $sql .= " WHERE setting_key = :key";

                $stmt = $this->db->prepare($sql);
                $stmt->bindParam(':value', $stringValue);
                $stmt->bindParam(':type', $type);
                $stmt->bindParam(':key', $key);

                if ($description !== null) {
                    $stmt->bindParam(':description', $description);
                }
            } else {
                // إضافة إعداد جديد
                $sql = "INSERT INTO " . $this->table_name . "
                        (setting_key, setting_value, setting_type, description)
                        VALUES (:key, :value, :type, :description)";

                $stmt = $this->db->prepare($sql);
                $stmt->bindParam(':key', $key);
                $stmt->bindParam(':value', $stringValue);
                $stmt->bindParam(':type', $type);
                $stmt->bindParam(':description', $description);
            }

            if ($stmt->execute()) {
                // تحديث الكاش
                self::$cache[$key] = $value;

                return [
                    'success' => true,
                    'message' => 'تم حفظ الإعداد بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في حفظ الإعداد'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * الحصول على جميع الإعدادات
     */
    public function getAll($publicOnly = false) {
        try {
            $sql = "SELECT setting_key, setting_value, setting_type, description, is_public
                    FROM " . $this->table_name;

            if ($publicOnly) {
                $sql .= " WHERE is_public = 1";
            }

            $sql .= " ORDER BY setting_key";

            $stmt = $this->db->prepare($sql);
            $stmt->execute();

            $settings = [];
            while ($row = $stmt->fetch()) {
                $settings[$row['setting_key']] = [
                    'value' => $this->convertValue($row['setting_value'], $row['setting_type']),
                    'type' => $row['setting_type'],
                    'description' => $row['description'],
                    'is_public' => $row['is_public']
                ];
            }

            return $settings;
        } catch (Exception $e) {
            error_log("خطأ في الحصول على الإعدادات: " . $e->getMessage());
            return [];
        }
    }

    /**
     * حذف إعداد
     */
    public function delete($key) {
        try {
            $sql = "DELETE FROM " . $this->table_name . " WHERE setting_key = :key";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':key', $key);

            if ($stmt->execute()) {
                // إزالة من الكاش
                unset(self::$cache[$key]);

                return [
                    'success' => true,
                    'message' => 'تم حذف الإعداد بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في حذف الإعداد'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * تحويل القيمة من النص إلى النوع المطلوب
     */
    private function convertValue($value, $type) {
        try {
            if ($value === null) {
                return null;
            }

            switch ($type) {
                case 'boolean':
                    if (is_bool($value)) {
                        return $value;
                    }
                    return filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ?? false;

                case 'number':
                    if (is_numeric($value)) {
                        return (float)$value;
                    }
                    return 0;

                case 'json':
                    if (is_array($value)) {
                        return $value;
                    }
                    $decoded = json_decode($value, true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        return $decoded;
                    }
                    error_log('خطأ في تحليل JSON: ' . json_last_error_msg());
                    return [];

                case 'string':
                default:
                    return (string)$value;
            }
        } catch (Exception $e) {
            error_log('خطأ في تحويل القيمة: ' . $e->getMessage());
            return $value;
        }
    }

    /**
     * تحويل القيمة إلى نص للحفظ
     */
    private function convertToString($value, $type) {
        try {
            switch ($type) {
                case 'boolean':
                    return $value ? 'true' : 'false';

                case 'json':
                    $encoded = json_encode($value, JSON_UNESCAPED_UNICODE);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        error_log('خطأ في ترميز JSON: ' . json_last_error_msg());
                        return json_encode([]);
                    }
                    return $encoded;

                case 'number':
                    return (string)$value;

                case 'string':
                default:
                    return (string)$value;
            }
        } catch (Exception $e) {
            error_log('خطأ في تحويل القيمة إلى نص: ' . $e->getMessage());
            return (string)$value;
        }
    }

    /**
     * مسح الكاش
     */
    public static function clearCache() {
        self::$cache = [];
    }

    /**
     * دوال مساعدة سريعة
     */
    public function getSiteName() {
        return $this->get('site_name', 'SeaSystem');
    }

    public function getCurrencySymbol() {
        return $this->get('currency_symbol', 'ر.س');
    }

    public function getTimezone() {
        return $this->get('timezone', 'Asia/Riyadh');
    }

    public function getMaxLoginAttempts() {
        return $this->get('max_login_attempts', 5);
    }

    public function getSessionTimeout() {
        return $this->get('session_timeout', 3600);
    }

    public function isMaintenanceMode() {
        return $this->get('maintenance_mode', false);
    }

    public function is2FAEnabled() {
        return $this->get('enable_2fa', false);
    }

    /**
     * دوال إضافية للشركة
     */
    public function getCompanyName() {
        return $this->get('company_name', 'شركة البحر');
    }

    public function getCompanyAddress() {
        return $this->get('company_address', '');
    }

    public function getCompanyPhone() {
        return $this->get('company_phone', '');
    }

    public function getCompanyEmail() {
        return $this->get('company_email', '');
    }

    public function getTaxNumber() {
        return $this->get('tax_number', '');
    }

    /**
     * تحديث مجموعة من الإعدادات
     */
    public function setBulk($settings) {
        $results = [];
        $this->db->beginTransaction();

        try {
            foreach ($settings as $key => $data) {
                $value = $data['value'] ?? $data;
                $type = $data['type'] ?? 'string';
                $description = $data['description'] ?? null;

                $result = $this->set($key, $value, $type, $description);
                $results[$key] = $result;

                if (!$result['success']) {
                    throw new Exception('فشل في حفظ الإعداد: ' . $key);
                }
            }

            $this->db->commit();
            return [
                'success' => true,
                'message' => 'تم حفظ جميع الإعدادات بنجاح',
                'results' => $results
            ];

        } catch (Exception $e) {
            $this->db->rollBack();
            return [
                'success' => false,
                'message' => 'خطأ في حفظ الإعدادات: ' . $e->getMessage(),
                'results' => $results
            ];
        }
    }

    /**
     * التحقق من وجود إعداد
     */
    public function exists($key) {
        try {
            $sql = "SELECT id FROM " . $this->table_name . " WHERE setting_key = :key";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':key', $key);
            $stmt->execute();

            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            error_log('خطأ في التحقق من وجود الإعداد: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * إعادة تعيين الإعدادات الافتراضية
     */
    public function resetToDefaults() {
        try {
            $this->db->beginTransaction();

            // حذف جميع الإعدادات
            $sql = "DELETE FROM " . $this->table_name;
            $this->db->exec($sql);

            // إعادة إدراج الإعدادات الافتراضية
            $this->insertDefaultSettings();

            // مسح الكاش
            self::clearCache();

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'تم إعادة تعيين الإعدادات الافتراضية'
            ];

        } catch (Exception $e) {
            $this->db->rollBack();
            return [
                'success' => false,
                'message' => 'خطأ في إعادة تعيين الإعدادات: ' . $e->getMessage()
            ];
        }
    }
}
?>
