/**
 * SeaSystem - تصميم الشريط الجانبي الموحد
 * Unified Sidebar Design
 */

/* الشريط الجانبي */
.sidebar {
    background: #f8f9fa;
    min-height: calc(100vh - 76px);
    padding: 1rem 0;
    border-left: 1px solid #dee2e6;
}

.sidebar .nav-link {
    color: #495057;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    margin: 0.2rem 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.sidebar .nav-link:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: bold;
}

/* تأثيرات الأزرار السريعة */
.quick-action-btn {
    transition: all 0.3s ease;
    transform: translateY(0);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.quick-action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

/* تأثيرات مخصصة لكل لون */
.btn-primary.quick-action-btn:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    border-color: #004085;
}

.btn-success.quick-action-btn:hover {
    background: linear-gradient(135deg, #157347 0%, #0f5132 100%);
    border-color: #0f5132;
}

.btn-warning.quick-action-btn:hover {
    background: linear-gradient(135deg, #e0a800 0%, #b08800 100%);
    border-color: #b08800;
}

.btn-secondary.quick-action-btn:hover {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%);
    border-color: #343a40;
}

.btn-dark.quick-action-btn:hover {
    background: linear-gradient(135deg, #1c1f23 0%, #0c0d0f 100%);
    border-color: #0c0d0f;
}

.btn-info.quick-action-btn:hover {
    background: linear-gradient(135deg, #0dcaf0 0%, #087990 100%);
    border-color: #087990;
}

.btn-outline-primary.quick-action-btn:hover {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    border-color: #0056b3;
    color: white;
}

/* تأثير النبضة للأزرار المهمة */
.btn-primary.quick-action-btn {
    animation: pulse-primary 2s infinite;
}

@keyframes pulse-primary {
    0% {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1), 0 0 0 0 rgba(13, 110, 253, 0.4);
    }
    70% {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1), 0 0 0 10px rgba(13, 110, 253, 0);
    }
    100% {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1), 0 0 0 0 rgba(13, 110, 253, 0);
    }
}

/* إيقاف النبضة عند التمرير */
.btn-primary.quick-action-btn:hover {
    animation: none;
}

/* التصميم العام */
.main-content {
    padding: 2rem;
}

.welcome-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stat-icon.success { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
.stat-icon.warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stat-icon.info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }

.table-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    overflow: hidden;
}

.table-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem 1.5rem;
}

.badge-status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
}

/* تحسينات إضافية */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    text-align: center;
}

.content-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    padding: 2rem;
    margin-bottom: 2rem;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.action-buttons .btn {
    transition: all 0.3s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* تصميم الجداول */
.table-responsive {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
}

.table td {
    vertical-align: middle;
    border-color: #f1f3f4;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
    transition: all 0.2s ease;
}
