<?php
/**
 * SeaSystem - صفحة عرض الفاتورة
 * View Invoice Page
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/Invoice.php';
require_once __DIR__ . '/classes/Customer.php';
require_once __DIR__ . '/classes/Supplier.php';

// التأكد من تسجيل الدخول
requireLogin();

// التحقق من وجود معرف الفاتورة
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: invoices.php?error=' . urlencode('معرف الفاتورة مطلوب'));
    exit();
}

$invoice_id = (int)$_GET['id'];

// إنشاء كائنات الفئات
$invoice = new Invoice();
$customer = new Customer();
$supplier = new Supplier();

// الحصول على بيانات المستخدم الحالي
$current_user = getCurrentUser();

// الحصول على بيانات الفاتورة
$invoice_data = $invoice->getById($invoice_id);

if (!$invoice_data) {
    header('Location: invoices.php?error=' . urlencode('الفاتورة غير موجودة'));
    exit();
}

// الحصول على عناصر الفاتورة
$invoice_items = $invoice->getInvoiceItems($invoice_id);

// تحديد نوع الفاتورة
$is_sales = $invoice_data['invoice_type'] == 'sales';

// حالات الفاتورة
$status_labels = [
    'draft' => ['مسودة', 'secondary'],
    'sent' => ['مرسلة', 'primary'],
    'paid' => ['مدفوعة', 'success'],
    'overdue' => ['متأخرة', 'danger'],
    'cancelled' => ['ملغية', 'dark']
];

$status_info = $status_labels[$invoice_data['status']] ?? ['غير محدد', 'secondary'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم <?php echo $invoice_data['invoice_number']; ?> - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            .invoice-container { box-shadow: none !important; }
        }
        .invoice-header {
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .invoice-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
    </style>

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }
        
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <div class="container-fluid">
        <!-- أزرار الإجراءات -->
        <div class="row no-print mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <a href="invoices.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للفواتير
                        </a>
                    </div>
                    <div class="d-flex gap-2">
                        <button onclick="window.print()" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-print me-2"></i>طباعة
                        </button>
                        <a href="invoice_edit.php?id=<?php echo $invoice_id; ?>" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-edit me-2"></i>تعديل
                        </a>
                        <?php if ($invoice_data['status'] != 'paid'): ?>
                        <button onclick="markAsPaid(<?php echo $invoice_id; ?>)" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-check me-2"></i>تسجيل كمدفوعة
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- الفاتورة -->
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card invoice-container">
                    <div class="card-body p-5">
                        <!-- رأس الفاتورة -->
                        <div class="invoice-header">
                            <div class="row">
                                <div class="col-md-6">
                                    <h1 class="text-primary mb-3">
                                        <i class="fas fa-calculator me-2"></i>
                                        <?php echo SITE_NAME; ?>
                                    </h1>
                                    <p class="text-muted mb-0">نظام محاسبي متكامل</p>
                                    <p class="text-muted">المملكة العربية السعودية</p>
                                </div>
                                <div class="col-md-6 text-end">
                                    <h2 class="text-primary mb-3">
                                        <?php echo $is_sales ? 'فاتورة مبيعات' : 'فاتورة مشتريات'; ?>
                                    </h2>
                                    <p class="mb-1"><strong>رقم الفاتورة:</strong> <?php echo $invoice_data['invoice_number']; ?></p>
                                    <p class="mb-1"><strong>التاريخ:</strong> <?php echo formatDate($invoice_data['invoice_date']); ?></p>
                                    <p class="mb-1"><strong>تاريخ الاستحقاق:</strong> <?php echo formatDate($invoice_data['due_date']); ?></p>
                                    <span class="badge bg-<?php echo $status_info[1]; ?> fs-6"><?php echo $status_info[0]; ?></span>
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل العميل/المورد -->
                        <div class="invoice-details">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-<?php echo $is_sales ? 'user' : 'truck'; ?> me-2"></i>
                                        <?php echo $is_sales ? 'بيانات العميل' : 'بيانات المورد'; ?>
                                    </h5>
                                    <p class="mb-1"><strong>الاسم:</strong> <?php echo $is_sales ? $invoice_data['customer_name'] : $invoice_data['supplier_name']; ?></p>
                                    <p class="mb-1"><strong>البريد الإلكتروني:</strong> <?php echo $is_sales ? $invoice_data['customer_email'] : $invoice_data['supplier_email']; ?></p>
                                </div>
                                <div class="col-md-6">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-info-circle me-2"></i>
                                        معلومات إضافية
                                    </h5>
                                    <p class="mb-1"><strong>تم الإنشاء بواسطة:</strong> <?php echo $invoice_data['created_by_name']; ?></p>
                                    <p class="mb-1"><strong>تاريخ الإنشاء:</strong> <?php echo formatDateTime($invoice_data['created_at']); ?></p>
                                    <?php if ($invoice_data['updated_at'] != $invoice_data['created_at']): ?>
                                    <p class="mb-1"><strong>آخر تحديث:</strong> <?php echo formatDateTime($invoice_data['updated_at']); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- عناصر الفاتورة -->
                        <div class="table-responsive mb-4">
                            <table class="table table-bordered">
                                <thead class="table-primary">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="40%">الوصف</th>
                                        <th width="15%" class="text-center">الكمية</th>
                                        <th width="15%" class="text-end">سعر الوحدة</th>
                                        <th width="15%" class="text-end">الإجمالي</th>
                                        <th width="10%" class="text-center">الحساب</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $counter = 1;
                                    foreach ($invoice_items as $item):
                                    ?>
                                    <tr>
                                        <td class="text-center"><?php echo $counter++; ?></td>
                                        <td><?php echo htmlspecialchars($item['item_description']); ?></td>
                                        <td class="text-center"><?php echo number_format($item['quantity'], 0); ?></td>
                                        <td class="text-end"><?php echo formatCurrency($item['unit_price']); ?></td>
                                        <td class="text-end"><?php echo formatCurrency($item['total_price']); ?></td>
                                        <td class="text-center">
                                            <small class="text-muted"><?php echo $item['account_name'] ?? 'غير محدد'; ?></small>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- ملخص الفاتورة -->
                        <div class="row">
                            <div class="col-md-6">
                                <?php if (!empty($invoice_data['notes'])): ?>
                                <div class="mb-3">
                                    <h6 class="text-primary">ملاحظات:</h6>
                                    <p class="text-muted"><?php echo nl2br(htmlspecialchars($invoice_data['notes'])); ?></p>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <tr>
                                            <td class="text-end"><strong>المجموع الفرعي:</strong></td>
                                            <td class="text-end"><?php echo formatCurrency($invoice_data['subtotal']); ?></td>
                                        </tr>
                                        <?php if ($invoice_data['discount_amount'] > 0): ?>
                                        <tr>
                                            <td class="text-end"><strong>الخصم:</strong></td>
                                            <td class="text-end text-danger">-<?php echo formatCurrency($invoice_data['discount_amount']); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                        <?php if ($invoice_data['tax_amount'] > 0): ?>
                                        <tr>
                                            <td class="text-end"><strong>الضريبة:</strong></td>
                                            <td class="text-end"><?php echo formatCurrency($invoice_data['tax_amount']); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                        <tr class="table-primary">
                                            <td class="text-end"><strong>الإجمالي:</strong></td>
                                            <td class="text-end"><strong><?php echo formatCurrency($invoice_data['total_amount']); ?></strong></td>
                                        </tr>
                                        <?php if ($invoice_data['paid_amount'] > 0): ?>
                                        <tr>
                                            <td class="text-end"><strong>المدفوع:</strong></td>
                                            <td class="text-end text-success"><?php echo formatCurrency($invoice_data['paid_amount']); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="text-end"><strong>المتبقي:</strong></td>
                                            <td class="text-end text-warning">
                                                <strong><?php echo formatCurrency($invoice_data['total_amount'] - $invoice_data['paid_amount']); ?></strong>
                                            </td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- تذييل الفاتورة -->
                        <div class="text-center mt-5 pt-4 border-top">
                            <p class="text-muted mb-0">شكراً لتعاملكم معنا</p>
                            <p class="text-muted small">تم إنشاء هذه الفاتورة بواسطة <?php echo SITE_NAME; ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        // تسجيل الفاتورة كمدفوعة
        function markAsPaid(invoiceId) {
            if (confirm('هل أنت متأكد من تسجيل هذه الفاتورة كمدفوعة؟')) {
                // إنشاء نموذج وإرساله
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'invoice_payment.php';

                const invoiceInput = document.createElement('input');
                invoiceInput.type = 'hidden';
                invoiceInput.name = 'invoice_id';
                invoiceInput.value = invoiceId;

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'mark_paid';

                form.appendChild(invoiceInput);
                form.appendChild(actionInput);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
