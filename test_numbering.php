<?php
/**
 * SeaSystem - اختبار نظام الترقيم
 * Test Numbering System
 */

require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/classes/NumberGenerator.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الترقيم - SeaSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-vial text-primary me-2"></i>اختبار نظام الترقيم
                </h1>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-play me-2"></i>اختبار توليد الأرقام</h5>
                            </div>
                            <div class="card-body">
                                <button class="btn btn-primary mb-2" onclick="testCustomer()">اختبار رقم عميل</button>
                                <button class="btn btn-success mb-2" onclick="testSupplier()">اختبار رقم مورد</button>
                                <button class="btn btn-warning mb-2" onclick="testProduct()">اختبار رقم منتج</button>
                                <button class="btn btn-info mb-2" onclick="testWarehouse()">اختبار رقم مستودع</button>
                                
                                <div id="testResults" class="mt-3"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-database me-2"></i>حالة قاعدة البيانات</h5>
                            </div>
                            <div class="card-body">
                                <?php
                                try {
                                    $numberGenerator = new NumberGenerator();
                                    $stats = $numberGenerator->getNumberingStats();
                                    
                                    if (!empty($stats)) {
                                        echo '<table class="table table-sm">';
                                        echo '<thead><tr><th>الكيان</th><th>آخر رقم</th></tr></thead>';
                                        echo '<tbody>';
                                        foreach ($stats as $stat) {
                                            echo '<tr>';
                                            echo '<td>' . $stat['entity_type'] . '</td>';
                                            echo '<td>' . $stat['last_number'] . '</td>';
                                            echo '</tr>';
                                        }
                                        echo '</tbody></table>';
                                    } else {
                                        echo '<p class="text-warning">لا توجد بيانات ترقيم</p>';
                                    }
                                } catch (Exception $e) {
                                    echo '<p class="text-danger">خطأ: ' . $e->getMessage() . '</p>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-code me-2"></i>اختبار مباشر</h5>
                            </div>
                            <div class="card-body">
                                <?php
                                if (isset($_POST['test_action'])) {
                                    try {
                                        $numberGenerator = new NumberGenerator();
                                        
                                        switch ($_POST['test_action']) {
                                            case 'customer':
                                                $number = $numberGenerator->generateCustomerCode();
                                                echo '<div class="alert alert-success">رقم العميل الجديد: <strong>' . $number . '</strong></div>';
                                                break;
                                                
                                            case 'supplier':
                                                $number = $numberGenerator->generateSupplierCode();
                                                echo '<div class="alert alert-success">رقم المورد الجديد: <strong>' . $number . '</strong></div>';
                                                break;
                                                
                                            case 'product':
                                                $number = $numberGenerator->generateProductCode();
                                                echo '<div class="alert alert-success">رقم المنتج الجديد: <strong>' . $number . '</strong></div>';
                                                break;
                                                
                                            case 'warehouse':
                                                $number = $numberGenerator->generateWarehouseCode();
                                                echo '<div class="alert alert-success">رقم المستودع الجديد: <strong>' . $number . '</strong></div>';
                                                break;
                                        }
                                    } catch (Exception $e) {
                                        echo '<div class="alert alert-danger">خطأ: ' . $e->getMessage() . '</div>';
                                    }
                                }
                                ?>
                                
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="test_action" value="customer">
                                    <button type="submit" class="btn btn-outline-primary me-2">توليد رقم عميل</button>
                                </form>
                                
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="test_action" value="supplier">
                                    <button type="submit" class="btn btn-outline-success me-2">توليد رقم مورد</button>
                                </form>
                                
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="test_action" value="product">
                                    <button type="submit" class="btn btn-outline-warning me-2">توليد رقم منتج</button>
                                </form>
                                
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="test_action" value="warehouse">
                                    <button type="submit" class="btn btn-outline-info me-2">توليد رقم مستودع</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <a href="dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-home me-2"></i>العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testCustomer() {
            fetch('get_next_number.php?type=customer')
                .then(response => response.json())
                .then(data => {
                    const results = document.getElementById('testResults');
                    if (data.success) {
                        results.innerHTML += '<div class="alert alert-success">رقم العميل: <strong>' + data.number + '</strong></div>';
                    } else {
                        results.innerHTML += '<div class="alert alert-danger">خطأ: ' + data.error + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('testResults').innerHTML += '<div class="alert alert-danger">خطأ في الشبكة: ' + error + '</div>';
                });
        }
        
        function testSupplier() {
            fetch('get_next_number.php?type=supplier')
                .then(response => response.json())
                .then(data => {
                    const results = document.getElementById('testResults');
                    if (data.success) {
                        results.innerHTML += '<div class="alert alert-success">رقم المورد: <strong>' + data.number + '</strong></div>';
                    } else {
                        results.innerHTML += '<div class="alert alert-danger">خطأ: ' + data.error + '</div>';
                    }
                });
        }
        
        function testProduct() {
            fetch('get_next_number.php?type=product')
                .then(response => response.json())
                .then(data => {
                    const results = document.getElementById('testResults');
                    if (data.success) {
                        results.innerHTML += '<div class="alert alert-success">رقم المنتج: <strong>' + data.number + '</strong></div>';
                    } else {
                        results.innerHTML += '<div class="alert alert-danger">خطأ: ' + data.error + '</div>';
                    }
                });
        }
        
        function testWarehouse() {
            fetch('get_next_number.php?type=warehouse')
                .then(response => response.json())
                .then(data => {
                    const results = document.getElementById('testResults');
                    if (data.success) {
                        results.innerHTML += '<div class="alert alert-success">رقم المستودع: <strong>' + data.number + '</strong></div>';
                    } else {
                        results.innerHTML += '<div class="alert alert-danger">خطأ: ' + data.error + '</div>';
                    }
                });
        }
    </script>
</body>
</html>
