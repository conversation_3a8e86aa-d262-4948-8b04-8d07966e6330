<?php
/**
 * SeaSystem - إنشاء نظام الصلاحيات
 * Create Permissions System
 */

require_once __DIR__ . '/config/database.php';

try {
    $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>إنشاء نظام الصلاحيات</h2>";
    
    // 1. إنشاء جدول الصلاحيات
    $permissionsTable = "CREATE TABLE IF NOT EXISTS permissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        permission_key VARCHAR(100) UNIQUE NOT NULL,
        permission_name VARCHAR(255) NOT NULL,
        permission_group VARCHAR(100) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    $db->exec($permissionsTable);
    echo "✅ تم إنشاء جدول الصلاحيات<br>";
    
    // 2. إنشاء جدول صلاحيات المستخدمين
    $userPermissionsTable = "CREATE TABLE IF NOT EXISTS user_permissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        permission_key VARCHAR(100) NOT NULL,
        granted BOOLEAN DEFAULT TRUE,
        granted_by INT,
        granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
        UNIQUE KEY unique_user_permission (user_id, permission_key)
    )";
    
    $db->exec($userPermissionsTable);
    echo "✅ تم إنشاء جدول صلاحيات المستخدمين<br>";
    
    // 3. إدراج الصلاحيات الأساسية
    $permissions = [
        // لوحة التحكم
        ['dashboard_view', 'عرض لوحة التحكم', 'dashboard', 'عرض الصفحة الرئيسية والإحصائيات'],
        
        // إدارة العملاء
        ['customers_view', 'عرض العملاء', 'customers', 'عرض قائمة العملاء'],
        ['customers_add', 'إضافة عميل جديد', 'customers', 'إضافة عملاء جدد'],
        ['customers_edit', 'تعديل بيانات العملاء', 'customers', 'تعديل معلومات العملاء'],
        ['customers_delete', 'حذف العملاء', 'customers', 'حذف العملاء من النظام'],
        
        // إدارة الموردين
        ['suppliers_view', 'عرض الموردين', 'suppliers', 'عرض قائمة الموردين'],
        ['suppliers_add', 'إضافة مورد جديد', 'suppliers', 'إضافة موردين جدد'],
        ['suppliers_edit', 'تعديل بيانات الموردين', 'suppliers', 'تعديل معلومات الموردين'],
        ['suppliers_delete', 'حذف الموردين', 'suppliers', 'حذف الموردين من النظام'],
        
        // إدارة المخزون
        ['inventory_view', 'عرض المخزون', 'inventory', 'عرض المخزون والمنتجات'],
        ['inventory_add', 'إضافة منتجات', 'inventory', 'إضافة منتجات جديدة'],
        ['inventory_edit', 'تعديل المنتجات', 'inventory', 'تعديل بيانات المنتجات'],
        ['inventory_delete', 'حذف المنتجات', 'inventory', 'حذف المنتجات من النظام'],
        ['inventory_movements', 'حركات المخزون', 'inventory', 'إدارة حركات المخزون'],
        ['warehouses_manage', 'إدارة المستودعات', 'inventory', 'إدارة المستودعات'],
        
        // الفواتير
        ['invoices_view', 'عرض الفواتير', 'invoices', 'عرض جميع الفواتير'],
        ['sales_invoices_add', 'إنشاء فواتير مبيعات', 'invoices', 'إنشاء فواتير مبيعات جديدة'],
        ['purchase_invoices_add', 'إنشاء فواتير مشتريات', 'invoices', 'إنشاء فواتير مشتريات جديدة'],
        ['invoices_edit', 'تعديل الفواتير', 'invoices', 'تعديل الفواتير الموجودة'],
        ['invoices_delete', 'حذف الفواتير', 'invoices', 'حذف الفواتير'],
        ['invoices_print', 'طباعة الفواتير', 'invoices', 'طباعة وتصدير الفواتير'],
        
        // المدفوعات
        ['payments_view', 'عرض المدفوعات', 'payments', 'عرض جميع المدفوعات'],
        ['payments_add', 'إضافة مدفوعات', 'payments', 'إضافة مدفوعات جديدة'],
        ['payments_edit', 'تعديل المدفوعات', 'payments', 'تعديل المدفوعات'],
        ['payments_delete', 'حذف المدفوعات', 'payments', 'حذف المدفوعات'],
        
        // الحسابات
        ['accounts_view', 'عرض دليل الحسابات', 'accounts', 'عرض دليل الحسابات'],
        ['accounts_add', 'إضافة حسابات جديدة', 'accounts', 'إضافة حسابات جديدة'],
        ['accounts_edit', 'تعديل الحسابات', 'accounts', 'تعديل بيانات الحسابات'],
        ['accounts_delete', 'حذف الحسابات', 'accounts', 'حذف الحسابات'],
        
        // دفتر اليومية
        ['journal_view', 'عرض دفتر اليومية', 'journal', 'عرض القيود المحاسبية'],
        ['journal_add', 'إنشاء قيود محاسبية', 'journal', 'إنشاء قيود محاسبية جديدة'],
        ['journal_edit', 'تعديل القيود المحاسبية', 'journal', 'تعديل القيود المحاسبية'],
        ['journal_delete', 'حذف القيود المحاسبية', 'journal', 'حذف القيود المحاسبية'],
        
        // التقارير
        ['reports_view', 'عرض التقارير', 'reports', 'عرض جميع التقارير'],
        ['reports_sales', 'تقارير المبيعات', 'reports', 'عرض تقارير المبيعات'],
        ['reports_purchases', 'تقارير المشتريات', 'reports', 'عرض تقارير المشتريات'],
        ['reports_inventory', 'تقارير المخزون', 'reports', 'عرض تقارير المخزون'],
        ['reports_financial', 'التقارير المالية', 'reports', 'عرض التقارير المالية'],
        ['reports_customers', 'تقارير العملاء', 'reports', 'عرض تقارير العملاء'],
        ['reports_suppliers', 'تقارير الموردين', 'reports', 'عرض تقارير الموردين'],
        
        // إدارة النظام
        ['users_view', 'عرض المستخدمين', 'system', 'عرض قائمة المستخدمين'],
        ['users_add', 'إضافة مستخدمين', 'system', 'إضافة مستخدمين جدد'],
        ['users_edit', 'تعديل المستخدمين', 'system', 'تعديل بيانات المستخدمين'],
        ['users_delete', 'حذف المستخدمين', 'system', 'حذف المستخدمين'],
        ['users_permissions', 'إدارة صلاحيات المستخدمين', 'system', 'تعديل صلاحيات المستخدمين'],
        ['system_settings', 'إعدادات النظام', 'system', 'تعديل إعدادات النظام'],
        ['system_backup', 'النسخ الاحتياطي', 'system', 'إنشاء واستعادة النسخ الاحتياطية'],
        ['system_security', 'إعدادات الأمان', 'system', 'إدارة إعدادات الأمان']
    ];
    
    $stmt = $db->prepare("INSERT IGNORE INTO permissions (permission_key, permission_name, permission_group, description) VALUES (?, ?, ?, ?)");
    
    foreach ($permissions as $permission) {
        $stmt->execute($permission);
    }
    
    echo "✅ تم إدراج " . count($permissions) . " صلاحية أساسية<br>";
    
    // 4. منح جميع الصلاحيات لمدير النظام
    $adminUsers = $db->query("SELECT id FROM users WHERE role = 'admin'")->fetchAll();
    
    if (!empty($adminUsers)) {
        $stmt = $db->prepare("INSERT IGNORE INTO user_permissions (user_id, permission_key, granted_by) VALUES (?, ?, ?)");
        
        foreach ($adminUsers as $admin) {
            foreach ($permissions as $permission) {
                $stmt->execute([$admin['id'], $permission[0], $admin['id']]);
            }
        }
        
        echo "✅ تم منح جميع الصلاحيات لمدراء النظام<br>";
    }
    
    echo "<br><h3>✅ تم إنشاء نظام الصلاحيات بنجاح!</h3>";
    echo "<p><a href='users.php' class='btn btn-primary'>الذهاب إلى إدارة المستخدمين</a></p>";
    
} catch (PDOException $e) {
    echo "❌ خطأ: " . $e->getMessage();
}
?>
