<?php
/**
 * ملف لإضافة العملاء الجدد
 */

require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/classes/Customer.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    // حذف جميع العملاء الموجودين
    $sql = "DELETE FROM customers";
    $db->exec($sql);

    echo "تم حذف جميع العملاء القدامى<br>";

    // إضافة العملاء الجدد
    $customer = new Customer();

    $customers_data = [
        [
            'customer_code' => 'CUS001',
            'name' => 'أحمد محمد علي',
            'email' => '<EMAIL>',
            'phone' => '01111111111',
            'address' => 'شارع التحرير، القاهرة',
            'tax_number' => '*********',
            'credit_limit' => 50000.00,
            'opening_balance' => 5000.00
        ],
        [
            'customer_code' => 'CUS002',
            'name' => 'فاطمة حسن محمود',
            'email' => '<EMAIL>',
            'phone' => '01111111112',
            'address' => 'شارع الهرم، الجيزة',
            'tax_number' => '*********',
            'credit_limit' => 30000.00,
            'opening_balance' => 3000.00
        ],
        [
            'customer_code' => 'CUS003',
            'name' => 'محمد أحمد السيد',
            'email' => '<EMAIL>',
            'phone' => '01111111113',
            'address' => 'شارع الكورنيش، الإسكندرية',
            'tax_number' => '*********',
            'credit_limit' => 75000.00,
            'opening_balance' => 7500.00
        ],
        [
            'customer_code' => 'CUS004',
            'name' => 'سارة عبد الرحمن',
            'email' => '<EMAIL>',
            'phone' => '01111111114',
            'address' => 'شارع الجامعة، أسيوط',
            'tax_number' => '*********',
            'credit_limit' => 45000.00,
            'opening_balance' => 4500.00
        ],
        [
            'customer_code' => 'CUS005',
            'name' => 'خالد عبد الله',
            'email' => '<EMAIL>',
            'phone' => '01111111115',
            'address' => 'شارع النصر، المنصورة',
            'tax_number' => '*********',
            'credit_limit' => 60000.00,
            'opening_balance' => 6000.00
        ]
    ];

    foreach ($customers_data as $data) {
        $result = $customer->create($data);
        if ($result['success']) {
            echo "تم إضافة العميل: " . $data['name'] . "<br>";
        } else {
            echo "فشل في إضافة العميل: " . $data['name'] . " - " . $result['message'] . "<br>";
        }
    }

    echo "<br>تم الانتهاء من إضافة جميع العملاء!<br>";
    echo '<a href="customers.php">انتقل لصفحة العملاء</a>';

} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
