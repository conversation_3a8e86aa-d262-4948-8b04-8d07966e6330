<?php
/**
 * SeaSystem - إدارة النسخ الاحتياطي
 * Backup Manager Page
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/BackupManager.php';

// التأكد من تسجيل الدخول وصلاحيات المدير
requireLogin();
if (!hasPermission('admin')) {
    die('غير مصرح لك بالوصول إلى هذه الصفحة');
}

$backupManager = new BackupManager();
$message = '';
$messageType = '';

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create_backup':
            $description = $_POST['description'] ?? '';
            $result = $backupManager->createFullBackup($description);
            
            if ($result['success']) {
                $message = 'تم إنشاء النسخة الاحتياطية بنجاح: ' . $result['filename'];
                $messageType = 'success';
            } else {
                $message = $result['message'];
                $messageType = 'danger';
            }
            break;
            
        case 'restore_backup':
            $filename = $_POST['filename'] ?? '';
            $result = $backupManager->restoreBackup($filename);
            $message = $result['message'];
            $messageType = $result['success'] ? 'success' : 'danger';
            break;
            
        case 'delete_backup':
            $filename = $_POST['filename'] ?? '';
            $result = $backupManager->deleteBackup($filename);
            $message = $result['message'];
            $messageType = $result['success'] ? 'success' : 'danger';
            break;
            
        case 'cleanup_old':
            $days = intval($_POST['days'] ?? 30);
            $result = $backupManager->cleanupOldBackups($days);
            $message = $result['message'];
            $messageType = $result['success'] ? 'success' : 'danger';
            break;
    }
}

// الحصول على قائمة النسخ الاحتياطية
$backups = $backupManager->getBackupList();
$totalSize = $backupManager->getBackupDirectorySize();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة النسخ الاحتياطي - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة النسخ الاحتياطي</h1>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- إحصائيات النسخ الاحتياطي -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-database fa-2x text-primary mb-2"></i>
                                <h5 class="card-title"><?php echo count($backups); ?></h5>
                                <p class="card-text">إجمالي النسخ</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-hdd fa-2x text-info mb-2"></i>
                                <h5 class="card-title"><?php echo BackupManager::formatFileSize($totalSize); ?></h5>
                                <p class="card-text">المساحة المستخدمة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                <h5 class="card-title">
                                    <?php 
                                    if (!empty($backups)) {
                                        echo date('Y-m-d', strtotime($backups[0]['created_at']));
                                    } else {
                                        echo 'لا يوجد';
                                    }
                                    ?>
                                </h5>
                                <p class="card-text">آخر نسخة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                                <h5 class="card-title">نشط</h5>
                                <p class="card-text">حالة النظام</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إنشاء نسخة احتياطية جديدة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-plus me-2"></i>إنشاء نسخة احتياطية جديدة</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="row g-3">
                            <input type="hidden" name="action" value="create_backup">
                            <div class="col-md-8">
                                <label for="description" class="form-label">وصف النسخة الاحتياطية</label>
                                <input type="text" class="form-control" id="description" name="description" 
                                       placeholder="مثال: نسخة احتياطية قبل التحديث">
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-save me-2"></i>إنشاء نسخة احتياطية
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- قائمة النسخ الاحتياطية -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>النسخ الاحتياطية المتاحة</h5>
                        <button type="button" class="btn btn-warning btn-sm" data-bs-toggle="modal" data-bs-target="#cleanupModal">
                            <i class="fas fa-broom me-2"></i>تنظيف النسخ القديمة
                        </button>
                    </div>
                    <div class="card-body">
                        <?php if (empty($backups)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-database fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد نسخ احتياطية متاحة</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>اسم الملف</th>
                                            <th>الحجم</th>
                                            <th>النوع</th>
                                            <th>الوصف</th>
                                            <th>تاريخ الإنشاء</th>
                                            <th>آخر استعادة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($backups as $backup): ?>
                                            <tr>
                                                <td>
                                                    <i class="fas fa-file-archive me-2 text-primary"></i>
                                                    <?php echo htmlspecialchars($backup['filename']); ?>
                                                </td>
                                                <td><?php echo BackupManager::formatFileSize($backup['file_size']); ?></td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo htmlspecialchars($backup['backup_type']); ?></span>
                                                </td>
                                                <td><?php echo htmlspecialchars($backup['description'] ?: 'بدون وصف'); ?></td>
                                                <td><?php echo date('Y-m-d H:i', strtotime($backup['created_at'])); ?></td>
                                                <td>
                                                    <?php if ($backup['restored_at']): ?>
                                                        <span class="text-success">
                                                            <i class="fas fa-check me-1"></i>
                                                            <?php echo date('Y-m-d H:i', strtotime($backup['restored_at'])); ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-muted">لم يتم الاستعادة</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <button type="button" class="btn btn-outline-success" 
                                                                onclick="restoreBackup('<?php echo htmlspecialchars($backup['filename']); ?>')">
                                                            <i class="fas fa-undo"></i>
                                                        </button>
                                                        <a href="storage/backups/<?php echo htmlspecialchars($backup['filename']); ?>" 
                                                           class="btn btn-outline-primary" download>
                                                            <i class="fas fa-download"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-outline-danger" 
                                                                onclick="deleteBackup('<?php echo htmlspecialchars($backup['filename']); ?>')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- نافذة تنظيف النسخ القديمة -->
    <div class="modal fade" id="cleanupModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تنظيف النسخ القديمة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="cleanup_old">
                        <div class="mb-3">
                            <label for="days" class="form-label">حذف النسخ الأقدم من (بالأيام)</label>
                            <input type="number" class="form-control" id="days" name="days" value="30" min="1" max="365">
                            <div class="form-text">سيتم حذف جميع النسخ الاحتياطية الأقدم من العدد المحدد من الأيام</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-warning">تنظيف النسخ القديمة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نماذج مخفية للإجراءات -->
    <form id="restoreForm" method="POST" style="display: none;">
        <input type="hidden" name="action" value="restore_backup">
        <input type="hidden" name="filename" id="restoreFilename">
    </form>

    <form id="deleteForm" method="POST" style="display: none;">
        <input type="hidden" name="action" value="delete_backup">
        <input type="hidden" name="filename" id="deleteFilename">
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function restoreBackup(filename) {
            if (confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية!')) {
                document.getElementById('restoreFilename').value = filename;
                document.getElementById('restoreForm').submit();
            }
        }

        function deleteBackup(filename) {
            if (confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟ لا يمكن التراجع عن هذا الإجراء!')) {
                document.getElementById('deleteFilename').value = filename;
                document.getElementById('deleteForm').submit();
            }
        }
    </script>
</body>
</html>
