<?php
/**
 * SeaSystem - فئة التقارير المالية
 * Financial Reports Class
 */

require_once __DIR__ . '/../config/database.php';

class FinancialReport {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * ميزان المراجعة - Trial Balance
     */
    public function getTrialBalance($date_from = null, $date_to = null) {
        try {
            // إذا لم يتم تحديد التواريخ، استخدم السنة الحالية
            if (!$date_from) $date_from = date('Y-01-01');
            if (!$date_to) $date_to = date('Y-12-31');
            
            $sql = "SELECT 
                        a.id,
                        a.account_code,
                        a.account_name,
                        a.account_type,
                        a.parent_id,
                        COALESCE(SUM(jed.debit_amount), 0) as total_debit,
                        COALESCE(SUM(jed.credit_amount), 0) as total_credit,
                        (COALESCE(SUM(jed.debit_amount), 0) - COALESCE(SUM(jed.credit_amount), 0)) as balance
                    FROM chart_of_accounts a
                    LEFT JOIN journal_entry_details jed ON a.id = jed.account_id
                    LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id
                    WHERE (je.entry_date BETWEEN :date_from AND :date_to OR je.entry_date IS NULL)
                    AND (je.status = 'posted' OR je.status IS NULL)
                    AND a.is_active = 1
                    GROUP BY a.id, a.account_code, a.account_name, a.account_type, a.parent_id
                    ORDER BY a.account_code";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':date_from', $date_from);
            $stmt->bindParam(':date_to', $date_to);
            $stmt->execute();
            
            $accounts = $stmt->fetchAll();
            
            // حساب المجاميع
            $total_debit = 0;
            $total_credit = 0;
            
            foreach ($accounts as &$account) {
                $total_debit += $account['total_debit'];
                $total_credit += $account['total_credit'];
                
                // تحديد طبيعة الرصيد حسب نوع الحساب
                $account['natural_balance'] = $this->getNaturalBalance($account['account_type']);
                $account['display_balance'] = $this->calculateDisplayBalance($account['balance'], $account['account_type']);
            }
            
            return [
                'accounts' => $accounts,
                'total_debit' => $total_debit,
                'total_credit' => $total_credit,
                'is_balanced' => abs($total_debit - $total_credit) < 0.01,
                'date_from' => $date_from,
                'date_to' => $date_to
            ];
            
        } catch (Exception $e) {
            return [
                'accounts' => [],
                'total_debit' => 0,
                'total_credit' => 0,
                'is_balanced' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * الميزانية العمومية - Balance Sheet
     */
    public function getBalanceSheet($as_of_date = null) {
        try {
            if (!$as_of_date) $as_of_date = date('Y-m-d');
            
            // الأصول
            $assets = $this->getAccountsByType(['asset'], $as_of_date);
            
            // الخصوم
            $liabilities = $this->getAccountsByType(['liability'], $as_of_date);
            
            // حقوق الملكية
            $equity = $this->getAccountsByType(['equity'], $as_of_date);
            
            // حساب صافي الدخل من قائمة الدخل
            $income_statement = $this->getIncomeStatement(date('Y-01-01', strtotime($as_of_date)), $as_of_date);
            $net_income = $income_statement['net_income'];
            
            // إضافة صافي الدخل لحقوق الملكية
            if ($net_income != 0) {
                $equity[] = [
                    'account_code' => '9999',
                    'account_name' => 'صافي دخل السنة',
                    'balance' => $net_income,
                    'account_type' => 'equity'
                ];
            }
            
            // حساب المجاميع
            $total_assets = array_sum(array_column($assets, 'balance'));
            $total_liabilities = array_sum(array_column($liabilities, 'balance'));
            $total_equity = array_sum(array_column($equity, 'balance'));
            $total_liabilities_equity = $total_liabilities + $total_equity;
            
            return [
                'assets' => $assets,
                'liabilities' => $liabilities,
                'equity' => $equity,
                'total_assets' => $total_assets,
                'total_liabilities' => $total_liabilities,
                'total_equity' => $total_equity,
                'total_liabilities_equity' => $total_liabilities_equity,
                'is_balanced' => abs($total_assets - $total_liabilities_equity) < 0.01,
                'as_of_date' => $as_of_date
            ];
            
        } catch (Exception $e) {
            return [
                'assets' => [],
                'liabilities' => [],
                'equity' => [],
                'total_assets' => 0,
                'total_liabilities' => 0,
                'total_equity' => 0,
                'total_liabilities_equity' => 0,
                'is_balanced' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * قائمة الدخل - Income Statement
     */
    public function getIncomeStatement($date_from = null, $date_to = null) {
        try {
            if (!$date_from) $date_from = date('Y-01-01');
            if (!$date_to) $date_to = date('Y-m-d');
            
            // الإيرادات
            $revenues = $this->getAccountsByType(['revenue'], $date_to, $date_from);
            
            // المصروفات
            $expenses = $this->getAccountsByType(['expense'], $date_to, $date_from);
            
            // حساب المجاميع
            $total_revenue = array_sum(array_column($revenues, 'balance'));
            $total_expenses = array_sum(array_column($expenses, 'balance'));
            $gross_profit = $total_revenue;
            $net_income = $total_revenue - $total_expenses;
            
            // تصنيف المصروفات
            $operating_expenses = [];
            $other_expenses = [];
            
            foreach ($expenses as $expense) {
                if (strpos(strtolower($expense['account_name']), 'فوائد') !== false || 
                    strpos(strtolower($expense['account_name']), 'ضرائب') !== false) {
                    $other_expenses[] = $expense;
                } else {
                    $operating_expenses[] = $expense;
                }
            }
            
            $total_operating_expenses = array_sum(array_column($operating_expenses, 'balance'));
            $total_other_expenses = array_sum(array_column($other_expenses, 'balance'));
            $operating_income = $gross_profit - $total_operating_expenses;
            
            return [
                'revenues' => $revenues,
                'operating_expenses' => $operating_expenses,
                'other_expenses' => $other_expenses,
                'total_revenue' => $total_revenue,
                'gross_profit' => $gross_profit,
                'total_operating_expenses' => $total_operating_expenses,
                'operating_income' => $operating_income,
                'total_other_expenses' => $total_other_expenses,
                'net_income' => $net_income,
                'date_from' => $date_from,
                'date_to' => $date_to
            ];
            
        } catch (Exception $e) {
            return [
                'revenues' => [],
                'operating_expenses' => [],
                'other_expenses' => [],
                'total_revenue' => 0,
                'gross_profit' => 0,
                'total_operating_expenses' => 0,
                'operating_income' => 0,
                'total_other_expenses' => 0,
                'net_income' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * قائمة التدفقات النقدية - Cash Flow Statement
     */
    public function getCashFlowStatement($date_from = null, $date_to = null) {
        try {
            if (!$date_from) $date_from = date('Y-01-01');
            if (!$date_to) $date_to = date('Y-m-d');
            
            // التدفقات النقدية من الأنشطة التشغيلية
            $operating_activities = $this->getOperatingCashFlow($date_from, $date_to);
            
            // التدفقات النقدية من الأنشطة الاستثمارية
            $investing_activities = $this->getInvestingCashFlow($date_from, $date_to);
            
            // التدفقات النقدية من الأنشطة التمويلية
            $financing_activities = $this->getFinancingCashFlow($date_from, $date_to);
            
            // حساب المجاميع
            $net_operating_cash = array_sum(array_column($operating_activities, 'amount'));
            $net_investing_cash = array_sum(array_column($investing_activities, 'amount'));
            $net_financing_cash = array_sum(array_column($financing_activities, 'amount'));
            
            $net_cash_flow = $net_operating_cash + $net_investing_cash + $net_financing_cash;
            
            // رصيد النقدية في بداية ونهاية الفترة
            $cash_beginning = $this->getCashBalance($date_from, true);
            $cash_ending = $this->getCashBalance($date_to);
            
            return [
                'operating_activities' => $operating_activities,
                'investing_activities' => $investing_activities,
                'financing_activities' => $financing_activities,
                'net_operating_cash' => $net_operating_cash,
                'net_investing_cash' => $net_investing_cash,
                'net_financing_cash' => $net_financing_cash,
                'net_cash_flow' => $net_cash_flow,
                'cash_beginning' => $cash_beginning,
                'cash_ending' => $cash_ending,
                'date_from' => $date_from,
                'date_to' => $date_to
            ];
            
        } catch (Exception $e) {
            return [
                'operating_activities' => [],
                'investing_activities' => [],
                'financing_activities' => [],
                'net_operating_cash' => 0,
                'net_investing_cash' => 0,
                'net_financing_cash' => 0,
                'net_cash_flow' => 0,
                'cash_beginning' => 0,
                'cash_ending' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * دفتر الأستاذ العام - General Ledger
     */
    public function getGeneralLedger($account_id = null, $date_from = null, $date_to = null) {
        try {
            if (!$date_from) $date_from = date('Y-01-01');
            if (!$date_to) $date_to = date('Y-m-d');
            
            $sql = "SELECT 
                        je.entry_date,
                        je.entry_number,
                        je.description as entry_description,
                        jed.description as detail_description,
                        jed.debit_amount,
                        jed.credit_amount,
                        a.account_code,
                        a.account_name,
                        a.account_type
                    FROM journal_entry_details jed
                    JOIN journal_entries je ON jed.journal_entry_id = je.id
                    JOIN chart_of_accounts a ON jed.account_id = a.id
                    WHERE je.entry_date BETWEEN :date_from AND :date_to
                    AND je.status = 'posted'";
            
            $params = [
                ':date_from' => $date_from,
                ':date_to' => $date_to
            ];
            
            if ($account_id) {
                $sql .= " AND jed.account_id = :account_id";
                $params[':account_id'] = $account_id;
            }
            
            $sql .= " ORDER BY a.account_code, je.entry_date, je.id";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            $transactions = $stmt->fetchAll();
            
            // تجميع المعاملات حسب الحساب
            $ledger = [];
            $running_balance = 0;
            
            foreach ($transactions as $transaction) {
                $account_key = $transaction['account_code'];
                
                if (!isset($ledger[$account_key])) {
                    $ledger[$account_key] = [
                        'account_info' => [
                            'account_code' => $transaction['account_code'],
                            'account_name' => $transaction['account_name'],
                            'account_type' => $transaction['account_type']
                        ],
                        'transactions' => [],
                        'total_debit' => 0,
                        'total_credit' => 0,
                        'balance' => 0
                    ];
                }
                
                $ledger[$account_key]['transactions'][] = $transaction;
                $ledger[$account_key]['total_debit'] += $transaction['debit_amount'];
                $ledger[$account_key]['total_credit'] += $transaction['credit_amount'];
                $ledger[$account_key]['balance'] = $ledger[$account_key]['total_debit'] - $ledger[$account_key]['total_credit'];
            }
            
            return [
                'ledger' => $ledger,
                'date_from' => $date_from,
                'date_to' => $date_to
            ];
            
        } catch (Exception $e) {
            return [
                'ledger' => [],
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * كشف حساب العميل
     */
    public function getCustomerStatement($customer_id, $date_from = null, $date_to = null) {
        try {
            if (!$date_from) $date_from = date('Y-01-01');
            if (!$date_to) $date_to = date('Y-m-d');
            
            // معلومات العميل
            $sql = "SELECT * FROM customers WHERE id = :customer_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->execute();
            $customer = $stmt->fetch();
            
            if (!$customer) {
                throw new Exception('العميل غير موجود');
            }
            
            // الفواتير
            $sql = "SELECT 
                        'invoice' as type,
                        invoice_date as date,
                        invoice_number as reference,
                        'فاتورة مبيعات' as description,
                        total_amount as debit,
                        0 as credit,
                        payment_status
                    FROM invoices 
                    WHERE customer_id = :customer_id 
                    AND invoice_date BETWEEN :date_from AND :date_to
                    AND invoice_type = 'sales'";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->bindParam(':date_from', $date_from);
            $stmt->bindParam(':date_to', $date_to);
            $stmt->execute();
            $invoices = $stmt->fetchAll();
            
            // المدفوعات
            $sql = "SELECT 
                        'payment' as type,
                        payment_date as date,
                        payment_number as reference,
                        'مقبوض' as description,
                        0 as debit,
                        amount as credit,
                        status
                    FROM payments 
                    WHERE customer_id = :customer_id 
                    AND payment_date BETWEEN :date_from AND :date_to
                    AND payment_type = 'received'
                    AND status = 'completed'";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->bindParam(':date_from', $date_from);
            $stmt->bindParam(':date_to', $date_to);
            $stmt->execute();
            $payments = $stmt->fetchAll();
            
            // دمج وترتيب المعاملات
            $transactions = array_merge($invoices, $payments);
            usort($transactions, function($a, $b) {
                return strtotime($a['date']) - strtotime($b['date']);
            });
            
            // حساب الرصيد الجاري
            $running_balance = 0;
            $total_debit = 0;
            $total_credit = 0;
            
            foreach ($transactions as &$transaction) {
                $running_balance += $transaction['debit'] - $transaction['credit'];
                $transaction['balance'] = $running_balance;
                $total_debit += $transaction['debit'];
                $total_credit += $transaction['credit'];
            }
            
            return [
                'customer' => $customer,
                'transactions' => $transactions,
                'total_debit' => $total_debit,
                'total_credit' => $total_credit,
                'final_balance' => $running_balance,
                'date_from' => $date_from,
                'date_to' => $date_to
            ];
            
        } catch (Exception $e) {
            return [
                'customer' => null,
                'transactions' => [],
                'total_debit' => 0,
                'total_credit' => 0,
                'final_balance' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * تقرير أعمار الديون
     */
    public function getAgeingReport($as_of_date = null, $type = 'customers') {
        try {
            if (!$as_of_date) $as_of_date = date('Y-m-d');
            
            $table = $type == 'customers' ? 'customers' : 'suppliers';
            $invoice_type = $type == 'customers' ? 'sales' : 'purchase';
            $entity_field = $type == 'customers' ? 'customer_id' : 'supplier_id';
            
            $sql = "SELECT 
                        e.id,
                        e.name,
                        i.invoice_number,
                        i.invoice_date,
                        i.due_date,
                        i.total_amount,
                        i.paid_amount,
                        (i.total_amount - i.paid_amount) as outstanding_amount,
                        DATEDIFF(:as_of_date, i.due_date) as days_overdue
                    FROM $table e
                    JOIN invoices i ON e.id = i.$entity_field
                    WHERE i.payment_status IN ('unpaid', 'partial')
                    AND i.invoice_type = :invoice_type
                    ORDER BY e.name, i.due_date";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':as_of_date', $as_of_date);
            $stmt->bindParam(':invoice_type', $invoice_type);
            $stmt->execute();
            
            $invoices = $stmt->fetchAll();
            
            // تصنيف حسب العمر
            $ageing = [];
            $totals = [
                'current' => 0,
                '1_30' => 0,
                '31_60' => 0,
                '61_90' => 0,
                'over_90' => 0,
                'total' => 0
            ];
            
            foreach ($invoices as $invoice) {
                $entity_id = $invoice['id'];
                $days_overdue = $invoice['days_overdue'];
                $amount = $invoice['outstanding_amount'];
                
                if (!isset($ageing[$entity_id])) {
                    $ageing[$entity_id] = [
                        'name' => $invoice['name'],
                        'current' => 0,
                        '1_30' => 0,
                        '31_60' => 0,
                        '61_90' => 0,
                        'over_90' => 0,
                        'total' => 0,
                        'invoices' => []
                    ];
                }
                
                $ageing[$entity_id]['invoices'][] = $invoice;
                $ageing[$entity_id]['total'] += $amount;
                $totals['total'] += $amount;
                
                if ($days_overdue <= 0) {
                    $ageing[$entity_id]['current'] += $amount;
                    $totals['current'] += $amount;
                } elseif ($days_overdue <= 30) {
                    $ageing[$entity_id]['1_30'] += $amount;
                    $totals['1_30'] += $amount;
                } elseif ($days_overdue <= 60) {
                    $ageing[$entity_id]['31_60'] += $amount;
                    $totals['31_60'] += $amount;
                } elseif ($days_overdue <= 90) {
                    $ageing[$entity_id]['61_90'] += $amount;
                    $totals['61_90'] += $amount;
                } else {
                    $ageing[$entity_id]['over_90'] += $amount;
                    $totals['over_90'] += $amount;
                }
            }
            
            return [
                'ageing' => $ageing,
                'totals' => $totals,
                'type' => $type,
                'as_of_date' => $as_of_date
            ];
            
        } catch (Exception $e) {
            return [
                'ageing' => [],
                'totals' => [
                    'current' => 0,
                    '1_30' => 0,
                    '31_60' => 0,
                    '61_90' => 0,
                    'over_90' => 0,
                    'total' => 0
                ],
                'error' => $e->getMessage()
            ];
        }
    }
    
    // دوال مساعدة
    
    private function getAccountsByType($types, $as_of_date, $date_from = null) {
        $type_placeholders = str_repeat('?,', count($types) - 1) . '?';
        
        $sql = "SELECT 
                    a.account_code,
                    a.account_name,
                    a.account_type,
                    COALESCE(SUM(jed.debit_amount), 0) as total_debit,
                    COALESCE(SUM(jed.credit_amount), 0) as total_credit
                FROM chart_of_accounts a
                LEFT JOIN journal_entry_details jed ON a.id = jed.account_id
                LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id
                WHERE a.account_type IN ($type_placeholders)
                AND a.is_active = 1";
        
        $params = $types;
        
        if ($date_from) {
            $sql .= " AND (je.entry_date BETWEEN ? AND ? OR je.entry_date IS NULL)";
            $params[] = $date_from;
            $params[] = $as_of_date;
        } else {
            $sql .= " AND (je.entry_date <= ? OR je.entry_date IS NULL)";
            $params[] = $as_of_date;
        }
        
        $sql .= " AND (je.status = 'posted' OR je.status IS NULL)
                  GROUP BY a.id, a.account_code, a.account_name, a.account_type
                  ORDER BY a.account_code";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        $accounts = $stmt->fetchAll();
        
        foreach ($accounts as &$account) {
            $account['balance'] = $this->calculateDisplayBalance(
                $account['total_debit'] - $account['total_credit'], 
                $account['account_type']
            );
        }
        
        return $accounts;
    }
    
    private function getNaturalBalance($account_type) {
        $debit_types = ['asset', 'expense'];
        return in_array($account_type, $debit_types) ? 'debit' : 'credit';
    }
    
    private function calculateDisplayBalance($balance, $account_type) {
        $natural_balance = $this->getNaturalBalance($account_type);
        
        if ($natural_balance == 'debit') {
            return $balance;
        } else {
            return -$balance;
        }
    }
    
    private function getOperatingCashFlow($date_from, $date_to) {
        // هذه دالة مبسطة - يمكن تطويرها أكثر
        return [
            ['description' => 'صافي الدخل', 'amount' => 0],
            ['description' => 'تغيرات في رأس المال العامل', 'amount' => 0]
        ];
    }
    
    private function getInvestingCashFlow($date_from, $date_to) {
        return [
            ['description' => 'شراء أصول ثابتة', 'amount' => 0],
            ['description' => 'بيع أصول ثابتة', 'amount' => 0]
        ];
    }
    
    private function getFinancingCashFlow($date_from, $date_to) {
        return [
            ['description' => 'قروض جديدة', 'amount' => 0],
            ['description' => 'سداد قروض', 'amount' => 0]
        ];
    }
    
    private function getCashBalance($date, $beginning = false) {
        $operator = $beginning ? '<' : '<=';
        
        $sql = "SELECT 
                    COALESCE(SUM(jed.debit_amount), 0) - COALESCE(SUM(jed.credit_amount), 0) as balance
                FROM journal_entry_details jed
                JOIN journal_entries je ON jed.journal_entry_id = je.id
                JOIN chart_of_accounts a ON jed.account_id = a.id
                WHERE a.account_type = 'asset'
                AND (a.account_name LIKE '%نقد%' OR a.account_name LIKE '%بنك%' OR a.account_name LIKE '%صندوق%')
                AND je.entry_date $operator ?
                AND je.status = 'posted'";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$date]);
        
        $result = $stmt->fetch();
        return $result['balance'] ?? 0;
    }
}
?>
