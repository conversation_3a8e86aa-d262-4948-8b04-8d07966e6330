<?php
/**
 * SeaSystem - اختبار فئة الإعدادات
 * Test Settings Class
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/classes/Settings.php';

$message = '';
$messageType = '';
$testResults = [];

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $settings = new Settings();
        
        switch ($action) {
            case 'test_basic':
                // اختبار العمليات الأساسية
                $testResults['title'] = 'اختبار العمليات الأساسية';
                
                // اختبار الحفظ
                $result1 = $settings->set('test_string', 'مرحبا بالعالم', 'string', 'اختبار نص');
                $testResults['set_string'] = $result1;
                
                $result2 = $settings->set('test_number', 123.45, 'number', 'اختبار رقم');
                $testResults['set_number'] = $result2;
                
                $result3 = $settings->set('test_boolean', true, 'boolean', 'اختبار منطقي');
                $testResults['set_boolean'] = $result3;
                
                $result4 = $settings->set('test_json', ['key' => 'value', 'number' => 42], 'json', 'اختبار JSON');
                $testResults['set_json'] = $result4;
                
                // اختبار الاسترجاع
                $testResults['get_string'] = [
                    'expected' => 'مرحبا بالعالم',
                    'actual' => $settings->get('test_string'),
                    'success' => $settings->get('test_string') === 'مرحبا بالعالم'
                ];
                
                $testResults['get_number'] = [
                    'expected' => 123.45,
                    'actual' => $settings->get('test_number'),
                    'success' => $settings->get('test_number') === 123.45
                ];
                
                $testResults['get_boolean'] = [
                    'expected' => true,
                    'actual' => $settings->get('test_boolean'),
                    'success' => $settings->get('test_boolean') === true
                ];
                
                $testResults['get_json'] = [
                    'expected' => ['key' => 'value', 'number' => 42],
                    'actual' => $settings->get('test_json'),
                    'success' => $settings->get('test_json') === ['key' => 'value', 'number' => 42]
                ];
                
                $message = 'تم اختبار العمليات الأساسية';
                $messageType = 'success';
                break;
                
            case 'test_helpers':
                // اختبار الدوال المساعدة
                $testResults['title'] = 'اختبار الدوال المساعدة';
                
                $testResults['site_name'] = $settings->getSiteName();
                $testResults['currency_symbol'] = $settings->getCurrencySymbol();
                $testResults['company_name'] = $settings->getCompanyName();
                $testResults['timezone'] = $settings->getTimezone();
                $testResults['maintenance_mode'] = $settings->isMaintenanceMode();
                $testResults['2fa_enabled'] = $settings->is2FAEnabled();
                
                $message = 'تم اختبار الدوال المساعدة';
                $messageType = 'info';
                break;
                
            case 'test_bulk':
                // اختبار التحديث المجمع
                $bulkSettings = [
                    'bulk_test_1' => ['value' => 'قيمة 1', 'type' => 'string', 'description' => 'اختبار مجمع 1'],
                    'bulk_test_2' => ['value' => 999, 'type' => 'number', 'description' => 'اختبار مجمع 2'],
                    'bulk_test_3' => ['value' => false, 'type' => 'boolean', 'description' => 'اختبار مجمع 3']
                ];
                
                $result = $settings->setBulk($bulkSettings);
                $testResults['bulk_update'] = $result;
                
                // التحقق من النتائج
                $testResults['bulk_verify'] = [
                    'test_1' => $settings->get('bulk_test_1'),
                    'test_2' => $settings->get('bulk_test_2'),
                    'test_3' => $settings->get('bulk_test_3')
                ];
                
                $message = 'تم اختبار التحديث المجمع';
                $messageType = 'success';
                break;
                
            case 'test_cache':
                // اختبار الكاش
                $testResults['title'] = 'اختبار الكاش';
                
                // إضافة قيمة جديدة
                $settings->set('cache_test', 'قيمة أصلية', 'string');
                
                // قراءة القيمة (من قاعدة البيانات)
                $value1 = $settings->get('cache_test');
                $testResults['first_read'] = $value1;
                
                // قراءة القيمة مرة أخرى (من الكاش)
                $value2 = $settings->get('cache_test');
                $testResults['second_read'] = $value2;
                
                // مسح الكاش
                Settings::clearCache();
                
                // قراءة القيمة بعد مسح الكاش
                $value3 = $settings->get('cache_test');
                $testResults['after_clear'] = $value3;
                
                $testResults['cache_working'] = ($value1 === $value2 && $value2 === $value3);
                
                $message = 'تم اختبار الكاش';
                $messageType = 'info';
                break;
                
            case 'cleanup':
                // تنظيف بيانات الاختبار
                $testKeys = ['test_string', 'test_number', 'test_boolean', 'test_json', 
                           'bulk_test_1', 'bulk_test_2', 'bulk_test_3', 'cache_test'];
                
                $deleted = 0;
                foreach ($testKeys as $key) {
                    $result = $settings->delete($key);
                    if ($result['success']) {
                        $deleted++;
                    }
                }
                
                $message = "تم حذف $deleted من بيانات الاختبار";
                $messageType = 'warning';
                break;
                
            case 'reset_defaults':
                // إعادة تعيين الإعدادات الافتراضية
                $result = $settings->resetToDefaults();
                $testResults['reset'] = $result;
                
                $message = $result['message'];
                $messageType = $result['success'] ? 'success' : 'danger';
                break;
        }
        
    } catch (Exception $e) {
        $message = 'خطأ في الاختبار: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// الحصول على جميع الإعدادات
try {
    $settings = new Settings();
    $allSettings = $settings->getAll();
} catch (Exception $e) {
    $allSettings = [];
    if (empty($message)) {
        $message = 'خطأ في تحميل الإعدادات: ' . $e->getMessage();
        $messageType = 'danger';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار فئة الإعدادات - SeaSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .success-result { border-left: 4px solid #198754; }
        .error-result { border-left: 4px solid #dc3545; }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 0.75rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-cog text-primary me-2"></i>اختبار فئة الإعدادات
                </h1>
                
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                        <i class="fas fa-info-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- أزرار الاختبار -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-play me-2"></i>اختبارات النظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <form method="POST">
                                    <input type="hidden" name="action" value="test_basic">
                                    <button type="submit" class="btn btn-success w-100">
                                        <i class="fas fa-check me-2"></i>اختبار أساسي
                                    </button>
                                </form>
                                <small class="text-muted">حفظ واسترجاع القيم</small>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <form method="POST">
                                    <input type="hidden" name="action" value="test_helpers">
                                    <button type="submit" class="btn btn-info w-100">
                                        <i class="fas fa-tools me-2"></i>دوال مساعدة
                                    </button>
                                </form>
                                <small class="text-muted">اختبار الدوال المساعدة</small>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <form method="POST">
                                    <input type="hidden" name="action" value="test_bulk">
                                    <button type="submit" class="btn btn-warning w-100">
                                        <i class="fas fa-list me-2"></i>تحديث مجمع
                                    </button>
                                </form>
                                <small class="text-muted">حفظ متعدد</small>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <form method="POST">
                                    <input type="hidden" name="action" value="test_cache">
                                    <button type="submit" class="btn btn-secondary w-100">
                                        <i class="fas fa-memory me-2"></i>اختبار كاش
                                    </button>
                                </form>
                                <small class="text-muted">نظام التخزين المؤقت</small>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <form method="POST">
                                    <input type="hidden" name="action" value="cleanup">
                                    <button type="submit" class="btn btn-outline-warning w-100">
                                        <i class="fas fa-trash me-2"></i>تنظيف بيانات الاختبار
                                    </button>
                                </form>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <form method="POST" onsubmit="return confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')">
                                    <input type="hidden" name="action" value="reset_defaults">
                                    <button type="submit" class="btn btn-outline-danger w-100">
                                        <i class="fas fa-undo me-2"></i>إعادة تعيين افتراضي
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- نتائج الاختبار -->
                <?php if (!empty($testResults)): ?>
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i><?php echo $testResults['title'] ?? 'نتائج الاختبار'; ?>
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($testResults as $key => $result): ?>
                                <?php if ($key === 'title') continue; ?>
                                
                                <div class="test-result">
                                    <h6>
                                        <i class="fas fa-cog me-2"></i><?php echo $key; ?>
                                    </h6>
                                    
                                    <div class="code-block">
                                        <?php if (is_array($result)): ?>
                                            <pre><?php echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                                        <?php else: ?>
                                            <code><?php echo htmlspecialchars(var_export($result, true)); ?></code>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- جميع الإعدادات -->
                <div class="card mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>جميع الإعدادات الحالية
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($allSettings)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                <p>لا توجد إعدادات أو حدث خطأ في التحميل</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>المفتاح</th>
                                            <th>القيمة</th>
                                            <th>النوع</th>
                                            <th>الوصف</th>
                                            <th>عام</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($allSettings as $key => $setting): ?>
                                            <tr>
                                                <td><code><?php echo htmlspecialchars($key); ?></code></td>
                                                <td>
                                                    <?php if ($setting['type'] === 'json'): ?>
                                                        <small class="text-muted">JSON</small>
                                                        <pre class="small"><?php echo json_encode($setting['value'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                                                    <?php elseif ($setting['type'] === 'boolean'): ?>
                                                        <span class="badge bg-<?php echo $setting['value'] ? 'success' : 'secondary'; ?>">
                                                            <?php echo $setting['value'] ? 'صحيح' : 'خطأ'; ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <code><?php echo htmlspecialchars($setting['value']); ?></code>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo $setting['type']; ?></span>
                                                </td>
                                                <td>
                                                    <small class="text-muted"><?php echo htmlspecialchars($setting['description'] ?? ''); ?></small>
                                                </td>
                                                <td>
                                                    <?php if ($setting['is_public']): ?>
                                                        <i class="fas fa-eye text-success" title="عام"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-eye-slash text-muted" title="خاص"></i>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="text-center">
                    <a href="dashboard.php" class="btn btn-secondary btn-lg">
                        <i class="fas fa-home me-2"></i>العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
