<?php
/**
 * SeaSystem - صفحة إدارة العملاء (اختبار)
 * Customers Management Page (Test)
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/Customer.php';

// التأكد من تسجيل الدخول
requireLogin();
$current_user = getCurrentUser();

// إنشاء كائن العملاء
$customer = new Customer();

// الحصول على قائمة العملاء
$customers = $customer->getAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <link href="assets/css/sidebar-style.css" rel="stylesheet">

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }
        
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-3 col-lg-2 sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                    </a>
                    <a class="nav-link" href="accounts.php">
                        <i class="fas fa-list me-2"></i>دليل الحسابات
                    </a>
                    <a class="nav-link active" href="customers.php">
                        <i class="fas fa-users me-2"></i>العملاء
                    </a>
                    <a class="nav-link" href="suppliers.php">
                        <i class="fas fa-truck me-2"></i>الموردين
                    </a>
                    <a class="nav-link" href="invoices.php">
                        <i class="fas fa-file-invoice me-2"></i>الفواتير
                    </a>
                    <a class="nav-link" href="payments.php">
                        <i class="fas fa-credit-card me-2"></i>المدفوعات
                    </a>
                    <a class="nav-link" href="journal.php">
                        <i class="fas fa-book me-2"></i>دفتر اليومية
                    </a>
                    <a class="nav-link" href="inventory.php">
                        <i class="fas fa-boxes me-2"></i>المخزون
                    </a>
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i>التقارير
                    </a>
                </nav>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <h1 class="mb-2">
                        <i class="fas fa-users me-3"></i>إدارة العملاء
                    </h1>
                    <p class="mb-0 opacity-75">إضافة وإدارة بيانات العملاء</p>
                    <small class="opacity-75">تم التحديث في: <?php echo date('Y-m-d H:i'); ?></small>
                </div>

                <!-- بطاقة العمليات -->
                <div class="content-card">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>قائمة العملاء (<?php echo count($customers); ?>)
                        </h5>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة عميل جديد
                            </button>
                        </div>
                    </div>

                    <!-- جدول العملاء -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رمز العميل</th>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الهاتف</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($customers)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <i class="fas fa-users fa-2x text-muted mb-2"></i>
                                            <p class="text-muted mb-0">لا توجد عملاء حتى الآن</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($customers as $cust): ?>
                                        <tr>
                                            <td><strong><?php echo htmlspecialchars($cust['customer_code']); ?></strong></td>
                                            <td><?php echo htmlspecialchars($cust['name']); ?></td>
                                            <td><?php echo htmlspecialchars($cust['email']); ?></td>
                                            <td><?php echo htmlspecialchars($cust['phone']); ?></td>
                                            <td>
                                                <?php if ($cust['is_active']): ?>
                                                    <span class="badge bg-success">نشط</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn btn-sm btn-outline-primary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
