<?php
/**
 * ملف لإنشاء فواتير الشراء
 */

require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/classes/Invoice.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>إنشاء فواتير الشراء</h2>";
    
    // إنشاء فاتورة شراء 1: من شركة الأهرام للتجارة
    $invoice_data_1 = [
        'invoice_number' => 'PUR001',
        'invoice_type' => 'purchase',
        'supplier_id' => 1, // شركة الأهرام للتجارة
        'invoice_date' => date('Y-m-d'),
        'due_date' => date('Y-m-d', strtotime('+30 days')),
        'subtotal' => 46000.00,
        'tax_amount' => 6440.00,
        'total_amount' => 52440.00,
        'notes' => 'فاتورة شراء من شركة الأهرام للتجارة',
        'items' => [
            [
                'product_id' => 1, // لابتوب ديل
                'quantity' => 5,
                'unit_price' => 8000.00,
                'total_price' => 40000.00
            ],
            [
                'product_id' => 2, // طابعة HP
                'quantity' => 3,
                'unit_price' => 2000.00,
                'total_price' => 6000.00
            ]
        ]
    ];
    
    // إدراج الفاتورة الأولى
    $sql = "INSERT INTO invoices (invoice_number, invoice_type, supplier_id, invoice_date, due_date, subtotal, tax_amount, total_amount, notes, status) 
            VALUES (:invoice_number, :invoice_type, :supplier_id, :invoice_date, :due_date, :subtotal, :tax_amount, :total_amount, :notes, 'pending')";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([
        ':invoice_number' => $invoice_data_1['invoice_number'],
        ':invoice_type' => $invoice_data_1['invoice_type'],
        ':supplier_id' => $invoice_data_1['supplier_id'],
        ':invoice_date' => $invoice_data_1['invoice_date'],
        ':due_date' => $invoice_data_1['due_date'],
        ':subtotal' => $invoice_data_1['subtotal'],
        ':tax_amount' => $invoice_data_1['tax_amount'],
        ':total_amount' => $invoice_data_1['total_amount'],
        ':notes' => $invoice_data_1['notes']
    ]);
    
    $invoice_id_1 = $db->lastInsertId();
    
    // إدراج عناصر الفاتورة الأولى
    foreach ($invoice_data_1['items'] as $item) {
        $sql = "INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, total_price) 
                VALUES (:invoice_id, :product_id, :quantity, :unit_price, :total_price)";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            ':invoice_id' => $invoice_id_1,
            ':product_id' => $item['product_id'],
            ':quantity' => $item['quantity'],
            ':unit_price' => $item['unit_price'],
            ':total_price' => $item['total_price']
        ]);
        
        // تحديث المخزون
        $sql = "UPDATE products SET current_stock = current_stock + :quantity WHERE id = :product_id";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            ':quantity' => $item['quantity'],
            ':product_id' => $item['product_id']
        ]);
    }
    
    echo "تم إنشاء فاتورة الشراء الأولى: PUR001<br>";
    
    // إنشاء فاتورة شراء 2: من مؤسسة النيل للمواد
    $invoice_data_2 = [
        'invoice_number' => 'PUR002',
        'invoice_type' => 'purchase',
        'supplier_id' => 2, // مؤسسة النيل للمواد
        'invoice_date' => date('Y-m-d'),
        'due_date' => date('Y-m-d', strtotime('+30 days')),
        'subtotal' => 11750.00,
        'tax_amount' => 1645.00,
        'total_amount' => 13395.00,
        'notes' => 'فاتورة شراء من مؤسسة النيل للمواد',
        'items' => [
            [
                'product_id' => 3, // ماوس لاسلكي
                'quantity' => 25,
                'unit_price' => 150.00,
                'total_price' => 3750.00
            ],
            [
                'product_id' => 4, // كيبورد ميكانيكي
                'quantity' => 10,
                'unit_price' => 800.00,
                'total_price' => 8000.00
            ]
        ]
    ];
    
    // إدراج الفاتورة الثانية
    $stmt = $db->prepare($sql);
    $stmt->execute([
        ':invoice_number' => $invoice_data_2['invoice_number'],
        ':invoice_type' => $invoice_data_2['invoice_type'],
        ':supplier_id' => $invoice_data_2['supplier_id'],
        ':invoice_date' => $invoice_data_2['invoice_date'],
        ':due_date' => $invoice_data_2['due_date'],
        ':subtotal' => $invoice_data_2['subtotal'],
        ':tax_amount' => $invoice_data_2['tax_amount'],
        ':total_amount' => $invoice_data_2['total_amount'],
        ':notes' => $invoice_data_2['notes']
    ]);
    
    $invoice_id_2 = $db->lastInsertId();
    
    // إدراج عناصر الفاتورة الثانية
    foreach ($invoice_data_2['items'] as $item) {
        $sql = "INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, total_price) 
                VALUES (:invoice_id, :product_id, :quantity, :unit_price, :total_price)";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            ':invoice_id' => $invoice_id_2,
            ':product_id' => $item['product_id'],
            ':quantity' => $item['quantity'],
            ':unit_price' => $item['unit_price'],
            ':total_price' => $item['total_price']
        ]);
        
        // تحديث المخزون
        $sql = "UPDATE products SET current_stock = current_stock + :quantity WHERE id = :product_id";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            ':quantity' => $item['quantity'],
            ':product_id' => $item['product_id']
        ]);
    }
    
    echo "تم إنشاء فاتورة الشراء الثانية: PUR002<br>";
    
    echo "<br><strong>ملخص فواتير الشراء:</strong><br>";
    echo "- فاتورة PUR001: 52,440 ج.م من شركة الأهرام للتجارة<br>";
    echo "- فاتورة PUR002: 13,395 ج.م من مؤسسة النيل للمواد<br>";
    echo "- إجمالي المشتريات: 65,835 ج.م<br>";
    
    echo '<br><a href="invoices.php">انتقل لصفحة الفواتير</a>';
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
