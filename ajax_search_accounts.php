<?php
/**
 * البحث الديناميكي في الحسابات
 * Dynamic Account Search
 */

// تعريف الثابت للوصول
define('SEASYSTEM_ACCESS', true);

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/Account.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    exit('غير مصرح');
}

// التحقق من وجود مصطلح البحث
$search_term = $_GET['search'] ?? '';

// إنشاء كائن الحساب
$account = new Account($pdo);

// البحث في الحسابات
if (!empty($search_term)) {
    $accounts = $account->search($search_term);
} else {
    $accounts = $account->getAll();
}

// تصنيف الحسابات حسب النوع
$account_types = [
    'asset' => 'الأصول',
    'liability' => 'الخصوم',
    'equity' => 'حقوق الملكية',
    'revenue' => 'الإيرادات',
    'expense' => 'المصروفات'
];

// عرض النتائج
if (empty($accounts)) {
    echo '<tr>
            <td colspan="7" class="text-center py-4">
                <i class="fas fa-search fa-2x text-muted mb-2"></i>
                <p class="text-muted">لا توجد حسابات تطابق البحث</p>
            </td>
          </tr>';
} else {
    foreach ($accounts as $acc) {
        // حساب الرصيد
        $balance = 0; // يمكن حسابه من قاعدة البيانات إذا لزم الأمر
        $balance_class = $balance >= 0 ? 'text-success' : 'text-danger';

        echo '<tr>
                <td><strong>' . htmlspecialchars($acc['account_code']) . '</strong></td>
                <td>' . htmlspecialchars($acc['account_name']) . '</td>
                <td>
                    <span class="badge bg-secondary">' .
                        ($account_types[$acc['account_type']] ?? $acc['account_type']) .
                    '</span>
                </td>
                <td>' . htmlspecialchars($acc['parent_name'] ?? '-') . '</td>
                <td>
                    <span class="' . $balance_class . '">' .
                        number_format($balance, 2) . ' ' . CURRENCY_SYMBOL .
                    '</span>
                </td>
                <td>' .
                    (($acc['is_active'] ?? true) ?
                        '<span class="badge bg-success">نشط</span>' :
                        '<span class="badge bg-secondary">غير نشط</span>') .
                '</td>
                <td>
                    <div class="d-flex gap-2">
                        <div class="text-center">
                            <button type="button" class="btn btn-outline-primary btn-sm"
                                    onclick="editAccount(' . htmlspecialchars(json_encode($acc)) . ')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <small class="d-block text-muted mt-1">تعديل</small>
                        </div>
                        <div class="text-center">
                            <button type="button" class="btn btn-outline-danger btn-sm btn-delete"
                                    onclick="deleteAccount(' . $acc['id'] . ')">
                                <i class="fas fa-trash"></i>
                            </button>
                            <small class="d-block text-muted mt-1">حذف</small>
                        </div>
                    </div>
                </td>
              </tr>';
    }
}
?>
