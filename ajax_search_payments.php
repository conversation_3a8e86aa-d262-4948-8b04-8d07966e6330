<?php
/**
 * البحث الديناميكي في المدفوعات
 * Dynamic Payment Search
 */

// تعريف الثابت للوصول
define('SEASYSTEM_ACCESS', true);

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/Payment.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    exit('غير مصرح');
}

// التحقق من وجود مصطلح البحث
$search_term = $_GET['search'] ?? '';

// إنشاء كائن المدفوعات
$payment = new Payment($pdo);

// البحث في المدفوعات
if (!empty($search_term)) {
    $payments = $payment->search($search_term);
} else {
    $payments = $payment->getAll();
}

// أنواع وطرق المدفوعات
$payment_types = [
    'سند قبض' => 'سند قبض',
    'سند صرف' => 'سند صرف'
];

$payment_methods = [
    'cash' => 'نقدي',
    'bank_transfer' => 'تحويل بنكي',
    'check' => 'شيك',
    'credit_card' => 'بطاقة ائتمان'
];

$payment_statuses = [
    'pending' => 'معلق',
    'completed' => 'مكتمل',
    'cancelled' => 'ملغي'
];

// عرض النتائج
if (empty($payments)) {
    echo '<tr>
            <td colspan="7" class="text-center py-4">
                <i class="fas fa-search fa-2x text-muted mb-2"></i>
                <p class="text-muted">لا توجد مدفوعات تطابق البحث</p>
            </td>
          </tr>';
} else {
    foreach ($payments as $pay) {
        // تحديد لون النوع
        $type_class = $pay['payment_type'] == 'سند قبض' ? 'bg-success' : 'bg-primary';
        
        // تحديد لون الحالة
        $status_class = '';
        switch ($pay['status']) {
            case 'completed':
                $status_class = 'bg-success';
                break;
            case 'pending':
                $status_class = 'bg-warning';
                break;
            case 'cancelled':
                $status_class = 'bg-danger';
                break;
            default:
                $status_class = 'bg-secondary';
        }
        
        echo '<tr>
                <td>' . htmlspecialchars($pay['payment_number']) . '</td>
                <td>
                    <span class="badge ' . $type_class . '">' . 
                        htmlspecialchars($pay['payment_type']) . 
                    '</span>
                </td>
                <td>' . htmlspecialchars($pay['customer_name'] ?? $pay['supplier_name'] ?? 'غير محدد') . '</td>
                <td class="text-end">' . number_format($pay['amount'], 2) . ' ' . CURRENCY_SYMBOL . '</td>
                <td>' . ($payment_methods[$pay['payment_method']] ?? $pay['payment_method']) . '</td>
                <td>' . date('Y-m-d', strtotime($pay['payment_date'])) . '</td>
                <td>
                    <span class="badge ' . $status_class . '">' . 
                        ($payment_statuses[$pay['status']] ?? $pay['status']) . 
                    '</span>
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-info btn-sm" 
                                onclick="viewPayment(' . $pay['id'] . ')" title="عرض">
                            <i class="fas fa-eye"></i>
                            <small class="d-block">عرض</small>
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                onclick="editPayment(' . $pay['id'] . ')" title="تعديل">
                            <i class="fas fa-edit"></i>
                            <small class="d-block">تعديل</small>
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" 
                                onclick="printPayment(' . $pay['id'] . ')" title="طباعة">
                            <i class="fas fa-print"></i>
                            <small class="d-block">طباعة</small>
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" 
                                onclick="deletePayment(' . $pay['id'] . ', \'' . htmlspecialchars($pay['payment_number']) . '\')" title="حذف">
                            <i class="fas fa-trash"></i>
                            <small class="d-block">حذف</small>
                        </button>
                    </div>
                </td>
              </tr>';
    }
}
?>
