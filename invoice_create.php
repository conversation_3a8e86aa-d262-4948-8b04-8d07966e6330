<?php
/**
 * SeaSystem - صفحة إنشاء فاتورة جديدة
 * Create Invoice Page
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/Invoice.php';
require_once __DIR__ . '/classes/Customer.php';
require_once __DIR__ . '/classes/Supplier.php';
require_once __DIR__ . '/classes/Account.php';
require_once __DIR__ . '/classes/Product.php';
require_once __DIR__ . '/classes/Inventory.php';

// التأكد من تسجيل الدخول
requireLogin();

// إنشاء كائنات الفئات
$invoice = new Invoice();
$customer = new Customer();
$supplier = new Supplier();
$account = new Account();
$product = new Product();
$inventory = new Inventory();

// الحصول على بيانات المستخدم الحالي
$current_user = getCurrentUser();

// تحديد نوع الفاتورة
$invoice_type = $_GET['type'] ?? 'sales';
if (!in_array($invoice_type, ['sales', 'purchase'])) {
    $invoice_type = 'sales';
}

$message = '';
$message_type = '';

// معالجة إنشاء الفاتورة
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // بيانات الفاتورة الأساسية
    $invoice_data = [
        'invoice_number' => $_POST['invoice_number'],
        'invoice_type' => $invoice_type,
        'customer_id' => $invoice_type == 'sales' ? $_POST['customer_id'] : null,
        'supplier_id' => $invoice_type == 'purchase' ? $_POST['supplier_id'] : null,
        'invoice_date' => $_POST['invoice_date'],
        'due_date' => $_POST['due_date'],
        'subtotal' => $_POST['subtotal'],
        'tax_amount' => $_POST['tax_amount'] ?? 0,
        'discount_amount' => $_POST['discount_amount'] ?? 0,
        'total_amount' => $_POST['total_amount'],
        'notes' => $_POST['notes'] ?? '',
        'created_by' => $current_user['id']
    ];

    // عناصر الفاتورة
    $items_data = [];
    if (!empty($_POST['item_description'])) {
        for ($i = 0; $i < count($_POST['item_description']); $i++) {
            if (!empty($_POST['item_description'][$i])) {
                $items_data[] = [
                    'item_description' => $_POST['item_description'][$i],
                    'quantity' => $_POST['quantity'][$i],
                    'unit_price' => $_POST['unit_price'][$i],
                    'total_price' => $_POST['item_total'][$i],
                    'account_id' => $_POST['account_id'][$i] ?? null
                ];
            }
        }
    }

    if (empty($items_data)) {
        $message = 'يجب إضافة عنصر واحد على الأقل للفاتورة';
        $message_type = 'danger';
    } else {
        $result = $invoice->create($invoice_data, $items_data);
        $message = $result['message'];
        $message_type = $result['success'] ? 'success' : 'danger';

        if ($result['success']) {
            // إعادة توجيه إلى صفحة عرض الفاتورة
            header('Location: invoices.php?success=' . urlencode($message));
            exit();
        }
    }
}

// الحصول على البيانات المطلوبة
$customers = $customer->getAll();
$suppliers = $supplier->getAll();
$accounts = $account->getAll();
$products = $product->getAll();
$warehouses = $inventory->getWarehouses();

// توليد رقم فاتورة تلقائي
$next_invoice_number = $invoice->generateInvoiceNumber($invoice_type);

// أنواع الحسابات للفواتير
$invoice_accounts = $account->getByType($invoice_type == 'sales' ? 'revenue' : 'expense');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء <?php echo $invoice_type == 'sales' ? 'فاتورة مبيعات' : 'فاتورة مشتريات'; ?> - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }

        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }

        /* فرض الألوان حسب نوع الفاتورة - تجاوز style.css */
        <?php if ($invoice_type == 'sales'): ?>
        /* فواتير المبيعات - أخضر */
        body .card-header,
        body .card-header.invoice-header,
        body div.card-header,
        .container-fluid .card-header,
        .container-fluid div.card-header {
            background: #198754 !important;
            background-color: #198754 !important;
            background-image: none !important;
            color: white !important;
            border-color: #198754 !important;
        }
        <?php else: ?>
        /* فواتير المشتريات - أزرق فاتح */
        html body .card-header,
        html body .card-header.invoice-header,
        html body div.card-header,
        html .container-fluid .card-header,
        html .container-fluid div.card-header,
        .card .card-header,
        div.card div.card-header {
            background: #0dcaf0 !important;
            background-color: #0dcaf0 !important;
            background-image: none !important;
            color: white !important;
            border-color: #0dcaf0 !important;
        }
        <?php endif; ?>

        <?php if ($invoice_type == 'sales'): ?>
        /* رؤوس جداول المبيعات - أخضر */
        body thead,
        body thead.invoice-table-header,
        body table thead,
        .container-fluid thead,
        .container-fluid table thead {
            background-color: #198754 !important;
            background: #198754 !important;
            background-image: none !important;
            color: white !important;
        }
        <?php else: ?>
        /* رؤوس جداول المشتريات - أزرق فاتح */
        html body thead,
        html body thead.invoice-table-header,
        html body table thead,
        html .container-fluid thead,
        html .container-fluid table thead,
        table thead,
        .table thead {
            background-color: #0dcaf0 !important;
            background: #0dcaf0 !important;
            background-image: none !important;
            color: white !important;
        }
        <?php endif; ?>

        /* CSS إضافي لفرض الألوان */
        <?php if ($invoice_type == 'purchase'): ?>
        * .card-header {
            background: #0dcaf0 !important;
            background-color: #0dcaf0 !important;
            background-image: none !important;
            color: white !important;
        }

        * thead {
            background: #0dcaf0 !important;
            background-color: #0dcaf0 !important;
            background-image: none !important;
            color: white !important;
        }
        <?php endif; ?>

        .invoice-button-green {
            background-color: #198754 !important;
            border-color: #198754 !important;
            color: white !important;
        }

        .invoice-text-green {
            color: #198754 !important;
        }

        .invoice-outline-green {
            border-color: #198754 !important;
            color: #198754 !important;
        }

        /* تحديد أكثر دقة لعناصر الفاتورة */
        <?php if ($invoice_type == 'sales'): ?>
        #invoiceItemsTable thead,
        #invoiceItemsTable thead tr,
        #invoiceItemsTable thead th {
            background-color: #198754 !important;
            background: #198754 !important;
            color: white !important;
        }
        <?php else: ?>
        #invoiceItemsTable thead,
        #invoiceItemsTable thead tr,
        #invoiceItemsTable thead th {
            background-color: #0dcaf0 !important;
            background: #0dcaf0 !important;
            color: white !important;
        }
        <?php endif; ?>

        /* CSS قوي جداً لفرض ألوان المشتريات */
        <?php if ($invoice_type == 'purchase'): ?>
        .card-header,
        div.card-header,
        .card .card-header,
        .container-fluid .card-header,
        body .card-header,
        html body .card-header {
            background: #0dcaf0 !important;
            background-color: #0dcaf0 !important;
            background-image: none !important;
            color: white !important;
            border-color: #0dcaf0 !important;
        }

        thead,
        table thead,
        .table thead,
        tbody thead,
        #invoiceItemsTable thead,
        body thead,
        html body thead {
            background: #0dcaf0 !important;
            background-color: #0dcaf0 !important;
            background-image: none !important;
            color: white !important;
        }

        thead th,
        table thead th,
        .table thead th,
        #invoiceItemsTable thead th,
        body thead th,
        html body thead th {
            background: #0dcaf0 !important;
            background-color: #0dcaf0 !important;
            background-image: none !important;
            color: white !important;
        }
        <?php endif; ?>

        /* إصلاح زر إضافة عنصر - حل مشكلة hover */
        <?php if ($invoice_type == 'sales'): ?>
        .btn-outline-success:hover {
            background-color: #198754 !important;
            border-color: #198754 !important;
            color: white !important;
        }
        <?php else: ?>
        .btn-outline-info:hover {
            background-color: #0dcaf0 !important;
            border-color: #0dcaf0 !important;
            color: white !important;
        }
        <?php endif; ?>

        /* تأكيد ظهور النص بوضوح */
        .btn:hover {
            color: white !important;
        }

        /* تحديد أكثر دقة لزر إضافة عنصر */
        <?php if ($invoice_type == 'sales'): ?>
        #invoiceItemsTable .btn-outline-success:hover,
        .table .btn-outline-success:hover {
            background-color: #198754 !important;
            border-color: #198754 !important;
            color: white !important;
        }
        <?php else: ?>
        #invoiceItemsTable .btn-outline-info:hover,
        .table .btn-outline-info:hover {
            background-color: #0dcaf0 !important;
            border-color: #0dcaf0 !important;
            color: white !important;
        }
        <?php endif; ?>

        /* ضمان ظهور الأيقونة والنص */
        .btn:hover i,
        .btn:hover .fas {
            color: white !important;
        }

        /* CSS خاص لزر إضافة عنصر */
        .add-item-btn {
            transition: all 0.3s ease !important;
        }

        .add-item-btn:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
        }

        .add-item-btn:hover i {
            color: white !important;
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>

    <!-- JavaScript فوري لفرض الألوان -->
    <?php if ($invoice_type == 'purchase'): ?>
    <script>
        // تطبيق فوري لألوان المشتريات
        function forcePurchaseColors() {
            console.log('تطبيق ألوان المشتريات بقوة');

            // رؤوس البطاقات
            const headers = document.querySelectorAll('.card-header');
            headers.forEach(header => {
                header.style.setProperty('background', '#0dcaf0', 'important');
                header.style.setProperty('background-color', '#0dcaf0', 'important');
                header.style.setProperty('background-image', 'none', 'important');
                header.style.setProperty('color', 'white', 'important');
            });

            // رؤوس الجداول
            const theads = document.querySelectorAll('thead, thead th');
            theads.forEach(thead => {
                thead.style.setProperty('background', '#0dcaf0', 'important');
                thead.style.setProperty('background-color', '#0dcaf0', 'important');
                thead.style.setProperty('background-image', 'none', 'important');
                thead.style.setProperty('color', 'white', 'important');
            });
        }

        // تطبيق فوري
        forcePurchaseColors();

        // تطبيق عند تحميل DOM
        document.addEventListener('DOMContentLoaded', forcePurchaseColors);

        // تطبيق عند تحميل الصفحة
        window.addEventListener('load', forcePurchaseColors);

        // تطبيق متكرر
        setInterval(forcePurchaseColors, 1000);
    </script>
    <?php endif; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <div class="container-fluid">
        <div class="row">
            <!-- المحتوى الرئيسي -->
            <div class="col-12 p-4">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="page-title">
                                <i class="fas fa-<?php echo $invoice_type == 'sales' ? 'file-invoice-dollar' : 'file-invoice'; ?> me-2"></i>
                                إنشاء <?php echo $invoice_type == 'sales' ? 'فاتورة مبيعات' : 'فاتورة مشتريات'; ?>
                            </h1>
                            <p class="page-subtitle">إضافة فاتورة جديدة للنظام</p>
                        </div>
                        <div class="col-auto">
                            <a href="invoices.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-2"></i>العودة للفواتير
                            </a>
                        </div>
                    </div>
                </div>

                <!-- الرسائل -->
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- نموذج الفاتورة -->
                <form method="POST" id="invoiceForm">
                    <div class="row">
                        <!-- معلومات الفاتورة الأساسية -->
                        <div class="col-lg-8">
                            <div class="card mb-4">
                                <div class="card-header invoice-header" style="background-color: <?php echo $invoice_type == 'sales' ? '#198754' : '#0dcaf0'; ?> !important;">
                                    <h5 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>معلومات الفاتورة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="invoice_number" class="form-label">رقم الفاتورة *</label>
                                            <input type="text" class="form-control" id="invoice_number" name="invoice_number"
                                                   value="<?php echo $next_invoice_number; ?>" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="invoice_date" class="form-label">تاريخ الفاتورة *</label>
                                            <input type="date" class="form-control" id="invoice_date" name="invoice_date"
                                                   value="<?php echo date('Y-m-d'); ?>" required>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="<?php echo $invoice_type == 'sales' ? 'customer_id' : 'supplier_id'; ?>" class="form-label">
                                                <?php echo $invoice_type == 'sales' ? 'العميل' : 'المورد'; ?> *
                                            </label>
                                            <select class="form-select" id="<?php echo $invoice_type == 'sales' ? 'customer_id' : 'supplier_id'; ?>"
                                                    name="<?php echo $invoice_type == 'sales' ? 'customer_id' : 'supplier_id'; ?>" required>
                                                <option value="">اختر <?php echo $invoice_type == 'sales' ? 'العميل' : 'المورد'; ?></option>
                                                <?php
                                                $entities = $invoice_type == 'sales' ? $customers : $suppliers;
                                                foreach ($entities as $entity):
                                                ?>
                                                    <option value="<?php echo $entity['id']; ?>">
                                                        <?php echo $entity['name']; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="due_date" class="form-label">تاريخ الاستحقاق *</label>
                                            <input type="date" class="form-control" id="due_date" name="due_date"
                                                   value="<?php echo date('Y-m-d', strtotime('+30 days')); ?>" required>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="warehouse_id" class="form-label">المستودع *</label>
                                            <select class="form-select" id="warehouse_id" name="warehouse_id" required>
                                                <option value="">اختر المستودع</option>
                                                <?php foreach ($warehouses as $warehouse): ?>
                                                    <option value="<?php echo $warehouse['id']; ?>"
                                                            <?php echo $warehouse['warehouse_code'] == 'MAIN' ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($warehouse['warehouse_name']); ?>
                                                        <?php if (!empty($warehouse['location'])): ?>
                                                            - <?php echo htmlspecialchars($warehouse['location']); ?>
                                                        <?php endif; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                سيتم <?php echo $invoice_type == 'sales' ? 'خصم' : 'إضافة'; ?> المنتجات <?php echo $invoice_type == 'sales' ? 'من' : 'إلى'; ?> هذا المستودع
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="payment_method" class="form-label">طريقة الدفع</label>
                                            <select class="form-select" id="payment_method" name="payment_method">
                                                <option value="cash">نقدي</option>
                                                <option value="credit">آجل</option>
                                                <option value="bank_transfer">حوالة بنكية</option>
                                                <option value="check">شيك</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="notes" class="form-label">ملاحظات</label>
                                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- عناصر الفاتورة -->
                            <div class="card">
                                <div class="card-header invoice-header" style="background-color: <?php echo $invoice_type == 'sales' ? '#198754' : '#0dcaf0'; ?> !important;">
                                    <h5 class="mb-0">
                                        <i class="fas fa-list me-2"></i>عناصر الفاتورة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered" id="invoiceItemsTable">
                                            <thead class="invoice-table-header" style="background-color: <?php echo $invoice_type == 'sales' ? '#198754' : '#0dcaf0'; ?> !important; color: white !important;">
                                                <tr>
                                                    <th width="35%">الوصف</th>
                                                    <th width="15%">الكمية</th>
                                                    <th width="15%">سعر الوحدة</th>
                                                    <th width="15%">الإجمالي</th>
                                                    <th width="15%">الحساب</th>
                                                    <th width="5%">حذف</th>
                                                </tr>
                                            </thead>
                                            <tbody id="invoiceItemsBody">
                                                <!-- سيتم إضافة الصفوف هنا بواسطة JavaScript -->
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td colspan="6">
                                                        <button type="button" class="btn <?php echo $invoice_type == 'sales' ? 'btn-outline-success' : 'btn-outline-info'; ?> btn-sm add-item-btn"
                                                                style="border-color: <?php echo $invoice_type == 'sales' ? '#198754' : '#0dcaf0'; ?>; color: <?php echo $invoice_type == 'sales' ? '#198754' : '#0dcaf0'; ?>;"
                                                                onmouseover="this.style.backgroundColor='<?php echo $invoice_type == 'sales' ? '#198754' : '#0dcaf0'; ?>'; this.style.color='white';"
                                                                onmouseout="this.style.backgroundColor='transparent'; this.style.color='<?php echo $invoice_type == 'sales' ? '#198754' : '#0dcaf0'; ?>';"
                                                                onclick="addInvoiceItem()">
                                                            <i class="fas fa-plus me-2"></i>إضافة عنصر
                                                        </button>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ملخص الفاتورة -->
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header invoice-header" style="background-color: <?php echo $invoice_type == 'sales' ? '#198754' : '#0dcaf0'; ?> !important;">
                                    <h5 class="mb-0">
                                        <i class="fas fa-calculator me-2"></i>ملخص الفاتورة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label class="form-label">المجموع الفرعي:</label>
                                        </div>
                                        <div class="col-6 text-end">
                                            <strong id="subtotalDisplay">0.00</strong>
                                            <input type="hidden" name="subtotal" id="subtotal" value="0">
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label for="discount_amount" class="form-label">الخصم:</label>
                                        </div>
                                        <div class="col-6">
                                            <div class="input-group input-group-sm">
                                                <input type="number" class="form-control" id="discount_amount" name="discount_amount"
                                                       value="0" step="0.01" min="0" onchange="calculateTotal()">
                                                <span class="input-group-text"><?php echo CURRENCY_SYMBOL; ?></span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label for="tax_amount" class="form-label">الضريبة:</label>
                                        </div>
                                        <div class="col-6">
                                            <div class="input-group input-group-sm">
                                                <input type="number" class="form-control" id="tax_amount" name="tax_amount"
                                                       value="0" step="0.01" min="0" onchange="calculateTotal()">
                                                <span class="input-group-text"><?php echo CURRENCY_SYMBOL; ?></span>
                                            </div>
                                        </div>
                                    </div>

                                    <hr>

                                    <div class="row mb-4">
                                        <div class="col-6">
                                            <strong>الإجمالي:</strong>
                                        </div>
                                        <div class="col-6 text-end">
                                            <h4 class="mb-0" style="color: #198754;" id="totalDisplay">0.00 <?php echo CURRENCY_SYMBOL; ?></h4>
                                            <input type="hidden" name="total_amount" id="total_amount" value="0">
                                        </div>
                                    </div>

                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn <?php echo $invoice_type == 'sales' ? 'btn-success' : 'btn-info'; ?>"
                                                style="background-color: <?php echo $invoice_type == 'sales' ? '#198754' : '#0dcaf0'; ?>; border-color: <?php echo $invoice_type == 'sales' ? '#198754' : '#0dcaf0'; ?>; color: white;">
                                            <i class="fas fa-save me-2"></i>حفظ الفاتورة
                                        </button>
                                        <a href="invoices.php" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>إلغاء
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        let itemCounter = 0;

        // بيانات الحسابات والمنتجات
        const accounts = <?php echo json_encode($invoice_accounts); ?>;
        const products = <?php echo json_encode($products); ?>;

        // إضافة عنصر جديد للفاتورة
        function addInvoiceItem() {
            itemCounter++;
            const tbody = document.getElementById('invoiceItemsBody');
            const row = document.createElement('tr');
            row.id = `item_${itemCounter}`;

            row.innerHTML = `
                <td>
                    <input type="text" class="form-control" name="item_description[]"
                           placeholder="وصف العنصر" required>
                </td>
                <td>
                    <input type="number" class="form-control quantity-input" name="quantity[]"
                           value="1" min="1" step="1" onchange="calculateItemTotal(this)" required>
                </td>
                <td>
                    <input type="number" class="form-control price-input" name="unit_price[]"
                           value="0" min="0" step="0.01" onchange="calculateItemTotal(this)" required>
                </td>
                <td>
                    <input type="number" class="form-control total-input" name="item_total[]"
                           value="0" readonly>
                </td>
                <td>
                    <select class="form-select" name="account_id[]">
                        <option value="">اختر الحساب</option>
                        ${accounts.map(account =>
                            `<option value="${account.id}">${account.account_code} - ${account.account_name}</option>`
                        ).join('')}
                    </select>
                </td>
                <td>
                    <button type="button" class="btn btn-outline-danger btn-sm"
                            onclick="removeInvoiceItem('item_${itemCounter}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;

            tbody.appendChild(row);
        }

        // حذف عنصر من الفاتورة
        function removeInvoiceItem(itemId) {
            const row = document.getElementById(itemId);
            if (row) {
                row.remove();
                calculateTotal();
            }
        }

        // حساب إجمالي العنصر
        function calculateItemTotal(input) {
            const row = input.closest('tr');
            const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
            const price = parseFloat(row.querySelector('.price-input').value) || 0;
            const total = quantity * price;

            row.querySelector('.total-input').value = total.toFixed(2);
            calculateTotal();
        }

        // حساب الإجمالي العام
        function calculateTotal() {
            let subtotal = 0;

            // جمع جميع عناصر الفاتورة
            document.querySelectorAll('.total-input').forEach(input => {
                subtotal += parseFloat(input.value) || 0;
            });

            const discount = parseFloat(document.getElementById('discount_amount').value) || 0;
            const tax = parseFloat(document.getElementById('tax_amount').value) || 0;
            const total = subtotal - discount + tax;

            // تحديث العرض
            document.getElementById('subtotalDisplay').textContent = subtotal.toFixed(2);
            document.getElementById('totalDisplay').textContent = total.toFixed(2) + ' <?php echo CURRENCY_SYMBOL; ?>';

            // تحديث الحقول المخفية
            document.getElementById('subtotal').value = subtotal.toFixed(2);
            document.getElementById('total_amount').value = total.toFixed(2);
        }

        // إضافة عنصر افتراضي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addInvoiceItem();
        });

        // التحقق من صحة النموذج قبل الإرسال
        document.getElementById('invoiceForm').addEventListener('submit', function(e) {
            const items = document.querySelectorAll('#invoiceItemsBody tr');
            if (items.length === 0) {
                e.preventDefault();
                alert('يجب إضافة عنصر واحد على الأقل للفاتورة');
                return false;
            }

            const total = parseFloat(document.getElementById('total_amount').value);
            if (total <= 0) {
                e.preventDefault();
                alert('يجب أن يكون إجمالي الفاتورة أكبر من صفر');
                return false;
            }
        });

        // فرض الألوان حسب نوع الفاتورة
        // مبيعات: أخضر | مشتريات: أزرق فاتح
        const invoiceType = '<?php echo $invoice_type; ?>';
        const invoiceColor = invoiceType === 'sales' ? '#198754' : '#0dcaf0';
        const colorName = invoiceType === 'sales' ? 'أخضر' : 'أزرق فاتح';
        $(document).ready(function() {
            // فرض الألوان بقوة على جميع العناصر
            function forceInvoiceColors() {
                console.log(`تطبيق اللون ${colorName} (${invoiceColor}) على فاتورة ${invoiceType}`);

                // رؤوس البطاقات - إزالة التدرجات وفرض اللون المناسب
                $('.card-header').each(function() {
                    // إزالة جميع الأنماط الموجودة
                    $(this).removeAttr('style');
                    $(this).removeClass();

                    // تطبيق اللون الجديد
                    $(this).attr('style', `background: ${invoiceColor} !important; background-color: ${invoiceColor} !important; background-image: none !important; color: white !important; border-color: ${invoiceColor} !important;`);
                    $(this).addClass('card-header invoice-header');

                    // تطبيق CSS مباشر
                    $(this)[0].style.setProperty('background', invoiceColor, 'important');
                    $(this)[0].style.setProperty('background-color', invoiceColor, 'important');
                    $(this)[0].style.setProperty('background-image', 'none', 'important');
                    $(this)[0].style.setProperty('color', 'white', 'important');
                });

                // رؤوس الجداول
                $('thead, thead tr, thead th').each(function() {
                    // إزالة الأنماط القديمة
                    $(this).removeAttr('style');

                    // تطبيق اللون الجديد
                    $(this).attr('style', `background-color: ${invoiceColor} !important; color: white !important; background: ${invoiceColor} !important; background-image: none !important;`);

                    // تطبيق CSS مباشر
                    $(this)[0].style.setProperty('background-color', invoiceColor, 'important');
                    $(this)[0].style.setProperty('background', invoiceColor, 'important');
                    $(this)[0].style.setProperty('background-image', 'none', 'important');
                    $(this)[0].style.setProperty('color', 'white', 'important');
                });

                // تحديد خاص لجدول عناصر الفاتورة
                $('#invoiceItemsTable thead, #invoiceItemsTable thead tr, #invoiceItemsTable thead th').each(function() {
                    $(this).removeAttr('style');
                    $(this).attr('style', `background-color: ${invoiceColor} !important; color: white !important; background: ${invoiceColor} !important; background-image: none !important;`);
                    $(this)[0].style.setProperty('background-color', invoiceColor, 'important');
                    $(this)[0].style.setProperty('background', invoiceColor, 'important');
                    $(this)[0].style.setProperty('color', 'white', 'important');
                });
            }

            // تطبيق فوري
            forceInvoiceColors();

            // إعادة تطبيق متعددة
            setTimeout(forceInvoiceColors, 500);
            setTimeout(forceInvoiceColors, 1000);
            setTimeout(forceInvoiceColors, 2000);

            // إعادة تطبيق عند أي تغيير
            $(document).on('DOMNodeInserted', forceInvoiceColors);

            // تطبيق عند تحميل الصفحة بالكامل
            $(window).on('load', forceInvoiceColors);
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
