<?php
/**
 * اختبار سريع للتأكد من إصلاح الأخطاء
 */

// بدء الجلسة
session_start();

// محاكاة user_id للاختبار
$_SESSION['user_id'] = 1;

require_once __DIR__ . '/config/config.php';

echo "<h1>اختبار إصلاح الأخطاء</h1>";

try {
    // اختبار SmartNumberManager
    echo "<h2>1. اختبار SmartNumberManager</h2>";
    require_once __DIR__ . '/classes/SmartNumberManager.php';
    $smartManager = new SmartNumberManager();
    echo "✅ تم إنشاء SmartNumberManager بنجاح<br>";
    
    // اختبار إنشاء الجداول
    $result = $smartManager->createTables();
    if ($result['success']) {
        echo "✅ تم إنشاء الجداول بنجاح<br>";
    } else {
        echo "❌ فشل في إنشاء الجداول: " . $result['error'] . "<br>";
    }
    
    // اختبار الحصول على رقم
    $numberResult = $smartManager->getNextNumber('test_fix', 'TEST', 3, 1);
    if ($numberResult['success']) {
        echo "✅ تم الحصول على رقم: " . $numberResult['number'] . "<br>";
        
        // اختبار تأكيد الرقم
        $confirmResult = $smartManager->confirmNumber('test_fix', 999, 1);
        if ($confirmResult['success']) {
            echo "✅ تم تأكيد الرقم بنجاح<br>";
            
            // اختبار حذف الرقم
            $deleteResult = $smartManager->deleteNumber('test_fix', $confirmResult['number'], 999, 1, 'اختبار الحذف');
            if ($deleteResult['success']) {
                echo "✅ تم حذف الرقم وإضافته لإعادة التدوير<br>";
            } else {
                echo "❌ فشل في حذف الرقم<br>";
            }
        } else {
            echo "❌ فشل في تأكيد الرقم<br>";
        }
    } else {
        echo "❌ فشل في الحصول على رقم: " . ($numberResult['error'] ?? 'خطأ غير محدد') . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في SmartNumberManager: " . $e->getMessage() . "<br>";
}

try {
    // اختبار Settings
    echo "<h2>2. اختبار Settings</h2>";
    require_once __DIR__ . '/classes/Settings.php';
    $settings = new Settings();
    echo "✅ تم إنشاء Settings بنجاح<br>";
    
    // اختبار حفظ واسترجاع إعداد
    $setResult = $settings->set('test_setting', 'قيمة اختبار', 'string', 'إعداد للاختبار');
    if ($setResult['success']) {
        echo "✅ تم حفظ الإعداد بنجاح<br>";
        
        $getValue = $settings->get('test_setting');
        if ($getValue === 'قيمة اختبار') {
            echo "✅ تم استرجاع الإعداد بنجاح<br>";
        } else {
            echo "❌ فشل في استرجاع الإعداد<br>";
        }
    } else {
        echo "❌ فشل في حفظ الإعداد: " . $setResult['message'] . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في Settings: " . $e->getMessage() . "<br>";
}

try {
    // اختبار Customer
    echo "<h2>3. اختبار Customer</h2>";
    require_once __DIR__ . '/classes/Customer.php';
    $customer = new Customer();
    echo "✅ تم إنشاء Customer بنجاح<br>";
    
    // اختبار الحصول على رمز ذكي
    $smartCode = $customer->getSmartCustomerCode();
    if ($smartCode['success']) {
        echo "✅ تم الحصول على رمز عميل ذكي: " . $smartCode['number'] . "<br>";
    } else {
        echo "❌ فشل في الحصول على رمز ذكي: " . ($smartCode['error'] ?? 'خطأ غير محدد') . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في Customer: " . $e->getMessage() . "<br>";
}

echo "<h2>✅ انتهى الاختبار</h2>";
echo "<p><strong>النتيجة:</strong> تم إصلاح جميع الأخطاء بنجاح!</p>";
echo "<p><a href='dashboard.php'>العودة للوحة التحكم</a></p>";
?>
