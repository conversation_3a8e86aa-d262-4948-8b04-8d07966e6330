<?php
/**
 * SeaSystem - فئة إدارة المدفوعات المتقدمة
 * Advanced Payment Management Class
 */

require_once __DIR__ . '/../config/database.php';

class Payment {
    private $db;
    private $table_name = "payments";

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->createTablesIfNotExist();
    }

    /**
     * إنشاء الجداول إذا لم تكن موجودة
     */
    private function createTablesIfNotExist() {
        try {
            // جدول المدفوعات
            $sql = "CREATE TABLE IF NOT EXISTS payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                payment_number VARCHAR(50) UNIQUE NOT NULL,
                payment_type ENUM('received', 'paid') NOT NULL,
                payment_method ENUM('cash', 'bank_transfer', 'check', 'credit_card', 'online') NOT NULL,
                customer_id INT NULL,
                supplier_id INT NULL,
                amount DECIMAL(15,2) NOT NULL,
                payment_date DATE NOT NULL,
                due_date DATE NULL,
                reference_number VARCHAR(100),
                bank_account_id INT NULL,
                check_number VARCHAR(50) NULL,
                check_date DATE NULL,
                status ENUM('pending', 'completed', 'cancelled', 'bounced', 'scheduled') DEFAULT 'pending',
                notes TEXT,
                created_by INT NOT NULL,
                approved_by INT NULL,
                approved_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )";
            $this->db->exec($sql);

            // جدول ربط المدفوعات بالفواتير
            $sql = "CREATE TABLE IF NOT EXISTS payment_invoice_allocations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                payment_id INT NOT NULL,
                invoice_id INT NOT NULL,
                allocated_amount DECIMAL(15,2) NOT NULL,
                allocation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT
            )";
            $this->db->exec($sql);

            // جدول الحسابات البنكية
            $sql = "CREATE TABLE IF NOT EXISTS bank_accounts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                account_name VARCHAR(100) NOT NULL,
                account_number VARCHAR(50) NOT NULL,
                bank_name VARCHAR(100) NOT NULL,
                current_balance DECIMAL(15,2) DEFAULT 0.00,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            $this->db->exec($sql);

        } catch (Exception $e) {
            // تجاهل الأخطاء إذا كانت الجداول موجودة
        }
    }

    /**
     * إنشاء مدفوع جديد
     */
    public function create($payment_data) {
        try {
            $this->db->beginTransaction();

            // توليد رقم المدفوع التلقائي
            if (empty($payment_data['payment_number'])) {
                $payment_data['payment_number'] = $this->generatePaymentNumber($payment_data['payment_type']);
            }

            // إدراج المدفوع
            $sql = "INSERT INTO " . $this->table_name . "
                    (payment_number, payment_type, payment_method, customer_id, supplier_id,
                     amount, payment_date, due_date, reference_number, bank_account_id,
                     check_number, check_date, status, notes, created_by)
                    VALUES (:payment_number, :payment_type, :payment_method, :customer_id, :supplier_id,
                            :amount, :payment_date, :due_date, :reference_number, :bank_account_id,
                            :check_number, :check_date, :status, :notes, :created_by)";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':payment_number', $payment_data['payment_number']);
            $stmt->bindParam(':payment_type', $payment_data['payment_type']);
            $stmt->bindParam(':payment_method', $payment_data['payment_method']);
            $stmt->bindParam(':customer_id', $payment_data['customer_id']);
            $stmt->bindParam(':supplier_id', $payment_data['supplier_id']);
            $stmt->bindParam(':amount', $payment_data['amount']);
            $stmt->bindParam(':payment_date', $payment_data['payment_date']);
            $stmt->bindParam(':due_date', $payment_data['due_date']);
            $stmt->bindParam(':reference_number', $payment_data['reference_number']);
            $stmt->bindParam(':bank_account_id', $payment_data['bank_account_id']);
            $stmt->bindParam(':check_number', $payment_data['check_number']);
            $stmt->bindParam(':check_date', $payment_data['check_date']);
            $stmt->bindParam(':status', $payment_data['status']);
            $stmt->bindParam(':notes', $payment_data['notes']);
            $stmt->bindParam(':created_by', $payment_data['created_by']);

            if (!$stmt->execute()) {
                throw new Exception('فشل في إنشاء المدفوع');
            }

            $payment_id = $this->db->lastInsertId();

            // ربط المدفوع بالفواتير إذا تم تحديدها
            if (!empty($payment_data['invoice_allocations'])) {
                foreach ($payment_data['invoice_allocations'] as $allocation) {
                    $this->allocateToInvoice($payment_id, $allocation['invoice_id'], $allocation['amount']);
                }
            }

            // إنشاء قيد محاسبي تلقائي
            if ($payment_data['status'] == 'completed') {
                $this->createJournalEntry($payment_id);
            }

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'تم إنشاء المدفوع بنجاح',
                'payment_id' => $payment_id,
                'payment_number' => $payment_data['payment_number']
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'message' => 'خطأ في إنشاء المدفوع: ' . $e->getMessage()
            ];
        }
    }

    /**
     * تحديث المدفوع
     */
    public function update($payment_id, $payment_data) {
        try {
            $this->db->beginTransaction();

            // التحقق من وجود المدفوع
            $payment = $this->getById($payment_id);
            if (!$payment) {
                throw new Exception('المدفوع غير موجود');
            }

            // تحديث المدفوع
            $sql = "UPDATE " . $this->table_name . "
                    SET payment_type = :payment_type, payment_method = :payment_method,
                        customer_id = :customer_id, supplier_id = :supplier_id,
                        amount = :amount, payment_date = :payment_date, due_date = :due_date,
                        reference_number = :reference_number, bank_account_id = :bank_account_id,
                        check_number = :check_number, check_date = :check_date,
                        status = :status, notes = :notes, updated_at = NOW()
                    WHERE id = :payment_id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':payment_type', $payment_data['payment_type']);
            $stmt->bindParam(':payment_method', $payment_data['payment_method']);
            $stmt->bindParam(':customer_id', $payment_data['customer_id']);
            $stmt->bindParam(':supplier_id', $payment_data['supplier_id']);
            $stmt->bindParam(':amount', $payment_data['amount']);
            $stmt->bindParam(':payment_date', $payment_data['payment_date']);
            $stmt->bindParam(':due_date', $payment_data['due_date']);
            $stmt->bindParam(':reference_number', $payment_data['reference_number']);
            $stmt->bindParam(':bank_account_id', $payment_data['bank_account_id']);
            $stmt->bindParam(':check_number', $payment_data['check_number']);
            $stmt->bindParam(':check_date', $payment_data['check_date']);
            $stmt->bindParam(':status', $payment_data['status']);
            $stmt->bindParam(':notes', $payment_data['notes']);
            $stmt->bindParam(':payment_id', $payment_id);

            if (!$stmt->execute()) {
                throw new Exception('فشل في تحديث المدفوع');
            }

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'تم تحديث المدفوع بنجاح'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'message' => 'خطأ في تحديث المدفوع: ' . $e->getMessage()
            ];
        }
    }

    /**
     * حذف المدفوع
     */
    public function delete($payment_id) {
        try {
            $this->db->beginTransaction();

            $payment = $this->getById($payment_id);
            if (!$payment) {
                throw new Exception('المدفوع غير موجود');
            }

            if ($payment['status'] == 'completed') {
                throw new Exception('لا يمكن حذف مدفوع مكتمل');
            }

            // حذف ربط الفواتير
            $sql = "DELETE FROM payment_invoice_allocations WHERE payment_id = :payment_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':payment_id', $payment_id);
            $stmt->execute();

            // حذف المدفوع
            $sql = "DELETE FROM " . $this->table_name . " WHERE id = :payment_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':payment_id', $payment_id);

            if (!$stmt->execute()) {
                throw new Exception('فشل في حذف المدفوع');
            }

            $this->db->commit();

            return [
                'success' => true,
                'message' => 'تم حذف المدفوع بنجاح'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            return [
                'success' => false,
                'message' => 'خطأ في حذف المدفوع: ' . $e->getMessage()
            ];
        }
    }

    /**
     * الحصول على مدفوع واحد
     */
    public function getById($payment_id) {
        try {
            $sql = "SELECT p.*,
                           c.name as customer_name,
                           s.name as supplier_name,
                           ba.account_name as bank_account_name,
                           u1.full_name as created_by_name,
                           u2.full_name as approved_by_name
                    FROM " . $this->table_name . " p
                    LEFT JOIN customers c ON p.customer_id = c.id
                    LEFT JOIN suppliers s ON p.supplier_id = s.id
                    LEFT JOIN bank_accounts ba ON p.bank_account_id = ba.id
                    LEFT JOIN users u1 ON p.created_by = u1.id
                    LEFT JOIN users u2 ON p.approved_by = u2.id
                    WHERE p.id = :payment_id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':payment_id', $payment_id);
            $stmt->execute();

            return $stmt->fetch();
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * الحصول على جميع المدفوعات
     */
    public function getAll($filters = []) {
        try {
            $sql = "SELECT p.*,
                           c.name as customer_name,
                           s.name as supplier_name,
                           ba.account_name as bank_account_name,
                           u1.full_name as created_by_name
                    FROM " . $this->table_name . " p
                    LEFT JOIN customers c ON p.customer_id = c.id
                    LEFT JOIN suppliers s ON p.supplier_id = s.id
                    LEFT JOIN bank_accounts ba ON p.bank_account_id = ba.id
                    LEFT JOIN users u1 ON p.created_by = u1.id
                    WHERE 1=1";

            $params = [];

            // تطبيق المرشحات
            if (!empty($filters['payment_type'])) {
                $sql .= " AND p.payment_type = :payment_type";
                $params[':payment_type'] = $filters['payment_type'];
            }

            if (!empty($filters['payment_method'])) {
                $sql .= " AND p.payment_method = :payment_method";
                $params[':payment_method'] = $filters['payment_method'];
            }

            if (!empty($filters['status'])) {
                $sql .= " AND p.status = :status";
                $params[':status'] = $filters['status'];
            }

            if (!empty($filters['date_from'])) {
                $sql .= " AND p.payment_date >= :date_from";
                $params[':date_from'] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $sql .= " AND p.payment_date <= :date_to";
                $params[':date_to'] = $filters['date_to'];
            }

            if (!empty($filters['customer_id'])) {
                $sql .= " AND p.customer_id = :customer_id";
                $params[':customer_id'] = $filters['customer_id'];
            }

            if (!empty($filters['supplier_id'])) {
                $sql .= " AND p.supplier_id = :supplier_id";
                $params[':supplier_id'] = $filters['supplier_id'];
            }

            $sql .= " ORDER BY p.payment_date DESC, p.id DESC";

            if (!empty($filters['limit'])) {
                $sql .= " LIMIT " . intval($filters['limit']);
            }

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * البحث في المدفوعات
     */
    public function search($keyword, $filters = []) {
        try {
            $sql = "SELECT p.*,
                           c.name as customer_name,
                           s.name as supplier_name,
                           ba.account_name as bank_account_name,
                           u1.full_name as created_by_name
                    FROM " . $this->table_name . " p
                    LEFT JOIN customers c ON p.customer_id = c.id
                    LEFT JOIN suppliers s ON p.supplier_id = s.id
                    LEFT JOIN bank_accounts ba ON p.bank_account_id = ba.id
                    LEFT JOIN users u1 ON p.created_by = u1.id
                    WHERE (p.payment_number LIKE :keyword
                           OR p.reference_number LIKE :keyword
                           OR p.notes LIKE :keyword
                           OR c.name LIKE :keyword
                           OR s.name LIKE :keyword)";

            $params = [':keyword' => "%$keyword%"];

            // تطبيق المرشحات الإضافية
            if (!empty($filters['payment_type'])) {
                $sql .= " AND p.payment_type = :payment_type";
                $params[':payment_type'] = $filters['payment_type'];
            }

            if (!empty($filters['status'])) {
                $sql .= " AND p.status = :status";
                $params[':status'] = $filters['status'];
            }

            $sql .= " ORDER BY p.payment_date DESC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * إحصائيات المدفوعات
     */
    public function getStatistics($filters = []) {
        try {
            $sql = "SELECT
                        COUNT(*) as total_payments,
                        COUNT(CASE WHEN payment_type = 'received' THEN 1 END) as total_received_count,
                        COUNT(CASE WHEN payment_type = 'paid' THEN 1 END) as total_paid_count,
                        SUM(CASE WHEN payment_type = 'received' AND status = 'completed' THEN amount ELSE 0 END) as total_received_amount,
                        SUM(CASE WHEN payment_type = 'paid' AND status = 'completed' THEN amount ELSE 0 END) as total_paid_amount,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_payments,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_payments,
                        AVG(CASE WHEN status = 'completed' THEN amount ELSE NULL END) as average_amount
                    FROM " . $this->table_name . "
                    WHERE 1=1";

            $params = [];

            // تطبيق المرشحات
            if (!empty($filters['date_from'])) {
                $sql .= " AND payment_date >= :date_from";
                $params[':date_from'] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $sql .= " AND payment_date <= :date_to";
                $params[':date_to'] = $filters['date_to'];
            }

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            $result = $stmt->fetch();

            // حساب صافي التدفق النقدي
            $result['net_cash_flow'] = $result['total_received_amount'] - $result['total_paid_amount'];

            return $result;
        } catch (Exception $e) {
            return [
                'total_payments' => 0,
                'total_received_count' => 0,
                'total_paid_count' => 0,
                'total_received_amount' => 0,
                'total_paid_amount' => 0,
                'pending_payments' => 0,
                'completed_payments' => 0,
                'average_amount' => 0,
                'net_cash_flow' => 0
            ];
        }
    }

    /**
     * ربط المدفوع بفاتورة
     */
    public function allocateToInvoice($payment_id, $invoice_id, $amount, $notes = '') {
        try {
            $sql = "INSERT INTO payment_invoice_allocations
                    (payment_id, invoice_id, allocated_amount, notes)
                    VALUES (:payment_id, :invoice_id, :amount, :notes)";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':payment_id', $payment_id);
            $stmt->bindParam(':invoice_id', $invoice_id);
            $stmt->bindParam(':amount', $amount);
            $stmt->bindParam(':notes', $notes);

            if ($stmt->execute()) {
                // تحديث حالة دفع الفاتورة
                $this->updateInvoicePaymentStatus($invoice_id);
                return true;
            }

            return false;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * تحديث حالة دفع الفاتورة
     */
    private function updateInvoicePaymentStatus($invoice_id) {
        try {
            // حساب إجمالي المدفوع للفاتورة
            $sql = "SELECT SUM(allocated_amount) as total_paid
                    FROM payment_invoice_allocations
                    WHERE invoice_id = :invoice_id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':invoice_id', $invoice_id);
            $stmt->execute();

            $result = $stmt->fetch();
            $total_paid = $result['total_paid'] ?? 0;

            // الحصول على إجمالي الفاتورة
            $sql = "SELECT total_amount FROM invoices WHERE id = :invoice_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':invoice_id', $invoice_id);
            $stmt->execute();

            $invoice = $stmt->fetch();
            if (!$invoice) return;

            $total_amount = $invoice['total_amount'];

            // تحديد حالة الدفع
            $payment_status = 'unpaid';
            if ($total_paid > 0) {
                if ($total_paid >= $total_amount) {
                    $payment_status = 'paid';
                } else {
                    $payment_status = 'partial';
                }
            }

            // تحديث الفاتورة
            $sql = "UPDATE invoices
                    SET paid_amount = :paid_amount,
                        payment_status = :payment_status,
                        last_payment_date = CURDATE()
                    WHERE id = :invoice_id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':paid_amount', $total_paid);
            $stmt->bindParam(':payment_status', $payment_status);
            $stmt->bindParam(':invoice_id', $invoice_id);
            $stmt->execute();

        } catch (Exception $e) {
            // تجاهل الأخطاء
        }
    }

    /**
     * توليد رقم مدفوع تلقائي
     */
    public function generatePaymentNumber($payment_type) {
        try {
            $year = date('Y');
            $month = date('m');
            $prefix = $payment_type == 'received' ? 'REC' : 'PAY';

            // البحث عن آخر رقم
            $sql = "SELECT payment_number FROM " . $this->table_name . "
                    WHERE payment_type = :payment_type
                    AND payment_number LIKE :pattern
                    ORDER BY id DESC LIMIT 1";

            $pattern = $prefix . $year . $month . '%';
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':payment_type', $payment_type);
            $stmt->bindParam(':pattern', $pattern);
            $stmt->execute();

            $result = $stmt->fetch();

            if ($result) {
                // استخراج الرقم التسلسلي
                $last_number = substr($result['payment_number'], -3);
                $next_number = str_pad(intval($last_number) + 1, 3, '0', STR_PAD_LEFT);
            } else {
                $next_number = '001';
            }

            return $prefix . $year . $month . $next_number;

        } catch (Exception $e) {
            return ($payment_type == 'received' ? 'REC' : 'PAY') . date('Ymd') . '001';
        }
    }

    /**
     * إنشاء قيد محاسبي من المدفوع
     */
    private function createJournalEntry($payment_id) {
        try {
            require_once __DIR__ . '/JournalEntry.php';
            $journal = new JournalEntry();

            $payment = $this->getById($payment_id);
            if (!$payment) return false;

            // بيانات القيد الرئيسي
            $entry_data = [
                'entry_date' => $payment['payment_date'],
                'reference_type' => 'payment',
                'reference_id' => $payment_id,
                'description' => 'قيد ' . ($payment['payment_type'] == 'received' ? 'مقبوض' : 'مدفوع') . ' رقم ' . $payment['payment_number'],
                'status' => 'posted',
                'notes' => 'قيد تلقائي من المدفوع',
                'created_by' => $payment['created_by']
            ];

            // تفاصيل القيد
            $details_data = [];

            if ($payment['payment_type'] == 'received') {
                // مقبوض
                // مدين: النقدية أو البنك
                $cash_account_id = $payment['payment_method'] == 'cash' ? 3 : 4; // النقدية أو البنك
                $details_data[] = [
                    'account_id' => $cash_account_id,
                    'debit_amount' => $payment['amount'],
                    'credit_amount' => 0,
                    'description' => 'مقبوض من ' . ($payment['customer_name'] ?? 'عميل')
                ];

                // دائن: العملاء
                $details_data[] = [
                    'account_id' => 5, // حساب العملاء
                    'debit_amount' => 0,
                    'credit_amount' => $payment['amount'],
                    'description' => 'تحصيل من ' . ($payment['customer_name'] ?? 'عميل')
                ];
            } else {
                // مدفوع
                // مدين: الموردين
                $details_data[] = [
                    'account_id' => 7, // حساب الموردين
                    'debit_amount' => $payment['amount'],
                    'credit_amount' => 0,
                    'description' => 'دفع لـ ' . ($payment['supplier_name'] ?? 'مورد')
                ];

                // دائن: النقدية أو البنك
                $cash_account_id = $payment['payment_method'] == 'cash' ? 3 : 4;
                $details_data[] = [
                    'account_id' => $cash_account_id,
                    'debit_amount' => 0,
                    'credit_amount' => $payment['amount'],
                    'description' => 'مدفوع لـ ' . ($payment['supplier_name'] ?? 'مورد')
                ];
            }

            return $journal->create($entry_data, $details_data);

        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * الحصول على الفواتير غير المدفوعة للعميل/المورد
     */
    public function getUnpaidInvoices($customer_id = null, $supplier_id = null) {
        try {
            $sql = "SELECT id, invoice_number, total_amount, paid_amount,
                           (total_amount - paid_amount) as remaining_amount,
                           invoice_date, due_date
                    FROM invoices
                    WHERE payment_status IN ('unpaid', 'partial')";

            $params = [];

            if ($customer_id) {
                $sql .= " AND customer_id = :customer_id";
                $params[':customer_id'] = $customer_id;
            }

            if ($supplier_id) {
                $sql .= " AND supplier_id = :supplier_id";
                $params[':supplier_id'] = $supplier_id;
            }

            $sql .= " ORDER BY invoice_date ASC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على تقرير التدفق النقدي
     */
    public function getCashFlowReport($filters = []) {
        try {
            $sql = "SELECT p.*,
                           c.name as customer_name,
                           s.name as supplier_name,
                           coa.account_name,
                           u.full_name as created_by_name
                    FROM " . $this->table_name . " p
                    LEFT JOIN customers c ON p.customer_id = c.id
                    LEFT JOIN suppliers s ON p.supplier_id = s.id
                    LEFT JOIN chart_of_accounts coa ON p.account_id = coa.id
                    LEFT JOIN users u ON p.created_by = u.id
                    WHERE p.status = 'completed'";

            $params = [];

            // فلترة بالتاريخ
            if (!empty($filters['date_from'])) {
                $sql .= " AND p.payment_date >= :date_from";
                $params[':date_from'] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $sql .= " AND p.payment_date <= :date_to";
                $params[':date_to'] = $filters['date_to'];
            }

            // فلترة بالحساب
            if (!empty($filters['account_id'])) {
                $sql .= " AND p.account_id = :account_id";
                $params[':account_id'] = $filters['account_id'];
            }

            // فلترة بنوع الدفع
            if (!empty($filters['payment_type'])) {
                $sql .= " AND p.payment_type = :payment_type";
                $params[':payment_type'] = $filters['payment_type'];
            }

            $sql .= " ORDER BY p.payment_date DESC, p.id DESC";

            $stmt = $this->db->prepare($sql);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }
}
?>
