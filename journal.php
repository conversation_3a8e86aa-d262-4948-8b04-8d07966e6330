<?php
/**
 * SeaSystem - صفحة دفتر اليومية
 * Journal Entries Page
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/includes/sidebar.php';
require_once __DIR__ . '/classes/JournalEntry.php';
require_once __DIR__ . '/classes/Account.php';

// التأكد من تسجيل الدخول
requireLogin();

$journal = new JournalEntry();
$account = new Account();
$current_user = getCurrentUser();

// معالجة العمليات
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'post_entry':
                $result = $journal->postEntry($_POST['entry_id'], $current_user['id']);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;

            case 'delete_entry':
                $result = $journal->delete($_POST['entry_id']);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;
        }
    }
}

// الحصول على المرشحات
$filters = [];
if (!empty($_GET['status'])) {
    $filters['status'] = $_GET['status'];
}
if (!empty($_GET['reference_type'])) {
    $filters['reference_type'] = $_GET['reference_type'];
}
if (!empty($_GET['date_from'])) {
    $filters['date_from'] = $_GET['date_from'];
}
if (!empty($_GET['date_to'])) {
    $filters['date_to'] = $_GET['date_to'];
}

// الحصول على قائمة القيود
$entries = $journal->getAll($filters);

// البحث
$search_term = $_GET['search'] ?? '';
if (!empty($search_term)) {
    $entries = $journal->search($search_term, $filters);
}

// إحصائيات القيود
$stats = $journal->getStatistics($filters);

// قوائم المرشحات
$entry_statuses = [
    'draft' => 'مسودة',
    'posted' => 'مرحل'
];

$reference_types = [
    'invoice' => 'فاتورة',
    'payment' => 'دفعة',
    'adjustment' => 'تسوية',
    'opening_balance' => 'رصيد ابتدائي'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دفتر اليومية - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <link href="assets/css/sidebar-only.css" rel="stylesheet">
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي الموحد -->
            <?php renderSidebar('journal.php'); ?>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="page-title">
                                <i class="fas fa-book me-2"></i>دفتر اليومية
                            </h1>
                            <p class="page-subtitle">إدارة القيود المحاسبية والترحيل</p>
                        </div>
                        <div class="col-auto">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="createInvoice('sales')">
                                    <i class="fas fa-file-invoice me-2"></i>فاتورة مبيعات
                                </button>
                                <button type="button" class="btn btn-outline-info btn-sm" onclick="createInvoice('purchase')">
                                    <i class="fas fa-file-invoice me-2"></i>فاتورة مشتريات
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="createJournalEntry()">
                                    <i class="fas fa-plus me-2"></i>إنشاء قيد محاسبي
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسائل -->
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon primary">
                                    <i class="fas fa-book"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format((int)($stats['total_entries'] ?? 0)); ?></h3>
                                    <p class="text-muted mb-0">إجمالي القيود</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon warning">
                                    <i class="fas fa-edit"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format((int)($stats['draft_entries'] ?? 0)); ?></h3>
                                    <p class="text-muted mb-0">قيود مسودة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon success">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format((int)($stats['posted_entries'] ?? 0)); ?></h3>
                                    <p class="text-muted mb-0">قيود مرحلة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon info">
                                    <i class="fas fa-calculator"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format((float)($stats['total_posted_amount'] ?? 0), 0); ?></h3>
                                    <p class="text-muted mb-0">إجمالي المبالغ المرحلة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- شريط البحث والفلترة -->
                <div class="card mb-4">
                    <div class="card-body">
                        <!-- البحث الديناميكي -->
                        <div class="row g-3 mb-3">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="journalSearch"
                                           placeholder="البحث السريع في قيود اليومية..."
                                           value="<?php echo htmlspecialchars($search_term); ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-outline-secondary w-100" id="clearJournalSearch">
                                    <i class="fas fa-times me-2"></i>مسح البحث
                                </button>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-outline-info w-100" id="toggleJournalFilters">
                                    <i class="fas fa-filter me-2"></i>فلاتر متقدمة
                                </button>
                            </div>
                        </div>

                        <!-- الفلاتر المتقدمة (مخفية افتراضياً) -->
                        <div id="advancedJournalFilters" class="border-top pt-3" style="display: none;">
                            <form method="GET" class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">حالة القيد</label>
                                    <select class="form-select" name="status">
                                        <option value="">جميع الحالات</option>
                                        <?php foreach ($entry_statuses as $status => $status_name): ?>
                                            <option value="<?php echo $status; ?>" <?php echo ($_GET['status'] ?? '') == $status ? 'selected' : ''; ?>>
                                                <?php echo $status_name; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">نوع المرجع</label>
                                    <select class="form-select" name="reference_type">
                                        <option value="">جميع الأنواع</option>
                                        <?php foreach ($reference_types as $type => $type_name): ?>
                                            <option value="<?php echo $type; ?>" <?php echo ($_GET['reference_type'] ?? '') == $type ? 'selected' : ''; ?>>
                                                <?php echo $type_name; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" name="date_from"
                                           value="<?php echo $_GET['date_from'] ?? ''; ?>">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" name="date_to"
                                           value="<?php echo $_GET['date_to'] ?? ''; ?>">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="submit" class="btn btn-primary w-100 d-block">
                                        <i class="fas fa-search me-2"></i>تطبيق الفلاتر
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- جدول القيود -->
                <div class="table-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-book me-2"></i>قائمة القيود المحاسبية (<?php echo count($entries); ?>)
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="journalTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم القيد</th>
                                        <th>التاريخ</th>
                                        <th>النوع</th>
                                        <th>الوصف</th>
                                        <th>المدين</th>
                                        <th>الدائن</th>
                                        <th>الحالة</th>
                                        <th>المنشئ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($entries)): ?>
                                        <tr>
                                            <td colspan="9" class="text-center py-4">
                                                <i class="fas fa-book fa-2x text-muted mb-2"></i>
                                                <p class="text-muted mb-0">لا توجد قيود محاسبية حتى الآن</p>
                                                <div class="mt-3">
                                                    <button type="button" class="btn btn-success me-2" onclick="createInvoice('sales')">
                                                        <i class="fas fa-file-invoice me-2"></i>إنشاء فاتورة مبيعات
                                                    </button>
                                                    <button type="button" class="btn btn-info me-2" onclick="createInvoice('purchase')">
                                                        <i class="fas fa-file-invoice me-2"></i>إنشاء فاتورة مشتريات
                                                    </button>
                                                    <button type="button" class="btn btn-primary" onclick="createJournalEntry()">
                                                        <i class="fas fa-plus me-2"></i>إنشاء قيد محاسبي
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($entries as $entry): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($entry['entry_number']); ?></strong>
                                                </td>
                                                <td><?php echo date('Y-m-d', strtotime($entry['entry_date'])); ?></td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <?php echo $reference_types[$entry['reference_type']] ?? $entry['reference_type']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlspecialchars($entry['description']); ?>">
                                                        <?php echo htmlspecialchars($entry['description']); ?>
                                                    </div>
                                                </td>
                                                <td class="text-success">
                                                    <?php echo number_format((float)($entry['total_debit'] ?? 0), 2) . ' ' . CURRENCY_SYMBOL; ?>
                                                </td>
                                                <td class="text-danger">
                                                    <?php echo number_format((float)($entry['total_credit'] ?? 0), 2) . ' ' . CURRENCY_SYMBOL; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status_class = $entry['status'] == 'posted' ? 'bg-success' : 'bg-warning';
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>">
                                                        <?php echo $entry_statuses[$entry['status']]; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <small><?php echo htmlspecialchars($entry['created_by_name']); ?></small>
                                                </td>
                                                <td>
                                                    <div class="d-flex gap-2">
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                                    onclick="viewEntry(<?php echo $entry['id']; ?>)">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">عرض</small>
                                                        </div>

                                                        <?php if ($entry['status'] == 'draft'): ?>
                                                            <div class="text-center">
                                                                <button type="button" class="btn btn-outline-success btn-sm"
                                                                        onclick="editEntry(<?php echo $entry['id']; ?>)">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                                <small class="d-block text-muted mt-1">تعديل</small>
                                                            </div>
                                                            <div class="text-center">
                                                                <button type="button" class="btn btn-outline-warning btn-sm"
                                                                        onclick="postEntry(<?php echo $entry['id']; ?>)">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                                <small class="d-block text-muted mt-1">ترحيل</small>
                                                            </div>
                                                            <div class="text-center">
                                                                <button type="button" class="btn btn-outline-danger btn-sm"
                                                                        onclick="deleteEntry(<?php echo $entry['id']; ?>)">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                                <small class="d-block text-muted mt-1">حذف</small>
                                                            </div>
                                                        <?php endif; ?>

                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-info btn-sm"
                                                                    onclick="printEntry(<?php echo $entry['id']; ?>)">
                                                                <i class="fas fa-print"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">طباعة</small>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        // إنشاء قيد محاسبي جديد
        function createJournalEntry() {
            window.location.href = 'journal_entry_create.php';
        }

        // إنشاء فاتورة جديدة
        function createInvoice(type) {
            window.location.href = `invoice_create.php?type=${type}`;
        }

        // عرض القيد
        function viewEntry(entryId) {
            alert('سيتم إضافة صفحة عرض القيد قريباً');
            // window.location.href = `journal_view.php?id=${entryId}`;
        }

        // تعديل القيد
        function editEntry(entryId) {
            alert('سيتم إضافة صفحة تعديل القيد قريباً');
            // window.location.href = `journal_edit.php?id=${entryId}`;
        }

        // ترحيل القيد
        function postEntry(entryId) {
            if (confirm('هل أنت متأكد من ترحيل هذا القيد؟\nلن تتمكن من تعديله بعد الترحيل.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="post_entry">
                    <input type="hidden" name="entry_id" value="${entryId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // حذف القيد
        function deleteEntry(entryId) {
            if (confirm('هل أنت متأكد من حذف هذا القيد؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_entry">
                    <input type="hidden" name="entry_id" value="${entryId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // طباعة القيد
        function printEntry(entryId) {
            alert('سيتم إضافة وظيفة طباعة القيد قريباً');
            // window.open(`journal_print.php?id=${entryId}`, '_blank');
        }

        // إظهار/إخفاء الفلاتر المتقدمة
        document.getElementById('toggleJournalFilters').addEventListener('click', function() {
            const filtersDiv = document.getElementById('advancedJournalFilters');
            const isVisible = filtersDiv.style.display !== 'none';

            if (isVisible) {
                filtersDiv.style.display = 'none';
                this.innerHTML = '<i class="fas fa-filter me-2"></i>فلاتر متقدمة';
            } else {
                filtersDiv.style.display = 'block';
                this.innerHTML = '<i class="fas fa-times me-2"></i>إخفاء الفلاتر';
            }
        });

        // البحث الديناميكي في قيود اليومية
        const journalSearchInput = document.getElementById('journalSearch');
        const clearJournalSearchBtn = document.getElementById('clearJournalSearch');
        const journalTableBody = document.querySelector('#journalTable tbody');
        let searchTimeout;

        // البحث عند الكتابة
        journalSearchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const searchTerm = this.value.trim();

            searchTimeout = setTimeout(() => {
                performJournalSearch(searchTerm);
            }, 300); // انتظار 300 ملي ثانية بعد انتهاء الكتابة
        });

        // مسح البحث
        clearJournalSearchBtn.addEventListener('click', function() {
            journalSearchInput.value = '';
            performJournalSearch('');
        });

        // تنفيذ البحث
        function performJournalSearch(searchTerm) {
            // إظهار مؤشر التحميل
            journalTableBody.innerHTML = '<tr><td colspan="8" class="text-center py-4"><i class="fas fa-spinner fa-spin fa-2x text-muted"></i><p class="text-muted mt-2">جاري البحث...</p></td></tr>';

            // إرسال طلب AJAX
            fetch(`ajax_search_journal.php?search=${encodeURIComponent(searchTerm)}`)
                .then(response => response.text())
                .then(data => {
                    journalTableBody.innerHTML = data;
                })
                .catch(error => {
                    console.error('خطأ في البحث:', error);
                    journalTableBody.innerHTML = '<tr><td colspan="8" class="text-center py-4 text-danger"><i class="fas fa-exclamation-triangle fa-2x mb-2"></i><p>حدث خطأ في البحث</p></td></tr>';
                });
        }

        // تحديث الوقت والتاريخ
        function updateDateTime() {
            const now = new Date();
            const timeElement = document.getElementById('current-time');
            const dateElement = document.getElementById('current-date');

            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString('ar-SA', {
                    hour: '2-digit', minute: '2-digit'
                });
            }
            if (dateElement) {
                dateElement.textContent = now.toLocaleDateString('ar-SA');
            }
        }
        setInterval(updateDateTime, 60000);
        updateDateTime();
    </script>
</body>
</html>
