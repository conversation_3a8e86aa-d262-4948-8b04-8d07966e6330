<?php
/**
 * SeaSystem - إدارة المستخدمين
 * Users Management Page
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/includes/sidebar.php';
require_once __DIR__ . '/classes/User.php';
require_once __DIR__ . '/classes/Permission.php';

// التأكد من تسجيل الدخول وصلاحيات المدير
requireLogin();
if (!hasPermission('admin')) {
    header('Location: dashboard.php?error=no_permission');
    exit();
}

$user = new User();
$permission = new Permission();
$current_user = getCurrentUser();

// معالجة العمليات
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $data = [
                    'username' => $_POST['username'],
                    'email' => $_POST['email'],
                    'password' => $_POST['password'],
                    'full_name' => $_POST['full_name'],
                    'role' => $_POST['role']
                ];

                $result = $user->create($data);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;

            case 'edit':
                $data = [
                    'username' => $_POST['username'],
                    'email' => $_POST['email'],
                    'full_name' => $_POST['full_name'],
                    'role' => $_POST['role']
                ];

                if (!empty($_POST['password'])) {
                    $data['password'] = $_POST['password'];
                }

                $result = $user->update($_POST['user_id'], $data);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;

            case 'delete':
                $result = $user->delete($_POST['user_id']);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;

            case 'toggle_status':
                $result = $user->toggleStatus($_POST['user_id']);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'danger';
                break;

            case 'update_permissions':
                try {
                    $userId = $_POST['user_id'];
                    $permissions = $_POST['permissions'] ?? [];

                    $result = $permission->updateUserPermissions($userId, $permissions, $current_user['id']);
                    $message = $result['message'];
                    $message_type = 'success';
                } catch (Exception $e) {
                    $message = $e->getMessage();
                    $message_type = 'danger';
                }
                break;
        }
    }
}

// جلب جميع المستخدمين
$users = $user->getAll();

// جلب جميع الصلاحيات مجمعة
$allPermissions = $permission->getAllPermissionsGrouped();
$groupNames = $permission->getGroupNames();

// أنواع المستخدمين
$user_roles = [
    'admin' => 'مدير النظام',
    'financial_manager' => 'مدير مالي',
    'accountant' => 'محاسب',
    'inventory_manager' => 'مدير مخزون',
    'sales_manager' => 'مدير مبيعات',
    'purchase_manager' => 'مدير مشتريات',
    'cashier' => 'أمين صندوق',
    'data_entry' => 'مدخل بيانات',
    'viewer' => 'مستعرض فقط'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <link href="assets/css/sidebar-only.css" rel="stylesheet">

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }

        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي الموحد -->
            <?php renderSidebar('users.php'); ?>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="page-title">
                                <i class="fas fa-user-cog me-2"></i>إدارة المستخدمين
                            </h1>
                            <p class="page-subtitle">إدارة مستخدمي النظام والصلاحيات</p>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                <i class="fas fa-plus me-2"></i>إضافة مستخدم جديد
                            </button>
                        </div>
                    </div>
                </div>

                <!-- رسائل النظام -->
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- جدول المستخدمين -->
                <div class="table-card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-users me-2"></i>قائمة المستخدمين (<?php echo count($users); ?>)
                                </h5>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex">
                                    <input type="text" class="form-control me-2" id="userSearch"
                                           placeholder="البحث في المستخدمين...">
                                    <button type="button" class="btn btn-light" id="clearUserSearch">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="usersTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم المستخدم</th>
                                        <th>الاسم الكامل</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الدور</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <?php if (empty($users)): ?>
                                        <tr>
                                            <td colspan="7" class="text-center py-4">
                                                <i class="fas fa-users fa-2x text-muted mb-2"></i>
                                                <p class="text-muted mb-0">لا يوجد مستخدمين</p>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($users as $usr): ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($usr['username']); ?></strong></td>
                                                <td><?php echo htmlspecialchars($usr['full_name']); ?></td>
                                                <td><?php echo htmlspecialchars($usr['email']); ?></td>
                                                <td>
                                                    <?php
                                                    $role_class = '';
                                                    switch ($usr['role']) {
                                                        case 'admin':
                                                            $role_class = 'bg-danger';
                                                            break;
                                                        case 'financial_manager':
                                                            $role_class = 'bg-primary';
                                                            break;
                                                        case 'accountant':
                                                            $role_class = 'bg-success';
                                                            break;
                                                        case 'inventory_manager':
                                                            $role_class = 'bg-warning';
                                                            break;
                                                        case 'sales_manager':
                                                            $role_class = 'bg-info';
                                                            break;
                                                        case 'purchase_manager':
                                                            $role_class = 'bg-dark';
                                                            break;
                                                        case 'cashier':
                                                            $role_class = 'bg-secondary';
                                                            break;
                                                        case 'data_entry':
                                                            $role_class = 'bg-light text-dark';
                                                            break;
                                                        case 'viewer':
                                                            $role_class = 'bg-muted';
                                                            break;
                                                        default:
                                                            $role_class = 'bg-secondary';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $role_class; ?>">
                                                        <?php echo $user_roles[$usr['role']]; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($usr['is_active']): ?>
                                                        <span class="badge bg-success">نشط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">غير نشط</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('Y-m-d', strtotime($usr['created_at'])); ?></td>
                                                <td>
                                                    <div class="d-flex gap-2">
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                                    onclick="editUser(<?php echo htmlspecialchars(json_encode($usr)); ?>)">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">تعديل</small>
                                                        </div>
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-info btn-sm"
                                                                    onclick="managePermissions(<?php echo $usr['id']; ?>, '<?php echo htmlspecialchars($usr['full_name']); ?>')">
                                                                <i class="fas fa-key"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">الصلاحيات</small>
                                                        </div>
                                                        <?php if ($usr['id'] != $current_user['id']): ?>
                                                            <div class="text-center">
                                                                <button type="button" class="btn btn-outline-warning btn-sm"
                                                                        onclick="toggleUserStatus(<?php echo $usr['id']; ?>, <?php echo $usr['is_active'] ? 'false' : 'true'; ?>)">
                                                                    <i class="fas fa-<?php echo $usr['is_active'] ? 'ban' : 'check'; ?>"></i>
                                                                </button>
                                                                <small class="d-block text-muted mt-1"><?php echo $usr['is_active'] ? 'تعطيل' : 'تفعيل'; ?></small>
                                                            </div>
                                                            <div class="text-center">
                                                                <button type="button" class="btn btn-outline-danger btn-sm"
                                                                        onclick="deleteUser(<?php echo $usr['id']; ?>, '<?php echo htmlspecialchars($usr['username']); ?>')">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                                <small class="d-block text-muted mt-1">حذف</small>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة مستخدم -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>إضافة مستخدم جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">

                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="username" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" name="email" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" name="password" required minlength="6">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="full_name" required>
                            </div>
                            <div class="col-12">
                                <label class="form-label">دور المستخدم <span class="text-danger">*</span></label>
                                <select class="form-select" name="role" required>
                                    <option value="">اختر الدور</option>
                                    <?php foreach ($user_roles as $role => $role_name): ?>
                                        <option value="<?php echo $role; ?>"><?php echo $role_name; ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">
                                    <strong>مدير النظام:</strong> صلاحيات كاملة لجميع أجزاء النظام<br>
                                    <strong>مدير مالي:</strong> إدارة الشؤون المالية والتقارير<br>
                                    <strong>محاسب:</strong> إدارة الحسابات والقيود المحاسبية<br>
                                    <strong>مدير مخزون:</strong> إدارة المخزون والمنتجات<br>
                                    <strong>مدير مبيعات:</strong> إدارة فواتير المبيعات والعملاء<br>
                                    <strong>مدير مشتريات:</strong> إدارة فواتير المشتريات والموردين<br>
                                    <strong>أمين صندوق:</strong> إدارة المدفوعات والمقبوضات<br>
                                    <strong>مدخل بيانات:</strong> إدخال وتعديل البيانات فقط<br>
                                    <strong>مستعرض فقط:</strong> عرض البيانات والتقارير بدون تعديل
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ المستخدم
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل مستخدم -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>تعديل المستخدم
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="user_id" id="edit_user_id">

                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="username" id="edit_username" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" name="email" id="edit_email" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">كلمة المرور الجديدة</label>
                                <input type="password" class="form-control" name="password" minlength="6">
                                <div class="form-text">اتركها فارغة إذا كنت لا تريد تغييرها</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="full_name" id="edit_full_name" required>
                            </div>
                            <div class="col-12">
                                <label class="form-label">دور المستخدم <span class="text-danger">*</span></label>
                                <select class="form-select" name="role" id="edit_role" required>
                                    <?php foreach ($user_roles as $role => $role_name): ?>
                                        <option value="<?php echo $role; ?>"><?php echo $role_name; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تعديل مستخدم
        function editUser(userData) {
            document.getElementById('edit_user_id').value = userData.id;
            document.getElementById('edit_username').value = userData.username;
            document.getElementById('edit_email').value = userData.email;
            document.getElementById('edit_full_name').value = userData.full_name;
            document.getElementById('edit_role').value = userData.role;

            new bootstrap.Modal(document.getElementById('editUserModal')).show();
        }

        // تغيير حالة المستخدم
        function toggleUserStatus(userId, newStatus) {
            const action = newStatus === 'true' ? 'تفعيل' : 'تعطيل';
            if (confirm(`هل أنت متأكد من ${action} هذا المستخدم؟`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="toggle_status">
                    <input type="hidden" name="user_id" value="${userId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // حذف مستخدم
        function deleteUser(userId, username) {
            if (confirm(`هل أنت متأكد من حذف المستخدم "${username}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="user_id" value="${userId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // البحث الديناميكي
        let searchTimeout;
        const searchInput = document.getElementById('userSearch');
        const clearButton = document.getElementById('clearUserSearch');
        const tableBody = document.querySelector('#usersTable tbody');

        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const searchTerm = this.value.trim();

                // تأخير البحث لمدة 300 ملي ثانية
                searchTimeout = setTimeout(() => {
                    performSearch(searchTerm);
                }, 300);
            });
        }

        if (clearButton) {
            clearButton.addEventListener('click', function() {
                searchInput.value = '';
                performSearch('');
            });
        }

        function performSearch(searchTerm) {
            // إظهار مؤشر التحميل
            tableBody.innerHTML = '<tr><td colspan="7" class="text-center py-4"><i class="fas fa-spinner fa-spin fa-2x text-muted"></i><p class="text-muted mt-2">جاري البحث...</p></td></tr>';

            // إرسال طلب AJAX
            fetch(`ajax_search_users.php?search=${encodeURIComponent(searchTerm)}`)
                .then(response => response.text())
                .then(data => {
                    tableBody.innerHTML = data;
                })
                .catch(error => {
                    console.error('خطأ في البحث:', error);
                    tableBody.innerHTML = '<tr><td colspan="7" class="text-center py-4 text-danger"><i class="fas fa-exclamation-triangle fa-2x mb-2"></i><p>حدث خطأ في البحث</p></td></tr>';
                });
        }

        // إدارة الصلاحيات
        function managePermissions(userId, userName) {
            // تحديث عنوان النافذة
            document.getElementById('permissionsModalLabel').textContent = 'إدارة صلاحيات: ' + userName;
            document.getElementById('permissions_user_id').value = userId;

            // جلب صلاحيات المستخدم الحالية
            fetch('get_user_permissions.php?user_id=' + userId)
                .then(response => response.json())
                .then(data => {
                    // إلغاء تحديد جميع الصلاحيات
                    document.querySelectorAll('#permissionsModal input[type="checkbox"]').forEach(checkbox => {
                        checkbox.checked = false;
                    });

                    // تحديد الصلاحيات الحالية
                    if (data.permissions) {
                        data.permissions.forEach(permission => {
                            const checkbox = document.querySelector(`input[name="permissions[]"][value="${permission}"]`);
                            if (checkbox) {
                                checkbox.checked = true;
                            }
                        });
                    }

                    // عرض النافذة
                    new bootstrap.Modal(document.getElementById('permissionsModal')).show();
                })
                .catch(error => {
                    console.error('خطأ في جلب الصلاحيات:', error);
                    alert('حدث خطأ في جلب صلاحيات المستخدم');
                });
        }

        // تحديد/إلغاء تحديد جميع صلاحيات مجموعة
        function toggleGroupPermissions(groupCheckbox, groupName) {
            const groupCheckboxes = document.querySelectorAll(`input[data-group="${groupName}"]`);
            groupCheckboxes.forEach(checkbox => {
                checkbox.checked = groupCheckbox.checked;
            });
        }
    </script>

    <!-- نافذة إدارة الصلاحيات -->
    <div class="modal fade" id="permissionsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="permissionsModalLabel">
                        <i class="fas fa-key me-2"></i>إدارة الصلاحيات
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update_permissions">
                        <input type="hidden" name="user_id" id="permissions_user_id">

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            حدد الصلاحيات التي تريد منحها لهذا المستخدم. يمكنك تحديد مجموعة كاملة أو صلاحيات محددة.
                        </div>

                        <div class="row">
                            <?php foreach ($allPermissions as $groupKey => $groupPermissions): ?>
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header bg-light">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                       id="group_<?php echo $groupKey; ?>"
                                                       onchange="toggleGroupPermissions(this, '<?php echo $groupKey; ?>')">
                                                <label class="form-check-label fw-bold" for="group_<?php echo $groupKey; ?>">
                                                    <?php echo $groupNames[$groupKey] ?? $groupKey; ?>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <?php foreach ($groupPermissions as $perm): ?>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox"
                                                           name="permissions[]"
                                                           value="<?php echo $perm['permission_key']; ?>"
                                                           data-group="<?php echo $groupKey; ?>"
                                                           id="perm_<?php echo $perm['permission_key']; ?>">
                                                    <label class="form-check-label" for="perm_<?php echo $perm['permission_key']; ?>">
                                                        <?php echo $perm['permission_name']; ?>
                                                    </label>
                                                    <?php if ($perm['description']): ?>
                                                        <small class="text-muted d-block"><?php echo $perm['description']; ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ الصلاحيات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
