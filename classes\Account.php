<?php
/**
 * SeaSystem - فئة إدارة الحسابات
 * Account Management Class
 */

require_once __DIR__ . '/../config/database.php';

class Account {
    private $db;
    private $table_name = "chart_of_accounts";

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }

    /**
     * إضافة حساب جديد
     */
    public function create($data) {
        try {
            // التحقق من عدم تكرار رمز الحساب
            if ($this->isAccountCodeExists($data['account_code'])) {
                return [
                    'success' => false,
                    'message' => 'رمز الحساب موجود مسبقاً'
                ];
            }

            $sql = "INSERT INTO " . $this->table_name . "
                    (account_code, account_name, account_type, parent_id, description)
                    VALUES (:account_code, :account_name, :account_type, :parent_id, :description)";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':account_code', $data['account_code']);
            $stmt->bindParam(':account_name', $data['account_name']);
            $stmt->bindParam(':account_type', $data['account_type']);
            $stmt->bindParam(':parent_id', $data['parent_id']);
            $stmt->bindParam(':description', $data['description']);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم إضافة الحساب بنجاح',
                    'account_id' => $this->db->lastInsertId()
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في إضافة الحساب'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * تحديث بيانات الحساب
     */
    public function update($id, $data) {
        try {
            // التحقق من وجود الحساب
            if (!$this->exists($id)) {
                return [
                    'success' => false,
                    'message' => 'الحساب غير موجود'
                ];
            }

            // التحقق من عدم تكرار رمز الحساب (باستثناء الحساب الحالي)
            if ($this->isAccountCodeExists($data['account_code'], $id)) {
                return [
                    'success' => false,
                    'message' => 'رمز الحساب موجود مسبقاً'
                ];
            }

            $sql = "UPDATE " . $this->table_name . "
                    SET account_code = :account_code,
                        account_name = :account_name,
                        account_type = :account_type,
                        parent_id = :parent_id,
                        description = :description,
                        updated_at = NOW()
                    WHERE id = :id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':account_code', $data['account_code']);
            $stmt->bindParam(':account_name', $data['account_name']);
            $stmt->bindParam(':account_type', $data['account_type']);
            $stmt->bindParam(':parent_id', $data['parent_id']);
            $stmt->bindParam(':description', $data['description']);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم تحديث الحساب بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في تحديث الحساب'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * حذف الحساب
     */
    public function delete($id) {
        try {
            // التحقق من وجود الحساب
            if (!$this->exists($id)) {
                return [
                    'success' => false,
                    'message' => 'الحساب غير موجود'
                ];
            }

            // التحقق من عدم وجود حسابات فرعية
            if ($this->hasSubAccounts($id)) {
                return [
                    'success' => false,
                    'message' => 'لا يمكن حذف الحساب لوجود حسابات فرعية'
                ];
            }

            // التحقق من عدم وجود قيود محاسبية
            if ($this->hasTransactions($id)) {
                // إلغاء تفعيل الحساب بدلاً من حذفه
                return $this->deactivate($id);
            }

            $sql = "DELETE FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم حذف الحساب بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في حذف الحساب'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * إلغاء تفعيل الحساب
     */
    public function deactivate($id) {
        try {
            $sql = "UPDATE " . $this->table_name . " SET is_active = 0, updated_at = NOW() WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم إلغاء تفعيل الحساب بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في إلغاء تفعيل الحساب'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * تفعيل الحساب
     */
    public function activate($id) {
        try {
            $sql = "UPDATE " . $this->table_name . " SET is_active = 1, updated_at = NOW() WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم تفعيل الحساب بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في تفعيل الحساب'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * الحصول على بيانات حساب واحد
     */
    public function getById($id) {
        try {
            $sql = "SELECT a.*, p.account_name as parent_name
                    FROM " . $this->table_name . " a
                    LEFT JOIN " . $this->table_name . " p ON a.parent_id = p.id
                    WHERE a.id = :id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();

            return $stmt->fetch();
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * الحصول على جميع الحسابات
     */
    public function getAll($active_only = true) {
        try {
            $sql = "SELECT a.*, p.account_name as parent_name
                    FROM " . $this->table_name . " a
                    LEFT JOIN " . $this->table_name . " p ON a.parent_id = p.id";

            if ($active_only) {
                $sql .= " WHERE a.is_active = 1";
            }

            $sql .= " ORDER BY a.account_code";

            $stmt = $this->db->prepare($sql);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على الحسابات حسب النوع
     */
    public function getByType($type, $active_only = true) {
        try {
            $sql = "SELECT * FROM " . $this->table_name . " WHERE account_type = :type";

            if ($active_only) {
                $sql .= " AND is_active = 1";
            }

            $sql .= " ORDER BY account_code";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':type', $type);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على الحسابات الرئيسية (بدون حساب أب)
     */
    public function getMainAccounts($active_only = true) {
        try {
            $sql = "SELECT * FROM " . $this->table_name . " WHERE parent_id IS NULL";

            if ($active_only) {
                $sql .= " AND is_active = 1";
            }

            $sql .= " ORDER BY account_code";

            $stmt = $this->db->prepare($sql);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على الحسابات الفرعية
     */
    public function getSubAccounts($parent_id, $active_only = true) {
        try {
            $sql = "SELECT * FROM " . $this->table_name . " WHERE parent_id = :parent_id";

            if ($active_only) {
                $sql .= " AND is_active = 1";
            }

            $sql .= " ORDER BY account_code";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':parent_id', $parent_id);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * البحث في الحسابات
     */
    public function search($keyword, $active_only = true) {
        try {
            $sql = "SELECT a.*, p.account_name as parent_name
                    FROM " . $this->table_name . " a
                    LEFT JOIN " . $this->table_name . " p ON a.parent_id = p.id
                    WHERE (a.account_code LIKE :keyword OR a.account_name LIKE :keyword)";

            if ($active_only) {
                $sql .= " AND a.is_active = 1";
            }

            $sql .= " ORDER BY a.account_code";

            $stmt = $this->db->prepare($sql);
            $keyword = "%$keyword%";
            $stmt->bindParam(':keyword', $keyword);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * التحقق من وجود الحساب
     */
    private function exists($id) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من تكرار رمز الحساب
     */
    private function isAccountCodeExists($account_code, $exclude_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE account_code = :account_code";

            if ($exclude_id) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':account_code', $account_code);

            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }

            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من وجود حسابات فرعية
     */
    private function hasSubAccounts($id) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE parent_id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من وجود قيود محاسبية للحساب
     */
    private function hasTransactions($id) {
        try {
            $sql = "SELECT COUNT(*) FROM journal_entry_details WHERE account_id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * حساب رصيد الحساب
     */
    public function getBalance($account_id, $date = null) {
        try {
            $sql = "SELECT
                        SUM(debit_amount) as total_debit,
                        SUM(credit_amount) as total_credit
                    FROM journal_entry_details jed
                    JOIN journal_entries je ON jed.journal_entry_id = je.id
                    WHERE jed.account_id = :account_id AND je.status = 'posted'";

            if ($date) {
                $sql .= " AND je.entry_date <= :date";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':account_id', $account_id);

            if ($date) {
                $stmt->bindParam(':date', $date);
            }

            $stmt->execute();
            $result = $stmt->fetch();

            $total_debit = $result['total_debit'] ?? 0;
            $total_credit = $result['total_credit'] ?? 0;

            return $total_debit - $total_credit;
        } catch (Exception $e) {
            return 0;
        }
    }
}
?>
