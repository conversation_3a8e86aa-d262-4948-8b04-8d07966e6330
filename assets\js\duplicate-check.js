/**
 * SeaSystem - التحقق من التكرار في الواجهة الأمامية
 * Frontend Duplicate Check
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // التحقق من تكرار رمز العميل
    const customerCodeInput = document.getElementById('customer_code');
    if (customerCodeInput) {
        customerCodeInput.addEventListener('blur', function() {
            checkCustomerCodeDuplicate(this.value, null);
        });
    }
    
    // التحقق من تكرار رمز المورد
    const supplierCodeInput = document.getElementById('supplier_code');
    if (supplierCodeInput) {
        supplierCodeInput.addEventListener('blur', function() {
            checkSupplierCodeDuplicate(this.value, null);
        });
    }
    
    // التحقق من تكرار رمز المنتج
    const productCodeInput = document.getElementById('product_code');
    if (productCodeInput) {
        productCodeInput.addEventListener('blur', function() {
            checkProductCodeDuplicate(this.value, null);
        });
    }
    
    // التحقق من تكرار اسم العميل
    const customerNameInput = document.getElementById('name');
    if (customerNameInput && window.location.pathname.includes('customers.php')) {
        customerNameInput.addEventListener('blur', function() {
            checkCustomerNameDuplicate(this.value, null);
        });
    }
    
    // التحقق من تكرار اسم المورد
    const supplierNameInput = document.getElementById('name');
    if (supplierNameInput && window.location.pathname.includes('suppliers.php')) {
        supplierNameInput.addEventListener('blur', function() {
            checkSupplierNameDuplicate(this.value, null);
        });
    }
    
    // التحقق من تكرار اسم المنتج
    const productNameInput = document.getElementById('product_name');
    if (productNameInput) {
        productNameInput.addEventListener('blur', function() {
            checkProductNameDuplicate(this.value, null);
        });
    }
    
    // التحقق من تكرار البريد الإلكتروني
    const emailInputs = document.querySelectorAll('input[type="email"]');
    emailInputs.forEach(function(input) {
        input.addEventListener('blur', function() {
            if (this.value.trim() !== '') {
                if (window.location.pathname.includes('customers.php')) {
                    checkCustomerEmailDuplicate(this.value, null);
                } else if (window.location.pathname.includes('suppliers.php')) {
                    checkSupplierEmailDuplicate(this.value, null);
                }
            }
        });
    });
    
    // التحقق من تكرار رقم الهاتف
    const phoneInputs = document.querySelectorAll('input[name="phone"]');
    phoneInputs.forEach(function(input) {
        input.addEventListener('blur', function() {
            if (this.value.trim() !== '') {
                if (window.location.pathname.includes('customers.php')) {
                    checkCustomerPhoneDuplicate(this.value, null);
                } else if (window.location.pathname.includes('suppliers.php')) {
                    checkSupplierPhoneDuplicate(this.value, null);
                }
            }
        });
    });
});

// دوال التحقق من تكرار العملاء
function checkCustomerCodeDuplicate(code, excludeId) {
    if (!code.trim()) return;
    
    fetch('ajax_check_duplicate.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `type=customer_code&value=${encodeURIComponent(code)}&exclude_id=${excludeId || ''}`
    })
    .then(response => response.json())
    .then(data => {
        showDuplicateResult('customer_code', data);
    })
    .catch(error => {
        console.error('خطأ في التحقق من التكرار:', error);
    });
}

function checkCustomerNameDuplicate(name, excludeId) {
    if (!name.trim()) return;
    
    fetch('ajax_check_duplicate.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `type=customer_name&value=${encodeURIComponent(name)}&exclude_id=${excludeId || ''}`
    })
    .then(response => response.json())
    .then(data => {
        showDuplicateResult('name', data);
    })
    .catch(error => {
        console.error('خطأ في التحقق من التكرار:', error);
    });
}

function checkCustomerEmailDuplicate(email, excludeId) {
    if (!email.trim()) return;
    
    fetch('ajax_check_duplicate.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `type=customer_email&value=${encodeURIComponent(email)}&exclude_id=${excludeId || ''}`
    })
    .then(response => response.json())
    .then(data => {
        showDuplicateResult('email', data);
    })
    .catch(error => {
        console.error('خطأ في التحقق من التكرار:', error);
    });
}

function checkCustomerPhoneDuplicate(phone, excludeId) {
    if (!phone.trim()) return;
    
    fetch('ajax_check_duplicate.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `type=customer_phone&value=${encodeURIComponent(phone)}&exclude_id=${excludeId || ''}`
    })
    .then(response => response.json())
    .then(data => {
        showDuplicateResult('phone', data);
    })
    .catch(error => {
        console.error('خطأ في التحقق من التكرار:', error);
    });
}

// دوال التحقق من تكرار الموردين
function checkSupplierCodeDuplicate(code, excludeId) {
    if (!code.trim()) return;
    
    fetch('ajax_check_duplicate.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `type=supplier_code&value=${encodeURIComponent(code)}&exclude_id=${excludeId || ''}`
    })
    .then(response => response.json())
    .then(data => {
        showDuplicateResult('supplier_code', data);
    })
    .catch(error => {
        console.error('خطأ في التحقق من التكرار:', error);
    });
}

function checkSupplierNameDuplicate(name, excludeId) {
    if (!name.trim()) return;
    
    fetch('ajax_check_duplicate.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `type=supplier_name&value=${encodeURIComponent(name)}&exclude_id=${excludeId || ''}`
    })
    .then(response => response.json())
    .then(data => {
        showDuplicateResult('name', data);
    })
    .catch(error => {
        console.error('خطأ في التحقق من التكرار:', error);
    });
}

function checkSupplierEmailDuplicate(email, excludeId) {
    if (!email.trim()) return;
    
    fetch('ajax_check_duplicate.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `type=supplier_email&value=${encodeURIComponent(email)}&exclude_id=${excludeId || ''}`
    })
    .then(response => response.json())
    .then(data => {
        showDuplicateResult('email', data);
    })
    .catch(error => {
        console.error('خطأ في التحقق من التكرار:', error);
    });
}

function checkSupplierPhoneDuplicate(phone, excludeId) {
    if (!phone.trim()) return;
    
    fetch('ajax_check_duplicate.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `type=supplier_phone&value=${encodeURIComponent(phone)}&exclude_id=${excludeId || ''}`
    })
    .then(response => response.json())
    .then(data => {
        showDuplicateResult('phone', data);
    })
    .catch(error => {
        console.error('خطأ في التحقق من التكرار:', error);
    });
}

// دوال التحقق من تكرار المنتجات
function checkProductCodeDuplicate(code, excludeId) {
    if (!code.trim()) return;
    
    fetch('ajax_check_duplicate.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `type=product_code&value=${encodeURIComponent(code)}&exclude_id=${excludeId || ''}`
    })
    .then(response => response.json())
    .then(data => {
        showDuplicateResult('product_code', data);
    })
    .catch(error => {
        console.error('خطأ في التحقق من التكرار:', error);
    });
}

function checkProductNameDuplicate(name, excludeId) {
    if (!name.trim()) return;
    
    fetch('ajax_check_duplicate.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `type=product_name&value=${encodeURIComponent(name)}&exclude_id=${excludeId || ''}`
    })
    .then(response => response.json())
    .then(data => {
        showDuplicateResult('product_name', data);
    })
    .catch(error => {
        console.error('خطأ في التحقق من التكرار:', error);
    });
}

// عرض نتيجة التحقق من التكرار
function showDuplicateResult(fieldId, data) {
    const field = document.getElementById(fieldId);
    if (!field) return;
    
    // إزالة الرسائل السابقة
    const existingMessage = field.parentElement.querySelector('.duplicate-message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    // إزالة الفئات السابقة
    field.classList.remove('is-valid', 'is-invalid');
    
    if (data.exists) {
        // إضافة رسالة خطأ
        field.classList.add('is-invalid');
        const errorMessage = document.createElement('div');
        errorMessage.className = 'invalid-feedback duplicate-message';
        errorMessage.innerHTML = `<i class="fas fa-exclamation-triangle me-1"></i>${data.message}`;
        field.parentElement.appendChild(errorMessage);
        
        // منع إرسال النموذج
        const form = field.closest('form');
        if (form) {
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>يوجد تكرار - لا يمكن الحفظ';
            }
        }
    } else {
        // إضافة رسالة نجاح
        field.classList.add('is-valid');
        const successMessage = document.createElement('div');
        successMessage.className = 'valid-feedback duplicate-message';
        successMessage.innerHTML = `<i class="fas fa-check me-1"></i>متاح للاستخدام`;
        field.parentElement.appendChild(successMessage);
        
        // تفعيل زر الإرسال إذا لم توجد أخطاء أخرى
        checkFormValidity(field.closest('form'));
    }
}

// التحقق من صحة النموذج
function checkFormValidity(form) {
    if (!form) return;
    
    const invalidFields = form.querySelectorAll('.is-invalid');
    const submitButton = form.querySelector('button[type="submit"]');
    
    if (submitButton) {
        if (invalidFields.length === 0) {
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="fas fa-save me-2"></i>حفظ';
        }
    }
}

// إضافة تنسيق CSS
const style = document.createElement('style');
style.textContent = `
    .duplicate-message {
        display: block;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.875em;
    }
    
    .is-invalid {
        border-color: #dc3545;
    }
    
    .is-valid {
        border-color: #198754;
    }
    
    .invalid-feedback {
        color: #dc3545;
    }
    
    .valid-feedback {
        color: #198754;
    }
    
    .duplicate-checking {
        border-color: #ffc107;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23ffc107'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='M6 3v3l2 1'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }
`;
document.head.appendChild(style);
