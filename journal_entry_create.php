<?php
/**
 * SeaSystem - إنشاء قيد محاسبي جديد
 * Create New Journal Entry
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/JournalEntry.php';
require_once __DIR__ . '/classes/Account.php';

// التأكد من تسجيل الدخول
requireLogin();

$journal = new JournalEntry();
$account = new Account();
$current_user = getCurrentUser();

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_entry'])) {
    $entry_data = [
        'entry_date' => $_POST['entry_date'],
        'reference_type' => $_POST['reference_type'],
        'reference_id' => $_POST['reference_id'],
        'description' => $_POST['description'],
        'notes' => $_POST['notes'],
        'created_by' => $current_user['id']
    ];

    // تفاصيل القيد
    $details_data = [];
    $total_debit = 0;
    $total_credit = 0;

    if (isset($_POST['account_id']) && is_array($_POST['account_id'])) {
        for ($i = 0; $i < count($_POST['account_id']); $i++) {
            if (!empty($_POST['account_id'][$i])) {
                $debit = floatval($_POST['debit_amount'][$i] ?? 0);
                $credit = floatval($_POST['credit_amount'][$i] ?? 0);

                if ($debit > 0 || $credit > 0) {
                    $details_data[] = [
                        'account_id' => $_POST['account_id'][$i],
                        'debit_amount' => $debit,
                        'credit_amount' => $credit,
                        'description' => $_POST['line_description'][$i] ?? ''
                    ];

                    $total_debit += $debit;
                    $total_credit += $credit;
                }
            }
        }
    }

    // التحقق من توازن القيد
    if (abs($total_debit - $total_credit) < 0.01 && count($details_data) >= 2) {
        $result = $journal->create($entry_data, $details_data);
        $message = $result['message'];
        $message_type = $result['success'] ? 'success' : 'danger';

        if ($result['success']) {
            // إعادة توجيه إلى صفحة دفتر اليومية
            header('Location: journal.php?success=' . urlencode($message));
            exit();
        }
    } else {
        $message = 'خطأ: يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن، ويجب أن يحتوي القيد على سطرين على الأقل';
        $message_type = 'danger';
    }
}

// الحصول على الحسابات
$accounts = $account->getAll();

// أنواع المراجع
$reference_types = [
    'manual' => 'قيد يدوي',
    'invoice' => 'فاتورة',
    'payment' => 'دفعة',
    'adjustment' => 'تسوية',
    'opening' => 'رصيد افتتاحي',
    'closing' => 'رصيد ختامي'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء قيد محاسبي - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <style>
        .entry-form-header {
            background: linear-gradient(135deg, #343a40 0%, #6c757d 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .form-section {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-title {
            color: #343a40;
            border-bottom: 2px solid #343a40;
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .journal-line {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .journal-line.remove-line {
            animation: fadeOut 0.3s ease-out;
        }

        @keyframes fadeOut {
            from { opacity: 1; transform: scale(1); }
            to { opacity: 0; transform: scale(0.95); }
        }

        .balance-indicator {
            position: sticky;
            top: 20px;
            z-index: 100;
        }

        .balance-card {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
        }

        .balance-equal {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
        }

        .balance-unequal {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
        }

        .form-control:focus {
            border-color: #343a40;
            box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #343a40 0%, #6c757d 100%);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #6c757d 0%, #343a40 100%);
        }
    </style>

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }

        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <div class="container-fluid">
        <div class="row">
            <!-- المحتوى الرئيسي -->
            <div class="col-lg-9 p-4">
                <!-- رأس الصفحة -->
                <div class="entry-form-header">
                    <h1 class="mb-2">
                        <i class="fas fa-book me-3"></i>إنشاء قيد محاسبي جديد
                    </h1>
                    <p class="mb-0 opacity-75">إنشاء قيد محاسبي جديد في دفتر اليومية مع ضمان توازن المدين والدائن</p>
                </div>

                <!-- أزرار التنقل -->
                <div class="row mb-3">
                    <div class="col">
                        <a href="journal.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة لدفتر اليومية
                        </a>
                    </div>
                </div>

                <!-- رسائل التنبيه -->
                <?php if (isset($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- نموذج إنشاء القيد -->
                <form method="POST" id="entryForm">
                    <!-- معلومات القيد الأساسية -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-info-circle me-2"></i>معلومات القيد الأساسية
                        </h4>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="entry_date" class="form-label">تاريخ القيد *</label>
                                    <input type="date" class="form-control" id="entry_date" name="entry_date"
                                           value="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="reference_type" class="form-label">نوع المرجع *</label>
                                    <select class="form-select" id="reference_type" name="reference_type" required>
                                        <?php foreach ($reference_types as $type => $type_name): ?>
                                            <option value="<?php echo $type; ?>" <?php echo $type == 'manual' ? 'selected' : ''; ?>>
                                                <?php echo $type_name; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="reference_id" class="form-label">رقم المرجع</label>
                                    <input type="text" class="form-control" id="reference_id" name="reference_id">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">وصف القيد *</label>
                            <input type="text" class="form-control" id="description" name="description" required>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                        </div>
                    </div>

                    <!-- تفاصيل القيد -->
                    <div class="form-section">
                        <div class="mb-3">
                            <h4 class="section-title mb-0">
                                <i class="fas fa-list me-2"></i>تفاصيل القيد
                            </h4>
                        </div>

                        <div id="journal-lines">
                            <!-- سطر القيد الأول -->
                            <div class="journal-line" data-line="1">
                                <div class="row align-items-center">
                                    <div class="col-md-4">
                                        <label class="form-label">الحساب *</label>
                                        <select class="form-select" name="account_id[]" required>
                                            <option value="">اختر الحساب</option>
                                            <?php foreach ($accounts as $acc): ?>
                                                <option value="<?php echo $acc['id']; ?>">
                                                    <?php echo htmlspecialchars($acc['account_code'] . ' - ' . $acc['account_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">مدين</label>
                                        <input type="number" class="form-control debit-input" name="debit_amount[]"
                                               step="0.01" min="0" onchange="updateBalance()">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">دائن</label>
                                        <input type="number" class="form-control credit-input" name="credit_amount[]"
                                               step="0.01" min="0" onchange="updateBalance()">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">وصف السطر</label>
                                        <input type="text" class="form-control" name="line_description[]">
                                    </div>
                                    <div class="col-md-1">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="button" class="btn btn-danger btn-sm w-100" onclick="removeJournalLine(this)" disabled>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- سطر القيد الثاني -->
                            <div class="journal-line" data-line="2">
                                <div class="row align-items-center">
                                    <div class="col-md-4">
                                        <label class="form-label">الحساب *</label>
                                        <select class="form-select" name="account_id[]" required>
                                            <option value="">اختر الحساب</option>
                                            <?php foreach ($accounts as $acc): ?>
                                                <option value="<?php echo $acc['id']; ?>">
                                                    <?php echo htmlspecialchars($acc['account_code'] . ' - ' . $acc['account_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">مدين</label>
                                        <input type="number" class="form-control debit-input" name="debit_amount[]"
                                               step="0.01" min="0" onchange="updateBalance()">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">دائن</label>
                                        <input type="number" class="form-control credit-input" name="credit_amount[]"
                                               step="0.01" min="0" onchange="updateBalance()">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">وصف السطر</label>
                                        <input type="text" class="form-control" name="line_description[]">
                                    </div>
                                    <div class="col-md-1">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="button" class="btn btn-danger btn-sm w-100" onclick="removeJournalLine(this)" disabled>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- زر إضافة سطر جديد -->
                        <div class="text-center mt-3">
                            <button type="button" class="btn btn-success" onclick="addJournalLine()">
                                <i class="fas fa-plus me-2"></i>إضافة سطر جديد
                            </button>
                        </div>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="form-section text-center">
                        <button type="submit" name="create_entry" class="btn btn-primary btn-lg me-3" id="saveButton" disabled>
                            <i class="fas fa-save me-2"></i>حفظ القيد
                        </button>
                        <button type="button" class="btn btn-success btn-lg me-3" onclick="saveAndAddAnother()" id="saveAddButton" disabled>
                            <i class="fas fa-plus me-2"></i>حفظ وإضافة آخر
                        </button>
                        <a href="journal.php" class="btn btn-secondary btn-lg">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                    </div>
                </form>
            </div>

            <!-- مؤشر التوازن -->
            <div class="col-lg-3 p-4">
                <div class="balance-indicator">
                    <div class="balance-card" id="balanceCard">
                        <h5 class="mb-3">
                            <i class="fas fa-balance-scale me-2"></i>توازن القيد
                        </h5>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>إجمالي المدين:</span>
                                <strong id="totalDebit">0.00</strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>إجمالي الدائن:</span>
                                <strong id="totalCredit">0.00</strong>
                            </div>
                            <hr class="my-2">
                            <div class="d-flex justify-content-between">
                                <span>الفرق:</span>
                                <strong id="difference">0.00</strong>
                            </div>
                        </div>

                        <div id="balanceStatus">
                            <i class="fas fa-info-circle me-2"></i>
                            <span>أدخل المبالغ</span>
                        </div>
                    </div>

                    <!-- نصائح -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-lightbulb me-2"></i>نصائح
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0 small">
                                <li><i class="fas fa-check text-success me-2"></i>يجب أن يكون إجمالي المدين = إجمالي الدائن</li>
                                <li><i class="fas fa-check text-success me-2"></i>يجب أن يحتوي القيد على سطرين على الأقل</li>
                                <li><i class="fas fa-check text-success me-2"></i>يمكن إضافة أكثر من سطر للقيود المركبة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let lineCounter = 2;

        // إضافة سطر جديد
        function addJournalLine() {
            lineCounter++;
            const container = document.getElementById('journal-lines');
            const newLine = document.createElement('div');
            newLine.className = 'journal-line';
            newLine.setAttribute('data-line', lineCounter);

            newLine.innerHTML = `
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <label class="form-label">الحساب *</label>
                        <select class="form-select" name="account_id[]" required>
                            <option value="">اختر الحساب</option>
                            <?php foreach ($accounts as $acc): ?>
                                <option value="<?php echo $acc['id']; ?>">
                                    <?php echo htmlspecialchars($acc['account_code'] . ' - ' . $acc['account_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">مدين</label>
                        <input type="number" class="form-control debit-input" name="debit_amount[]"
                               step="0.01" min="0" onchange="updateBalance()">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">دائن</label>
                        <input type="number" class="form-control credit-input" name="credit_amount[]"
                               step="0.01" min="0" onchange="updateBalance()">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">وصف السطر</label>
                        <input type="text" class="form-control" name="line_description[]">
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <button type="button" class="btn btn-danger btn-sm w-100" onclick="removeJournalLine(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;

            container.appendChild(newLine);
            updateRemoveButtons();
        }

        // حذف سطر
        function removeJournalLine(button) {
            const line = button.closest('.journal-line');
            line.classList.add('remove-line');
            setTimeout(() => {
                line.remove();
                updateBalance();
                updateRemoveButtons();
            }, 300);
        }

        // تحديث أزرار الحذف
        function updateRemoveButtons() {
            const lines = document.querySelectorAll('.journal-line');
            const removeButtons = document.querySelectorAll('.journal-line button[onclick*="removeJournalLine"]');

            removeButtons.forEach(button => {
                button.disabled = lines.length <= 2;
            });
        }

        // تحديث التوازن
        function updateBalance() {
            const debitInputs = document.querySelectorAll('.debit-input');
            const creditInputs = document.querySelectorAll('.credit-input');

            let totalDebit = 0;
            let totalCredit = 0;

            debitInputs.forEach(input => {
                totalDebit += parseFloat(input.value) || 0;
            });

            creditInputs.forEach(input => {
                totalCredit += parseFloat(input.value) || 0;
            });

            const difference = Math.abs(totalDebit - totalCredit);
            const isBalanced = difference < 0.01 && totalDebit > 0 && totalCredit > 0;

            // تحديث العرض
            document.getElementById('totalDebit').textContent = totalDebit.toFixed(2);
            document.getElementById('totalCredit').textContent = totalCredit.toFixed(2);
            document.getElementById('difference').textContent = difference.toFixed(2);

            const balanceCard = document.getElementById('balanceCard');
            const balanceStatus = document.getElementById('balanceStatus');
            const saveButton = document.getElementById('saveButton');
            const saveAddButton = document.getElementById('saveAddButton');

            if (isBalanced) {
                balanceCard.className = 'balance-card balance-equal';
                balanceStatus.innerHTML = '<i class="fas fa-check-circle me-2"></i><span>القيد متوازن</span>';
                saveButton.disabled = false;
                saveAddButton.disabled = false;
            } else if (totalDebit > 0 || totalCredit > 0) {
                balanceCard.className = 'balance-card balance-unequal';
                balanceStatus.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i><span>القيد غير متوازن</span>';
                saveButton.disabled = true;
                saveAddButton.disabled = true;
            } else {
                balanceCard.className = 'balance-card';
                balanceStatus.innerHTML = '<i class="fas fa-info-circle me-2"></i><span>أدخل المبالغ</span>';
                saveButton.disabled = true;
                saveAddButton.disabled = true;
            }
        }

        // حفظ وإضافة آخر
        function saveAndAddAnother() {
            const form = document.getElementById('entryForm');
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'save_and_add_another';
            input.value = '1';
            form.appendChild(input);
            form.submit();
        }

        // التحقق من صحة النموذج
        document.getElementById('entryForm').addEventListener('submit', function(e) {
            const description = document.getElementById('description').value.trim();

            if (!description) {
                e.preventDefault();
                alert('يرجى إدخال وصف القيد');
                document.getElementById('description').focus();
                return;
            }

            // التحقق من وجود حسابات محددة
            const accountSelects = document.querySelectorAll('select[name="account_id[]"]');
            let hasValidAccounts = 0;

            accountSelects.forEach(select => {
                if (select.value) hasValidAccounts++;
            });

            if (hasValidAccounts < 2) {
                e.preventDefault();
                alert('يجب تحديد حسابين على الأقل');
                return;
            }
        });

        // تحديث التوازن عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateBalance();
            updateRemoveButtons();
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
