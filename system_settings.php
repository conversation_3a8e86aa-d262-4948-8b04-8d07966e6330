<?php
/**
 * SeaSystem - إعدادات النظام
 * System Settings Page
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/includes/sidebar.php';

// التأكد من تسجيل الدخول وصلاحيات المدير
requireLogin();
if (!hasPermission('admin')) {
    header('Location: dashboard.php?error=no_permission');
    exit();
}

$current_user = getCurrentUser();

// معالجة العمليات
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // معالجة حفظ الإعدادات
    $message = 'تم حفظ الإعدادات بنجاح';
    $message_type = 'success';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <link href="assets/css/sidebar-only.css" rel="stylesheet">
</head>
<body>
    <!-- الهيدر الثابت -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-anchor me-2"></i><?php echo SITE_NAME; ?>
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($current_user['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- القائمة الجانبية -->
            <?php renderSidebar('system_settings.php'); ?>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="page-title">
                                <i class="fas fa-sliders-h me-2"></i>إعدادات النظام
                            </h1>
                            <p class="page-subtitle">إدارة إعدادات النظام العامة والتخصيص</p>
                        </div>
                    </div>
                </div>

                <!-- رسائل التنبيه -->
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- بطاقات الإعدادات -->
                <div class="row">
                    <!-- إعدادات عامة -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-cog me-2"></i>الإعدادات العامة
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <div class="mb-3">
                                        <label class="form-label">اسم النظام</label>
                                        <input type="text" class="form-control" name="site_name" value="<?php echo SITE_NAME; ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">رمز العملة</label>
                                        <input type="text" class="form-control" name="currency_symbol" value="<?php echo CURRENCY_SYMBOL; ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">المنطقة الزمنية</label>
                                        <select class="form-select" name="timezone">
                                            <option value="Asia/Riyadh">الرياض</option>
                                            <option value="Asia/Dubai">دبي</option>
                                            <option value="Asia/Kuwait">الكويت</option>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الأمان -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-shield-alt me-2"></i>إعدادات الأمان
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">الحد الأقصى لمحاولات تسجيل الدخول</label>
                                    <input type="number" class="form-control" value="5" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">مهلة انتهاء الجلسة (دقيقة)</label>
                                    <input type="number" class="form-control" value="60" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الحد الأدنى لطول كلمة المرور</label>
                                    <input type="number" class="form-control" value="6" readonly>
                                </div>
                                <button type="button" class="btn btn-warning">
                                    <i class="fas fa-edit me-2"></i>تعديل إعدادات الأمان
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات النظام -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>معلومات النظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>اسم النظام:</strong></td>
                                        <td><?php echo SITE_NAME; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>الإصدار:</strong></td>
                                        <td><?php echo defined('SITE_VERSION') ? SITE_VERSION : '1.0.0'; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>إصدار PHP:</strong></td>
                                        <td><?php echo PHP_VERSION; ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>قاعدة البيانات:</strong></td>
                                        <td>MySQL</td>
                                    </tr>
                                    <tr>
                                        <td><strong>المنطقة الزمنية:</strong></td>
                                        <td><?php echo date_default_timezone_get(); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>تاريخ اليوم:</strong></td>
                                        <td><?php echo date('Y-m-d H:i:s'); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
