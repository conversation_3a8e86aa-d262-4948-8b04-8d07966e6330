<?php
/**
 * SeaSystem - مولد الأرقام التلقائية
 * Automatic Number Generator Class
 */

require_once __DIR__ . '/../config/database.php';

class NumberGenerator {
    private $db;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->createNumberingTable();
    }

    /**
     * إنشاء جدول الترقيم إذا لم يكن موجوداً
     */
    private function createNumberingTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS auto_numbering (
                id INT AUTO_INCREMENT PRIMARY KEY,
                entity_type VARCHAR(50) NOT NULL,
                prefix VARCHAR(10) NOT NULL,
                last_number INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_entity (entity_type)
            )";
            $this->db->exec($sql);

            // إدراج الكيانات الأساسية إذا لم تكن موجودة
            $entities = [
                ['entity_type' => 'customer', 'prefix' => 'CUS'],
                ['entity_type' => 'supplier', 'prefix' => 'SUP'],
                ['entity_type' => 'product', 'prefix' => 'PRD'],
                ['entity_type' => 'warehouse', 'prefix' => 'WH'],
                ['entity_type' => 'invoice_sales', 'prefix' => 'SALES'],
                ['entity_type' => 'invoice_purchase', 'prefix' => 'PURCH'],
                ['entity_type' => 'payment', 'prefix' => 'PAY'],
                ['entity_type' => 'journal', 'prefix' => 'JE']
            ];

            foreach ($entities as $entity) {
                $check_sql = "SELECT COUNT(*) FROM auto_numbering WHERE entity_type = :entity_type";
                $check_stmt = $this->db->prepare($check_sql);
                $check_stmt->bindParam(':entity_type', $entity['entity_type']);
                $check_stmt->execute();

                if ($check_stmt->fetchColumn() == 0) {
                    $insert_sql = "INSERT INTO auto_numbering (entity_type, prefix, last_number) VALUES (:entity_type, :prefix, 0)";
                    $insert_stmt = $this->db->prepare($insert_sql);
                    $insert_stmt->bindParam(':entity_type', $entity['entity_type']);
                    $insert_stmt->bindParam(':prefix', $entity['prefix']);
                    $insert_stmt->execute();
                }
            }

        } catch (Exception $e) {
            // تجاهل الأخطاء إذا كان الجدول موجوداً
        }
    }

    /**
     * توليد رقم تلقائي للعملاء
     */
    public function generateCustomerCode() {
        return $this->generateNextNumber('customer', 'CUS', 3);
    }

    /**
     * توليد رقم تلقائي للموردين
     */
    public function generateSupplierCode() {
        return $this->generateNextNumber('supplier', 'SUP', 3);
    }

    /**
     * توليد رقم تلقائي للمنتجات
     */
    public function generateProductCode() {
        return $this->generateNextNumber('product', 'PRD', 3);
    }

    /**
     * توليد رقم تلقائي للمستودعات
     */
    public function generateWarehouseCode() {
        return $this->generateNextNumber('warehouse', 'WH', 3);
    }

    /**
     * توليد رقم تلقائي للفواتير
     */
    public function generateInvoiceNumber($invoice_type = 'sales') {
        $entity_type = 'invoice_' . $invoice_type;
        $prefix = ($invoice_type == 'sales') ? 'SALES' : 'PURCH';
        $year = date('Y');
        $month = date('m');

        $number = $this->generateNextNumber($entity_type, $prefix . $year . $month, 4);
        return $number;
    }

    /**
     * توليد رقم تلقائي للمدفوعات
     */
    public function generatePaymentNumber($payment_type = 'received') {
        $prefix = ($payment_type == 'received') ? 'REC' : 'PAY';
        $year = date('Y');
        $month = date('m');

        $number = $this->generateNextNumber('payment', $prefix . $year . $month, 3);
        return $number;
    }

    /**
     * توليد رقم تلقائي للقيود المحاسبية
     */
    public function generateJournalNumber() {
        $year = date('Y');
        $month = date('m');

        $number = $this->generateNextNumber('journal', 'JE' . $year . $month, 4);
        return $number;
    }

    /**
     * الدالة الأساسية لتوليد الأرقام
     */
    private function generateNextNumber($entity_type, $prefix, $padding = 3) {
        try {
            $this->db->beginTransaction();

            // التأكد من وجود السجل أولاً
            $check_sql = "SELECT last_number FROM auto_numbering WHERE entity_type = :entity_type";
            $check_stmt = $this->db->prepare($check_sql);
            $check_stmt->bindParam(':entity_type', $entity_type);
            $check_stmt->execute();

            $existing = $check_stmt->fetch();

            if ($existing) {
                // تحديث الرقم الموجود
                $sql = "UPDATE auto_numbering SET last_number = last_number + 1 WHERE entity_type = :entity_type";
                $stmt = $this->db->prepare($sql);
                $stmt->bindParam(':entity_type', $entity_type);
                $stmt->execute();

                $next_number = $existing['last_number'] + 1;
            } else {
                // إنشاء سجل جديد
                $sql = "INSERT INTO auto_numbering (entity_type, prefix, last_number) VALUES (:entity_type, :prefix, 1)";
                $stmt = $this->db->prepare($sql);
                $stmt->bindParam(':entity_type', $entity_type);
                $stmt->bindParam(':prefix', $prefix);
                $stmt->execute();

                $next_number = 1;
            }

            $this->db->commit();

            // تنسيق الرقم النهائي
            return $prefix . str_pad($next_number, $padding, '0', STR_PAD_LEFT);

        } catch (Exception $e) {
            $this->db->rollBack();
            // في حالة الخطأ، إرجاع رقم افتراضي
            return $prefix . str_pad(1, $padding, '0', STR_PAD_LEFT);
        }
    }

    /**
     * إعادة تعيين الترقيم لكيان معين
     */
    public function resetNumbering($entity_type, $start_number = 0) {
        try {
            $sql = "UPDATE auto_numbering SET last_number = :start_number WHERE entity_type = :entity_type";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':entity_type', $entity_type);
            $stmt->bindParam(':start_number', $start_number);
            $stmt->execute();

            return [
                'success' => true,
                'message' => 'تم إعادة تعيين الترقيم بنجاح'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في إعادة تعيين الترقيم: ' . $e->getMessage()
            ];
        }
    }

    /**
     * الحصول على الرقم التالي دون زيادة العداد (للعرض فقط)
     */
    public function getNextNumber($entity_type, $prefix, $padding = 3) {
        try {
            $sql = "SELECT last_number FROM auto_numbering WHERE entity_type = :entity_type";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':entity_type', $entity_type);
            $stmt->execute();

            $result = $stmt->fetch();
            $next_number = $result ? ($result['last_number'] + 1) : 1;

            return $prefix . str_pad($next_number, $padding, '0', STR_PAD_LEFT);

        } catch (Exception $e) {
            return $prefix . str_pad(1, $padding, '0', STR_PAD_LEFT);
        }
    }

    /**
     * حجز رقم مؤقت (للعرض في النموذج)
     */
    public function reserveNumber($entity_type, $prefix, $padding = 3) {
        try {
            // الحصول على الرقم التالي دون زيادة العداد
            $sql = "SELECT last_number FROM auto_numbering WHERE entity_type = :entity_type";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':entity_type', $entity_type);
            $stmt->execute();

            $result = $stmt->fetch();
            $next_number = $result ? ($result['last_number'] + 1) : 1;

            $reserved_number = $prefix . str_pad($next_number, $padding, '0', STR_PAD_LEFT);

            // حفظ الرقم المحجوز في الجلسة
            if (!isset($_SESSION['reserved_numbers'])) {
                $_SESSION['reserved_numbers'] = [];
            }
            $_SESSION['reserved_numbers'][$entity_type] = [
                'number' => $reserved_number,
                'timestamp' => time(),
                'raw_number' => $next_number
            ];

            return $reserved_number;

        } catch (Exception $e) {
            return $prefix . str_pad(1, $padding, '0', STR_PAD_LEFT);
        }
    }

    /**
     * تأكيد استخدام الرقم المحجوز (عند الحفظ)
     */
    public function confirmReservedNumber($entity_type) {
        try {
            if (isset($_SESSION['reserved_numbers'][$entity_type])) {
                $reserved = $_SESSION['reserved_numbers'][$entity_type];

                // زيادة العداد في قاعدة البيانات
                $this->db->beginTransaction();

                $sql = "UPDATE auto_numbering SET last_number = :number WHERE entity_type = :entity_type";
                $stmt = $this->db->prepare($sql);
                $stmt->bindParam(':number', $reserved['raw_number']);
                $stmt->bindParam(':entity_type', $entity_type);
                $stmt->execute();

                $this->db->commit();

                // إزالة الحجز من الجلسة
                unset($_SESSION['reserved_numbers'][$entity_type]);

                return $reserved['number'];
            }

            // إذا لم يوجد حجز، استخدم الطريقة العادية
            return $this->generateNextNumber($entity_type, '', 3);

        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }

    /**
     * إلغاء حجز الرقم (عند إلغاء العملية)
     */
    public function cancelReservedNumber($entity_type) {
        if (isset($_SESSION['reserved_numbers'][$entity_type])) {
            unset($_SESSION['reserved_numbers'][$entity_type]);
            return true;
        }
        return false;
    }

    /**
     * الحصول على الرقم المحجوز
     */
    public function getReservedNumber($entity_type) {
        if (isset($_SESSION['reserved_numbers'][$entity_type])) {
            return $_SESSION['reserved_numbers'][$entity_type]['number'];
        }
        return null;
    }

    /**
     * تنظيف الأرقام المحجوزة القديمة (أكثر من 30 دقيقة)
     */
    public function cleanupExpiredReservations() {
        if (isset($_SESSION['reserved_numbers'])) {
            $current_time = time();
            foreach ($_SESSION['reserved_numbers'] as $entity_type => $reservation) {
                // إذا مر أكثر من 30 دقيقة، احذف الحجز
                if (($current_time - $reservation['timestamp']) > 1800) {
                    unset($_SESSION['reserved_numbers'][$entity_type]);
                }
            }
        }
    }

    /**
     * الحصول على إحصائيات الترقيم
     */
    public function getNumberingStats() {
        try {
            $sql = "SELECT entity_type, prefix, last_number, updated_at FROM auto_numbering ORDER BY entity_type";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();

            return $stmt->fetchAll();

        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * التحقق من وجود رقم معين
     */
    public function isNumberExists($table, $column, $number) {
        try {
            $sql = "SELECT COUNT(*) FROM $table WHERE $column = :number";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':number', $number);
            $stmt->execute();

            return $stmt->fetchColumn() > 0;

        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * مزامنة الترقيم مع البيانات الموجودة
     */
    public function syncNumbering() {
        try {
            $sync_data = [
                'customer' => ['table' => 'customers', 'column' => 'customer_code', 'prefix' => 'CUS'],
                'supplier' => ['table' => 'suppliers', 'column' => 'supplier_code', 'prefix' => 'SUP'],
                'product' => ['table' => 'products', 'column' => 'product_code', 'prefix' => 'PRD'],
                'warehouse' => ['table' => 'warehouses', 'column' => 'warehouse_code', 'prefix' => 'WH']
            ];

            foreach ($sync_data as $entity_type => $data) {
                try {
                    // البحث عن أعلى رقم موجود
                    $sql = "SELECT MAX(CAST(SUBSTRING({$data['column']}, LENGTH('{$data['prefix']}') + 1) AS UNSIGNED)) as max_num
                            FROM {$data['table']}
                            WHERE {$data['column']} LIKE '{$data['prefix']}%'";

                    $stmt = $this->db->prepare($sql);
                    $stmt->execute();
                    $result = $stmt->fetch();

                    $max_number = $result['max_num'] ?? 0;

                    // تحديث جدول الترقيم
                    $update_sql = "UPDATE auto_numbering SET last_number = :max_number WHERE entity_type = :entity_type";
                    $update_stmt = $this->db->prepare($update_sql);
                    $update_stmt->bindParam(':max_number', $max_number);
                    $update_stmt->bindParam(':entity_type', $entity_type);
                    $update_stmt->execute();

                } catch (Exception $e) {
                    // تجاهل أخطاء الجداول غير الموجودة
                    continue;
                }
            }

            return [
                'success' => true,
                'message' => 'تم مزامنة الترقيم بنجاح'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في المزامنة: ' . $e->getMessage()
            ];
        }
    }
}
?>
