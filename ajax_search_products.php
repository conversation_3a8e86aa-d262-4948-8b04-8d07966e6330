<?php
/**
 * البحث الديناميكي في المنتجات
 * Dynamic Product Search
 */

// تعريف الثابت للوصول
define('SEASYSTEM_ACCESS', true);

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/Product.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    exit('غير مصرح');
}

// التحقق من وجود مصطلح البحث
$search_term = $_GET['search'] ?? '';

// إنشاء كائن المنتج
$product = new Product($pdo);

// البحث في المنتجات
if (!empty($search_term)) {
    $filters = ['search' => $search_term];
    $products = $product->getAll($filters);
} else {
    $products = $product->getAll();
}

// عرض النتائج
if (empty($products)) {
    echo '<tr>
            <td colspan="9" class="text-center py-4">
                <i class="fas fa-search fa-2x text-muted mb-2"></i>
                <p class="text-muted">لا توجد منتجات تطابق البحث</p>
            </td>
          </tr>';
} else {
    foreach ($products as $prod) {
        $stock_status = '';
        $stock_class = '';

        if ($prod['current_stock'] <= 0) {
            $stock_status = 'نفد المخزون';
            $stock_class = 'text-danger';
        } elseif ($prod['current_stock'] <= $prod['min_stock_level']) {
            $stock_status = 'مخزون منخفض';
            $stock_class = 'text-warning';
        } else {
            $stock_status = 'متوفر';
            $stock_class = 'text-success';
        }

        echo '<tr>
                <td><strong>' . htmlspecialchars($prod['product_code']) . '</strong></td>
                <td>' . htmlspecialchars($prod['product_name']) . '</td>
                <td>
                    <small class="text-muted">' .
                        htmlspecialchars(substr($prod['description'] ?? '', 0, 50)) .
                        (strlen($prod['description'] ?? '') > 50 ? '...' : '') .
                    '</small>
                </td>
                <td>' . number_format($prod['unit_price'], 2) . ' ' . CURRENCY_SYMBOL . '</td>
                <td>' . number_format($prod['cost_price'] ?? 0, 2) . ' ' . CURRENCY_SYMBOL . '</td>
                <td>
                    <span class="badge bg-' . ($prod['current_stock'] <= $prod['min_stock_level'] ? 'danger' : 'success') . '">' .
                        number_format($prod['current_stock']) .
                    '</span>
                </td>
                <td>' . number_format($prod['min_stock_level']) . '</td>
                <td>
                    <span class="badge bg-' .
                        ($prod['current_stock'] <= 0 ? 'danger">نفد المخزون' :
                         ($prod['current_stock'] <= $prod['min_stock_level'] ? 'warning">مخزون منخفض' : 'success">متوفر')) .
                    '</span>
                </td>
                <td>
                    <div class="d-flex gap-2">
                        <div class="text-center">
                            <button type="button" class="btn btn-outline-primary btn-sm"
                                    onclick="editProduct(' . htmlspecialchars(json_encode($prod)) . ')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <small class="d-block text-muted mt-1">تعديل</small>
                        </div>
                        <div class="text-center">
                            <button type="button" class="btn btn-outline-danger btn-sm"
                                    onclick="deleteProduct(' . $prod['id'] . ', \'' . htmlspecialchars($prod['product_name']) . '\')">
                                <i class="fas fa-trash"></i>
                            </button>
                            <small class="d-block text-muted mt-1">حذف</small>
                        </div>
                    </div>
                </td>
              </tr>';
    }
}
?>
