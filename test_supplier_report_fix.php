<?php
/**
 * SeaSystem - اختبار إصلاح تقرير الموردين
 * Test Supplier Report Fix
 */

session_start();

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'admin';
    $_SESSION['full_name'] = 'مدير النظام';
    $_SESSION['role'] = 'admin';
    $_SESSION['login_time'] = time();
    $_SESSION['last_activity'] = time();
}

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/classes/Supplier.php';
require_once __DIR__ . '/classes/Invoice.php';

echo "<h1>🔧 اختبار إصلاح تقرير الموردين</h1>";
echo "<hr>";

try {
    // إنشاء كائن المورد
    $supplier = new Supplier();
    echo "✅ تم إنشاء كائن Supplier بنجاح<br>";
    
    // اختبار الدالة الجديدة
    $filters = [
        'status' => '',
        'search' => ''
    ];
    
    echo "<h3>🧪 اختبار دالة getSuppliersReport()</h3>";
    $suppliers_data = $supplier->getSuppliersReport($filters);
    echo "✅ تم استدعاء دالة getSuppliersReport() بنجاح<br>";
    echo "📊 عدد الموردين المسترجعة: " . count($suppliers_data) . "<br>";
    
    // عرض بعض البيانات
    if (!empty($suppliers_data)) {
        echo "<h4>📋 عينة من البيانات:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>الرمز</th>";
        echo "<th style='padding: 8px;'>الاسم</th>";
        echo "<th style='padding: 8px;'>البريد</th>";
        echo "<th style='padding: 8px;'>الحالة</th>";
        echo "</tr>";
        
        foreach (array_slice($suppliers_data, 0, 5) as $supplier_data) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($supplier_data['supplier_code']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($supplier_data['name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($supplier_data['email'] ?? 'غير محدد') . "</td>";
            echo "<td style='padding: 8px;'>" . ($supplier_data['is_active'] ? 'نشط' : 'غير نشط') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>⚠️ لا توجد بيانات موردين في النظام</p>";
    }
    
    // اختبار الفلاتر
    echo "<h3>🔍 اختبار الفلاتر</h3>";
    
    // فلتر الموردين النشطين
    $active_filters = ['status' => 'active'];
    $active_suppliers = $supplier->getSuppliersReport($active_filters);
    echo "✅ الموردين النشطين: " . count($active_suppliers) . "<br>";
    
    // فلتر البحث
    if (!empty($suppliers_data)) {
        $search_filters = ['search' => $suppliers_data[0]['name']];
        $search_results = $supplier->getSuppliersReport($search_filters);
        echo "✅ نتائج البحث: " . count($search_results) . "<br>";
    }
    
    echo "<hr>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>🎉 تم إصلاح الخطأ بنجاح!</h4>";
    echo "<p style='margin: 0; color: #155724;'>دالة getSuppliersReport() تعمل الآن بشكل صحيح.</p>";
    echo "</div>";
    
    echo "<br><h3>🔗 اختبار صفحة التقرير الأصلية</h3>";
    echo "<p>يمكنك الآن الوصول إلى تقرير الموردين:</p>";
    echo "<a href='report_suppliers.php' class='btn btn-primary' target='_blank'>فتح تقرير الموردين</a>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h4 style='color: #721c24; margin: 0 0 10px 0;'>❌ خطأ في الاختبار</h4>";
    echo "<p style='margin: 0; color: #721c24;'>حدث خطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<br><a href='dashboard.php' class='btn btn-success'>العودة للوحة التحكم</a>";
echo " <a href='suppliers.php' class='btn btn-info'>إدارة الموردين</a>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 900px;
    margin: 30px auto;
    padding: 20px;
    background: #f8f9fa;
    direction: rtl;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 500;
    color: white;
}

.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn-info { background: #17a2b8; }
.btn:hover { opacity: 0.9; text-decoration: none; }

h1 { color: #343a40; }
h3 { color: #495057; }
h4 { color: #6c757d; }

table {
    font-size: 0.9rem;
}

th {
    background: #e9ecef !important;
    font-weight: 600;
}
</style>
