<?php
/**
 * SeaSystem - نظام المصادقة والأمان
 * Authentication and Security System
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/csrf.php';
require_once __DIR__ . '/rate_limiter.php';
require_once __DIR__ . '/security.php';

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

class Auth {
    private $db;
    private $rateLimiter;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->rateLimiter = new RateLimiter();
    }

    /**
     * تسجيل دخول المستخدم
     */
    public function login($username, $password) {
        try {
            // تنظيف المدخلات
            $username = Security::cleanInput($username);

            // التحقق من Rate Limiting
            $rateLimitCheck = $this->rateLimiter->canAttemptLogin($username);
            if (!$rateLimitCheck['allowed']) {
                Security::logSecurityEvent('login_blocked', "Username: $username, Blocked until: {$rateLimitCheck['blocked_until']}");
                return [
                    'success' => false,
                    'message' => 'تم حظر المحاولات مؤقتاً. المحاولة التالية بعد: ' . $rateLimitCheck['blocked_until'],
                    'blocked' => true
                ];
            }

            $sql = "SELECT id, username, email, password, full_name, role, is_active
                    FROM users
                    WHERE (username = :username OR email = :username) AND is_active = 1";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':username', $username);
            $stmt->execute();

            if ($stmt->rowCount() == 1) {
                $user = $stmt->fetch();

                // تسجيل دخول مؤقت بدون كلمة مرور (للتطوير فقط)
                // TODO: إعادة تفعيل التحقق من كلمة المرور عند الانتهاء من التطوير

                // مسح محاولات الدخول الفاشلة
                $this->rateLimiter->clearAttempts($username);

                // إنشاء جلسة المستخدم
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['login_time'] = time();
                $_SESSION['last_activity'] = time();

                // تسجيل عملية الدخول
                $this->logActivity($user['id'], 'login', 'تسجيل دخول مؤقت (وضع التطوير)');
                Security::logSecurityEvent('login_success_dev', "User: {$user['username']} (Development Mode)", $user['id']);

                return [
                    'success' => true,
                    'message' => 'تم تسجيل الدخول بنجاح (وضع التطوير)',
                    'user' => $user
                ];
            } else {
                // تسجيل محاولة فاشلة
                $this->rateLimiter->recordFailedAttempt($username);
                Security::logSecurityEvent('login_failed', "Username: $username, Reason: User not found");

                return [
                    'success' => false,
                    'message' => 'اسم المستخدم غير موجود'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }

    /**
     * تسجيل خروج المستخدم
     */
    public function logout() {
        if (isset($_SESSION['user_id'])) {
            $this->logActivity($_SESSION['user_id'], 'logout', 'تسجيل خروج');
        }

        session_unset();
        session_destroy();

        return [
            'success' => true,
            'message' => 'تم تسجيل الخروج بنجاح'
        ];
    }

    /**
     * التحقق من تسجيل الدخول
     */
    public function isLoggedIn() {
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['last_activity'])) {
            return false;
        }

        // التحقق من انتهاء صلاحية الجلسة
        if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
            $this->logout();
            return false;
        }

        // تحديث وقت آخر نشاط
        $_SESSION['last_activity'] = time();

        return true;
    }

    /**
     * التحقق من صلاحيات المستخدم
     */
    public function hasPermission($required_role) {
        if (!$this->isLoggedIn()) {
            return false;
        }

        $user_role = $_SESSION['role'];

        // ترتيب الأدوار حسب الصلاحيات
        $roles_hierarchy = [
            'user' => 1,
            'accountant' => 2,
            'admin' => 3
        ];

        $user_level = $roles_hierarchy[$user_role] ?? 0;
        $required_level = $roles_hierarchy[$required_role] ?? 0;

        return $user_level >= $required_level;
    }

    /**
     * الحصول على بيانات المستخدم الحالي
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }

        try {
            $sql = "SELECT id, username, email, full_name, role, created_at
                    FROM users
                    WHERE id = :user_id AND is_active = 1";

            $stmt = $this->db->prepare($sql);
            $user_id = $_SESSION['user_id'];
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();

            return $stmt->fetch();
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * تغيير كلمة المرور
     */
    public function changePassword($user_id, $old_password, $new_password) {
        try {
            // التحقق من كلمة المرور القديمة
            $sql = "SELECT password FROM users WHERE id = :user_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();

            $user = $stmt->fetch();

            if (!password_verify($old_password, $user['password'])) {
                return [
                    'success' => false,
                    'message' => 'كلمة المرور القديمة غير صحيحة'
                ];
            }

            // تحديث كلمة المرور
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);

            $sql = "UPDATE users SET password = :password, updated_at = NOW() WHERE id = :user_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':password', $hashed_password);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();

            $this->logActivity($user_id, 'password_change', 'تم تغيير كلمة المرور');

            return [
                'success' => true,
                'message' => 'تم تغيير كلمة المرور بنجاح'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في تغيير كلمة المرور: ' . $e->getMessage()
            ];
        }
    }

    /**
     * تسجيل أنشطة المستخدم
     */
    private function logActivity($user_id, $action, $description) {
        try {
            $sql = "INSERT INTO user_activities (user_id, action, description, ip_address, user_agent, created_at)
                    VALUES (:user_id, :action, :description, :ip_address, :user_agent, NOW())";

            $stmt = $this->db->prepare($sql);
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':action', $action);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':ip_address', $ip_address);
            $stmt->bindParam(':user_agent', $user_agent);
            $stmt->execute();
        } catch (Exception $e) {
            // تجاهل أخطاء تسجيل الأنشطة لعدم تعطيل النظام
            error_log("خطأ في تسجيل النشاط: " . $e->getMessage());
        }
    }

    /**
     * حماية الصفحات - إعادة توجيه غير المسجلين
     */
    public function requireLogin($redirect_url = 'login.php') {
        if (!$this->isLoggedIn()) {
            header("Location: $redirect_url");
            exit();
        }
    }

    /**
     * حماية الصفحات بصلاحيات معينة
     */
    public function requirePermission($required_role, $redirect_url = 'dashboard.php') {
        $this->requireLogin();

        if (!$this->hasPermission($required_role)) {
            header("Location: $redirect_url?error=no_permission");
            exit();
        }
    }
}

// إنشاء كائن المصادقة العام
$auth = new Auth();

/**
 * دوال مساعدة للمصادقة
 */

function isLoggedIn() {
    global $auth;
    return $auth->isLoggedIn();
}

function getCurrentUser() {
    global $auth;
    return $auth->getCurrentUser();
}

function hasPermission($role) {
    global $auth;
    return $auth->hasPermission($role);
}

function requireLogin($redirect = 'login.php') {
    global $auth;
    $auth->requireLogin($redirect);
}

function requirePermission($role, $redirect = 'dashboard.php') {
    global $auth;
    $auth->requirePermission($role, $redirect);
}
?>
