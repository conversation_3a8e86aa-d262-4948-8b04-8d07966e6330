-- إنشاء جدول لتتبع الأرقام المحذوفة
-- Create table to track deleted numbers

CREATE TABLE IF NOT EXISTS deleted_numbers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    entity_type VARCHAR(50) NOT NULL,
    deleted_number INT NOT NULL,
    original_code VARCHAR(50) NOT NULL,
    deleted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_by INT,
    reason VARCHAR(255),
    INDEX idx_entity_type (entity_type),
    INDEX idx_deleted_number (deleted_number),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة بيانات تجريبية للاختبار
INSERT INTO deleted_numbers (entity_type, deleted_number, original_code, reason) VALUES
('customer', 3, 'CUS003', 'حذف عميل غير نشط'),
('customer', 7, 'CUS007', 'عميل مكرر'),
('supplier', 2, 'SUP002', 'مورد لم يعد يتعامل معنا'),
('product', 5, 'PRD005', 'منتج متوقف');
