<?php
/**
 * SeaSystem - تقرير كشف حساب العميل
 * Customer Statement Report
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/FinancialReport.php';
require_once __DIR__ . '/classes/Customer.php';

// التأكد من تسجيل الدخول
requireLogin();

$financial_report = new FinancialReport();
$customer_class = new Customer();
$current_user = getCurrentUser();

// الحصول على معرف العميل
$customer_id = $_GET['customer_id'] ?? 0;
$date_from = $_GET['date_from'] ?? date('Y-01-01');
$date_to = $_GET['date_to'] ?? date('Y-m-d');

// الحصول على قائمة العملاء
$customers = $customer_class->getAll();

// الحصول على كشف حساب العميل
$statement = null;
if ($customer_id) {
    $statement = $financial_report->getCustomerStatement($customer_id, $date_from, $date_to);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>كشف حساب العميل - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <style>
        .report-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .customer-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-left: 4px solid #6f42c1;
        }

        .balance-summary {
            position: fixed;
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
            z-index: 1000;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            padding: 1rem;
            min-width: 200px;
        }

        .debit-amount {
            color: #dc3545;
        }

        .credit-amount {
            color: #28a745;
        }

        .running-balance {
            font-weight: bold;
        }

        .positive-balance {
            color: #dc3545;
        }

        .negative-balance {
            color: #28a745;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            .balance-summary {
                position: static;
                transform: none;
                margin-bottom: 1rem;
            }

            .report-header {
                background: #f8f9fa !important;
                color: #333 !important;
                border: 2px solid #dee2e6;
            }
        }
    </style>

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }
        
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <!-- ملخص الرصيد -->
    <?php if ($statement && $statement['customer']): ?>
        <div class="balance-summary no-print">
            <div class="text-center">
                <i class="fas fa-user fa-2x text-primary"></i>
                <h6 class="mt-2 mb-1">ملخص الحساب</h6>

                <div class="small mb-2">
                    <div class="d-flex justify-content-between">
                        <span>إجمالي المدين:</span>
                        <strong class="debit-amount"><?php echo number_format($statement['total_debit'], 0); ?></strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>إجمالي الدائن:</span>
                        <strong class="credit-amount"><?php echo number_format($statement['total_credit'], 0); ?></strong>
                    </div>
                    <hr class="my-1">
                    <div class="d-flex justify-content-between">
                        <span>الرصيد النهائي:</span>
                        <strong class="<?php echo $statement['final_balance'] >= 0 ? 'positive-balance' : 'negative-balance'; ?>">
                            <?php echo number_format(abs($statement['final_balance']), 0); ?>
                        </strong>
                    </div>
                </div>

                <span class="badge <?php echo $statement['final_balance'] >= 0 ? 'bg-danger' : 'bg-success'; ?>">
                    <?php echo $statement['final_balance'] >= 0 ? 'مدين' : 'دائن'; ?>
                </span>
            </div>
        </div>
    <?php endif; ?>

    <div class="container-fluid">
        <div class="row">
            <!-- المحتوى الرئيسي -->
            <div class="col-12 p-4">
                <!-- أزرار الإجراءات -->
                <div class="row mb-3 no-print">
                    <div class="col">
                        <a href="reports.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للتقارير
                        </a>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.print()">
                                <i class="fas fa-print me-2"></i>طباعة
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-2"></i>تصدير Excel
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                            </button>
                        </div>
                    </div>
                </div>

                <!-- رأس التقرير -->
                <div class="report-header">
                    <h1 class="mb-2">
                        <i class="fas fa-user me-3"></i>كشف حساب العميل
                    </h1>
                    <h4 class="mb-3">Customer Statement</h4>
                    <?php if ($statement && $statement['customer']): ?>
                        <p class="mb-0">
                            من <?php echo date('d/m/Y', strtotime($statement['date_from'])); ?>
                            إلى <?php echo date('d/m/Y', strtotime($statement['date_to'])); ?>
                        </p>
                    <?php endif; ?>
                    <small class="opacity-75">تاريخ الإنشاء: <?php echo date('d/m/Y H:i'); ?></small>
                </div>

                <!-- اختيار العميل والتواريخ -->
                <div class="card mb-4 no-print">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="customer_id" class="form-label">اختر العميل *</label>
                                <select class="form-select" id="customer_id" name="customer_id" required>
                                    <option value="">اختر العميل</option>
                                    <?php foreach ($customers as $customer): ?>
                                        <option value="<?php echo $customer['id']; ?>"
                                                <?php echo $customer_id == $customer['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($customer['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="date_from" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from"
                                       value="<?php echo $date_from; ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="date_to" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to"
                                       value="<?php echo $date_to; ?>">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>عرض الكشف
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <?php if ($statement && $statement['customer']): ?>
                    <!-- معلومات العميل -->
                    <div class="customer-info">
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="mb-3">
                                    <i class="fas fa-user me-2"></i>معلومات العميل
                                </h5>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>الاسم:</strong></div>
                                    <div class="col-8"><?php echo htmlspecialchars($statement['customer']['name']); ?></div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>رمز العميل:</strong></div>
                                    <div class="col-8"><?php echo htmlspecialchars($statement['customer']['customer_code']); ?></div>
                                </div>
                                <?php if ($statement['customer']['email']): ?>
                                    <div class="row mb-2">
                                        <div class="col-4"><strong>البريد الإلكتروني:</strong></div>
                                        <div class="col-8"><?php echo htmlspecialchars($statement['customer']['email']); ?></div>
                                    </div>
                                <?php endif; ?>
                                <?php if ($statement['customer']['phone']): ?>
                                    <div class="row mb-2">
                                        <div class="col-4"><strong>الهاتف:</strong></div>
                                        <div class="col-8"><?php echo htmlspecialchars($statement['customer']['phone']); ?></div>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <h5 class="mb-3">
                                    <i class="fas fa-chart-line me-2"></i>ملخص الحساب
                                </h5>
                                <div class="row mb-2">
                                    <div class="col-6"><strong>إجمالي المدين:</strong></div>
                                    <div class="col-6 text-end debit-amount">
                                        <strong><?php echo number_format($statement['total_debit'], 2) . ' ' . CURRENCY_SYMBOL; ?></strong>
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-6"><strong>إجمالي الدائن:</strong></div>
                                    <div class="col-6 text-end credit-amount">
                                        <strong><?php echo number_format($statement['total_credit'], 2) . ' ' . CURRENCY_SYMBOL; ?></strong>
                                    </div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-6"><strong>الرصيد النهائي:</strong></div>
                                    <div class="col-6 text-end">
                                        <strong class="<?php echo $statement['final_balance'] >= 0 ? 'positive-balance' : 'negative-balance'; ?>">
                                            <?php echo number_format(abs($statement['final_balance']), 2) . ' ' . CURRENCY_SYMBOL; ?>
                                            (<?php echo $statement['final_balance'] >= 0 ? 'مدين' : 'دائن'; ?>)
                                        </strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول المعاملات -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2"></i>تفاصيل المعاملات
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>المرجع</th>
                                            <th>الوصف</th>
                                            <th class="text-end">مدين</th>
                                            <th class="text-end">دائن</th>
                                            <th class="text-end">الرصيد</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($statement['transactions'])): ?>
                                            <tr>
                                                <td colspan="6" class="text-center py-4">
                                                    <i class="fas fa-exclamation-triangle fa-2x text-muted mb-2"></i>
                                                    <p class="text-muted mb-0">لا توجد معاملات للفترة المحددة</p>
                                                </td>
                                            </tr>
                                        <?php else: ?>
                                            <?php foreach ($statement['transactions'] as $transaction): ?>
                                                <tr>
                                                    <td><?php echo date('d/m/Y', strtotime($transaction['date'])); ?></td>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($transaction['reference']); ?></strong>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($transaction['description']); ?></td>
                                                    <td class="text-end">
                                                        <?php if ($transaction['debit'] > 0): ?>
                                                            <span class="debit-amount">
                                                                <strong><?php echo number_format($transaction['debit'], 2); ?></strong>
                                                            </span>
                                                        <?php else: ?>
                                                            <span class="text-muted">-</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="text-end">
                                                        <?php if ($transaction['credit'] > 0): ?>
                                                            <span class="credit-amount">
                                                                <strong><?php echo number_format($transaction['credit'], 2); ?></strong>
                                                            </span>
                                                        <?php else: ?>
                                                            <span class="text-muted">-</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="text-end">
                                                        <span class="running-balance <?php echo $transaction['balance'] >= 0 ? 'positive-balance' : 'negative-balance'; ?>">
                                                            <?php echo number_format(abs($transaction['balance']), 2); ?>
                                                            <?php echo $transaction['balance'] >= 0 ? 'مدين' : 'دائن'; ?>
                                                        </span>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>

                                    <?php if (!empty($statement['transactions'])): ?>
                                        <tfoot class="table-secondary">
                                            <tr>
                                                <td colspan="3"><strong>الإجمالي:</strong></td>
                                                <td class="text-end">
                                                    <strong class="debit-amount"><?php echo number_format($statement['total_debit'], 2); ?></strong>
                                                </td>
                                                <td class="text-end">
                                                    <strong class="credit-amount"><?php echo number_format($statement['total_credit'], 2); ?></strong>
                                                </td>
                                                <td class="text-end">
                                                    <strong class="<?php echo $statement['final_balance'] >= 0 ? 'positive-balance' : 'negative-balance'; ?>">
                                                        <?php echo number_format(abs($statement['final_balance']), 2); ?>
                                                        <?php echo $statement['final_balance'] >= 0 ? 'مدين' : 'دائن'; ?>
                                                    </strong>
                                                </td>
                                            </tr>
                                        </tfoot>
                                    <?php endif; ?>
                                </table>
                            </div>
                        </div>
                    </div>

                <?php elseif ($customer_id): ?>
                    <!-- رسالة عدم وجود بيانات -->
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                            <h4>لا توجد بيانات</h4>
                            <p class="text-muted">لم يتم العثور على معاملات للعميل المحدد في الفترة المطلوبة</p>
                        </div>
                    </div>

                <?php else: ?>
                    <!-- رسالة اختيار العميل -->
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
                            <h4>اختر العميل</h4>
                            <p class="text-muted">يرجى اختيار العميل من القائمة أعلاه لعرض كشف الحساب</p>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- ملاحظات -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-info-circle me-2"></i>ملاحظات
                        </h6>
                        <ul class="mb-0">
                            <li>كشف الحساب يعرض جميع معاملات العميل خلال الفترة المحددة</li>
                            <li>المدين يمثل المبالغ المستحقة على العميل (الفواتير)</li>
                            <li>الدائن يمثل المبالغ المدفوعة من العميل (المقبوضات)</li>
                            <li>الرصيد المدين يعني أن العميل مدين للشركة</li>
                            <li>الرصيد الدائن يعني أن الشركة مدينة للعميل</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function exportToExcel() {
            const customerId = document.querySelector('select[name="customer_id"]').value;
            if (!customerId) {
                alert('يرجى اختيار عميل أولاً');
                return;
            }
            window.open(`export_handler.php?type=customer_statement&format=excel&customer_id=${customerId}`, '_blank');
        }

        function exportToPDF() {
            const customerId = document.querySelector('select[name="customer_id"]').value;
            if (!customerId) {
                alert('يرجى اختيار عميل أولاً');
                return;
            }
            window.open(`export_handler.php?type=customer_statement&format=pdf&customer_id=${customerId}`, '_blank');
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
