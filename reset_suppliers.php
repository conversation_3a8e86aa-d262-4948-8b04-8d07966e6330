<?php
/**
 * ملف لحذف البيانات التجريبية وإضافة البيانات الجديدة
 */

require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/classes/Supplier.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // حذف جميع الموردين الموجودين
    $sql = "DELETE FROM suppliers";
    $db->exec($sql);
    
    echo "تم حذف جميع الموردين القدامى<br>";
    
    // إضافة الموردين الجدد
    $supplier = new Supplier();
    
    $suppliers_data = [
        [
            'supplier_code' => 'SUP001',
            'name' => 'شركة الأهرام للتجارة',
            'email' => '<EMAIL>',
            'phone' => '0*********0',
            'address' => 'القاهرة، مصر',
            'tax_number' => '*********',
            'opening_balance' => 10000.00
        ],
        [
            'supplier_code' => 'SUP002',
            'name' => 'مؤسسة النيل للمواد',
            'email' => '<EMAIL>',
            'phone' => '0*********1',
            'address' => 'الجيزة، مصر',
            'tax_number' => '*********',
            'opening_balance' => 15000.00
        ],
        [
            'supplier_code' => 'SUP003',
            'name' => 'شركة الدلتا التجارية',
            'email' => '<EMAIL>',
            'phone' => '0*********2',
            'address' => 'الإسكندرية، مصر',
            'tax_number' => '*********',
            'opening_balance' => 8000.00
        ],
        [
            'supplier_code' => 'SUP004',
            'name' => 'مجموعة الخليج للاستيراد',
            'email' => '<EMAIL>',
            'phone' => '0*********3',
            'address' => 'دبي، الإمارات',
            'tax_number' => '*********',
            'opening_balance' => 25000.00
        ],
        [
            'supplier_code' => 'SUP005',
            'name' => 'شركة المتوسط للتوريدات',
            'email' => '<EMAIL>',
            'phone' => '0*********4',
            'address' => 'بيروت، لبنان',
            'tax_number' => '*********',
            'opening_balance' => 12000.00
        ]
    ];
    
    foreach ($suppliers_data as $data) {
        $result = $supplier->create($data);
        if ($result['success']) {
            echo "تم إضافة المورد: " . $data['name'] . "<br>";
        } else {
            echo "فشل في إضافة المورد: " . $data['name'] . " - " . $result['message'] . "<br>";
        }
    }
    
    echo "<br>تم الانتهاء من إضافة جميع الموردين!<br>";
    echo '<a href="suppliers.php">انتقل لصفحة الموردين</a>';
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
