<?php
/**
 * SeaSystem - تقرير المخزون
 * Inventory Report
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/Inventory.php';

// التأكد من تسجيل الدخول
requireLogin();

$inventory = new Inventory();
$current_user = getCurrentUser();

// الحصول على المرشحات
$filters = [];
if (!empty($_GET['category_id'])) {
    $filters['category_id'] = $_GET['category_id'];
}
if (!empty($_GET['search'])) {
    $filters['search'] = $_GET['search'];
}
if (isset($_GET['low_stock']) && $_GET['low_stock']) {
    $filters['low_stock'] = true;
}

// الحصول على البيانات
$products = $inventory->getAllProducts($filters);
$categories = $inventory->getProductCategories();
$stats = $inventory->getInventoryStatistics();
$low_stock_products = $inventory->getLowStockProducts();

// حساب إحصائيات إضافية
$total_products = count($products);
$total_value = array_sum(array_column($products, 'stock_value'));
$out_of_stock = count(array_filter($products, function($p) { return $p['current_stock'] <= 0; }));
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المخزون - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <style>
        .report-header {
            background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .inventory-summary {
            position: fixed;
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
            z-index: 1000;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            padding: 1rem;
            min-width: 250px;
        }

        .low-stock-row {
            background-color: #fff3cd;
        }

        .out-of-stock-row {
            background-color: #f8d7da;
        }

        .stock-indicator {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }

        .stock-level {
            height: 100%;
            transition: width 0.3s ease;
        }

        .stock-good {
            background: #28a745;
        }

        .stock-warning {
            background: #ffc107;
        }

        .stock-danger {
            background: #dc3545;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            .inventory-summary {
                position: static;
                transform: none;
                margin-bottom: 1rem;
            }

            .report-header {
                background: #f8f9fa !important;
                color: #333 !important;
                border: 2px solid #dee2e6;
            }
        }
    </style>

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }
        
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <!-- ملخص المخزون -->
    <div class="inventory-summary no-print">
        <div class="text-center">
            <i class="fas fa-boxes fa-2x text-primary"></i>
            <h6 class="mt-2 mb-1">ملخص المخزون</h6>

            <div class="small mb-2">
                <div class="d-flex justify-content-between">
                    <span>إجمالي المنتجات:</span>
                    <strong><?php echo number_format($total_products); ?></strong>
                </div>
                <div class="d-flex justify-content-between">
                    <span>منتجات متوفرة:</span>
                    <strong class="text-success"><?php echo number_format($stats['products_in_stock']); ?></strong>
                </div>
                <div class="d-flex justify-content-between">
                    <span>مخزون منخفض:</span>
                    <strong class="text-warning"><?php echo number_format($stats['low_stock_products']); ?></strong>
                </div>
                <div class="d-flex justify-content-between">
                    <span>نفد المخزون:</span>
                    <strong class="text-danger"><?php echo number_format($out_of_stock); ?></strong>
                </div>
                <hr class="my-1">
                <div class="d-flex justify-content-between">
                    <span>قيمة المخزون:</span>
                    <strong><?php echo number_format($total_value, 0); ?></strong>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- المحتوى الرئيسي -->
            <div class="col-12 p-4">
                <!-- أزرار الإجراءات -->
                <div class="row mb-3 no-print">
                    <div class="col">
                        <a href="inventory.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للمخزون
                        </a>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.print()">
                                <i class="fas fa-print me-2"></i>طباعة
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-2"></i>تصدير Excel
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                            </button>
                        </div>
                    </div>
                </div>

                <!-- رأس التقرير -->
                <div class="report-header">
                    <h1 class="mb-2">
                        <i class="fas fa-boxes me-3"></i>تقرير المخزون
                    </h1>
                    <h4 class="mb-3">Inventory Report</h4>
                    <p class="mb-0">تقرير شامل لحالة المخزون والمنتجات</p>
                    <small class="opacity-75">تاريخ الإنشاء: <?php echo date('d/m/Y H:i'); ?></small>
                </div>

                <!-- فلترة البيانات -->
                <div class="card mb-4 no-print">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       placeholder="البحث في المنتجات..."
                                       value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="category_id" class="form-label">الفئة</label>
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value="">جميع الفئات</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>"
                                                <?php echo ($_GET['category_id'] ?? '') == $category['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['category_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="low_stock" value="1"
                                           <?php echo isset($_GET['low_stock']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label">مخزون منخفض فقط</label>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>تطبيق
                                </button>
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <a href="report_inventory.php" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-redo"></i>
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-primary"><?php echo number_format($total_products); ?></h3>
                                <p class="card-text">إجمالي المنتجات</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-success"><?php echo number_format($stats['products_in_stock']); ?></h3>
                                <p class="card-text">منتجات متوفرة</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-warning"><?php echo number_format($stats['low_stock_products']); ?></h3>
                                <p class="card-text">مخزون منخفض</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-info"><?php echo number_format($total_value, 0) . ' ' . CURRENCY_SYMBOL; ?></h3>
                                <p class="card-text">قيمة المخزون</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول المنتجات -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>تفاصيل المخزون (<?php echo count($products); ?> منتج)
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th>رمز المنتج</th>
                                        <th>اسم المنتج</th>
                                        <th>الفئة</th>
                                        <th>الوحدة</th>
                                        <th>المخزون الحالي</th>
                                        <th>مؤشر المخزون</th>
                                        <th>سعر التكلفة</th>
                                        <th>سعر البيع</th>
                                        <th>قيمة المخزون</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($products)): ?>
                                        <tr>
                                            <td colspan="10" class="text-center py-4">
                                                <i class="fas fa-exclamation-triangle fa-2x text-muted mb-2"></i>
                                                <p class="text-muted mb-0">لا توجد منتجات تطابق المعايير المحددة</p>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($products as $product): ?>
                                            <?php
                                            $stock_percentage = $product['max_stock_level'] > 0 ?
                                                ($product['current_stock'] / $product['max_stock_level']) * 100 : 0;

                                            $stock_class = 'stock-good';
                                            $row_class = '';
                                            $status = 'متوفر';
                                            $status_class = 'success';

                                            if ($product['current_stock'] <= 0) {
                                                $stock_class = 'stock-danger';
                                                $row_class = 'out-of-stock-row';
                                                $status = 'نفد المخزون';
                                                $status_class = 'danger';
                                            } elseif ($product['current_stock'] <= $product['min_stock_level']) {
                                                $stock_class = 'stock-warning';
                                                $row_class = 'low-stock-row';
                                                $status = 'مخزون منخفض';
                                                $status_class = 'warning';
                                            }
                                            ?>
                                            <tr class="<?php echo $row_class; ?>">
                                                <td>
                                                    <strong><?php echo htmlspecialchars($product['product_code']); ?></strong>
                                                    <?php if ($product['barcode']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($product['barcode']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="fw-bold"><?php echo htmlspecialchars($product['product_name']); ?></div>
                                                    <?php if ($product['description']): ?>
                                                        <small class="text-muted"><?php echo htmlspecialchars(substr($product['description'], 0, 50)); ?>...</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <?php echo htmlspecialchars($product['category_name']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars($product['unit_name']); ?></td>
                                                <td>
                                                    <span class="fw-bold <?php echo $product['current_stock'] <= 0 ? 'text-danger' : ($product['current_stock'] <= $product['min_stock_level'] ? 'text-warning' : 'text-success'); ?>">
                                                        <?php echo number_format($product['current_stock'], 2); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="stock-indicator">
                                                        <div class="stock-level <?php echo $stock_class; ?>"
                                                             style="width: <?php echo min(100, $stock_percentage); ?>%"></div>
                                                    </div>
                                                    <small class="text-muted">
                                                        الحد الأدنى: <?php echo $product['min_stock_level']; ?>
                                                    </small>
                                                </td>
                                                <td><?php echo number_format($product['cost_price'], 2) . ' ' . CURRENCY_SYMBOL; ?></td>
                                                <td><?php echo number_format($product['selling_price'], 2) . ' ' . CURRENCY_SYMBOL; ?></td>
                                                <td>
                                                    <strong><?php echo number_format($product['stock_value'], 2) . ' ' . CURRENCY_SYMBOL; ?></strong>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo $status_class; ?>">
                                                        <?php echo $status; ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>

                                <?php if (!empty($products)): ?>
                                    <tfoot class="table-secondary">
                                        <tr>
                                            <td colspan="8"><strong>الإجمالي:</strong></td>
                                            <td>
                                                <strong><?php echo number_format($total_value, 2) . ' ' . CURRENCY_SYMBOL; ?></strong>
                                            </td>
                                            <td>
                                                <strong><?php echo count($products); ?> منتج</strong>
                                            </td>
                                        </tr>
                                    </tfoot>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- ملاحظات -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-info-circle me-2"></i>ملاحظات
                        </h6>
                        <ul class="mb-0">
                            <li>التقرير يعرض حالة المخزون الحالية لجميع المنتجات النشطة</li>
                            <li>المنتجات ذات الخلفية الصفراء تحتاج إعادة طلب (مخزون منخفض)</li>
                            <li>المنتجات ذات الخلفية الحمراء نفد مخزونها</li>
                            <li>قيمة المخزون محسوبة بناءً على سعر التكلفة × الكمية المتوفرة</li>
                            <li>مؤشر المخزون يعرض النسبة المئوية للمخزون الحالي مقارنة بالحد الأقصى</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function exportToExcel() {
            alert('سيتم إضافة وظيفة تصدير Excel قريباً');
        }

        function exportToPDF() {
            alert('سيتم إضافة وظيفة تصدير PDF قريباً');
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
