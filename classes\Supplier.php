<?php
/**
 * SeaSystem - فئة إدارة الموردين
 * Supplier Management Class
 */

require_once __DIR__ . '/../config/database.php';

class Supplier {
    private $db;
    private $table_name = "suppliers";

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->createTableIfNotExist();
    }

    /**
     * إنشاء جدول الموردين إذا لم يكن موجوداً
     */
    private function createTableIfNotExist() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS suppliers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                supplier_code VARCHAR(50) UNIQUE NOT NULL,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100),
                phone VARCHAR(20),
                address TEXT,
                tax_number VARCHAR(50),
                opening_balance DECIMAL(15,2) DEFAULT 0.00,
                current_balance DECIMAL(15,2) DEFAULT 0.00,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )";
            $this->db->exec($sql);

        } catch (Exception $e) {
            // تجاهل الأخطاء إذا كان الجدول موجوداً
        }
    }

    /**
     * الحصول على جميع الموردين
     */
    public function getAll() {
        try {
            $sql = "SELECT * FROM " . $this->table_name . " WHERE is_active = 1 ORDER BY name";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على مورد واحد
     */
    public function getById($supplier_id) {
        try {
            $sql = "SELECT * FROM " . $this->table_name . " WHERE id = :supplier_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':supplier_id', $supplier_id);
            $stmt->execute();
            return $stmt->fetch();
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * البحث في الموردين
     */
    public function search($keyword) {
        try {
            $sql = "SELECT * FROM " . $this->table_name . "
                    WHERE (supplier_code LIKE :keyword OR name LIKE :keyword OR email LIKE :keyword)
                    AND is_active = 1
                    ORDER BY name";
            $stmt = $this->db->prepare($sql);
            $keyword = "%$keyword%";
            $stmt->bindParam(':keyword', $keyword);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * إضافة مورد جديد
     */
    public function create($data) {
        try {
            // التحقق من عدم تكرار رمز المورد
            if ($this->isSupplierCodeExists($data['supplier_code'])) {
                return ['success' => false, 'message' => 'رمز المورد "' . $data['supplier_code'] . '" موجود مسبقاً'];
            }

            // التحقق من عدم تكرار اسم المورد
            if ($this->isSupplierNameExists($data['name'])) {
                return ['success' => false, 'message' => 'اسم المورد "' . $data['name'] . '" موجود مسبقاً'];
            }

            // التحقق من عدم تكرار البريد الإلكتروني
            if (!empty($data['email']) && $this->isEmailExists($data['email'])) {
                return ['success' => false, 'message' => 'البريد الإلكتروني "' . $data['email'] . '" موجود مسبقاً'];
            }

            // التحقق من عدم تكرار رقم الهاتف
            if (!empty($data['phone']) && $this->isPhoneExists($data['phone'])) {
                return ['success' => false, 'message' => 'رقم الهاتف "' . $data['phone'] . '" موجود مسبقاً'];
            }

            // التحقق من عدم تكرار الرقم الضريبي
            if (!empty($data['tax_number']) && $this->isTaxNumberExists($data['tax_number'])) {
                return ['success' => false, 'message' => 'الرقم الضريبي "' . $data['tax_number'] . '" موجود مسبقاً'];
            }

            $sql = "INSERT INTO " . $this->table_name . "
                    (supplier_code, name, email, phone, address, tax_number, opening_balance, current_balance)
                    VALUES (:supplier_code, :name, :email, :phone, :address, :tax_number, :opening_balance, :opening_balance)";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':supplier_code', $data['supplier_code']);
            $stmt->bindParam(':name', $data['name']);
            $stmt->bindParam(':email', $data['email']);
            $stmt->bindParam(':phone', $data['phone']);
            $stmt->bindParam(':address', $data['address']);
            $stmt->bindParam(':tax_number', $data['tax_number']);
            $stmt->bindParam(':opening_balance', $data['opening_balance']);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم إضافة المورد بنجاح',
                    'supplier_id' => $this->db->lastInsertId()
                ];
            } else {
                return ['success' => false, 'message' => 'فشل في إضافة المورد'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()];
        }
    }

    /**
     * تحديث بيانات المورد
     */
    public function update($id, $data) {
        try {
            // التحقق من وجود المورد
            if (!$this->exists($id)) {
                return ['success' => false, 'message' => 'المورد غير موجود'];
            }

            // التحقق من عدم تكرار رمز المورد
            if ($this->isSupplierCodeExists($data['supplier_code'], $id)) {
                return ['success' => false, 'message' => 'رمز المورد موجود مسبقاً'];
            }

            $sql = "UPDATE " . $this->table_name . "
                    SET supplier_code = :supplier_code, name = :name, email = :email,
                        phone = :phone, address = :address, tax_number = :tax_number,
                        opening_balance = :opening_balance, updated_at = NOW()
                    WHERE id = :id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':supplier_code', $data['supplier_code']);
            $stmt->bindParam(':name', $data['name']);
            $stmt->bindParam(':email', $data['email']);
            $stmt->bindParam(':phone', $data['phone']);
            $stmt->bindParam(':address', $data['address']);
            $stmt->bindParam(':tax_number', $data['tax_number']);
            $stmt->bindParam(':opening_balance', $data['opening_balance']);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                return ['success' => true, 'message' => 'تم تحديث بيانات المورد بنجاح'];
            } else {
                return ['success' => false, 'message' => 'فشل في تحديث بيانات المورد'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()];
        }
    }

    /**
     * حذف مورد
     */
    public function delete($id) {
        try {
            $sql = "UPDATE " . $this->table_name . " SET is_active = 0 WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                return ['success' => true, 'message' => 'تم حذف المورد بنجاح'];
            } else {
                return ['success' => false, 'message' => 'فشل في حذف المورد'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()];
        }
    }

    /**
     * التحقق من وجود رمز المورد
     */
    private function isSupplierCodeExists($supplier_code, $exclude_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE supplier_code = :supplier_code";
            if ($exclude_id) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':supplier_code', $supplier_code);
            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }
            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من وجود المورد
     */
    private function exists($id) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من تكرار اسم المورد
     */
    private function isSupplierNameExists($name, $exclude_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE LOWER(TRIM(name)) = LOWER(TRIM(:name))";
            if ($exclude_id) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':name', $name);
            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }
            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من تكرار البريد الإلكتروني
     */
    private function isEmailExists($email, $exclude_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE email = :email";
            if ($exclude_id) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':email', $email);
            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }
            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من تكرار رقم الهاتف
     */
    private function isPhoneExists($phone, $exclude_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE phone = :phone";
            if ($exclude_id) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':phone', $phone);
            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }
            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من تكرار الرقم الضريبي
     */
    private function isTaxNumberExists($tax_number, $exclude_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE tax_number = :tax_number";
            if ($exclude_id) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':tax_number', $tax_number);
            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }
            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    // دوال عامة للتحقق من التكرار (للاستخدام مع AJAX)
    public function checkSupplierCodeExists($supplier_code, $exclude_id = null) {
        return $this->isSupplierCodeExists($supplier_code, $exclude_id);
    }

    public function checkSupplierNameExists($name, $exclude_id = null) {
        return $this->isSupplierNameExists($name, $exclude_id);
    }

    public function checkEmailExists($email, $exclude_id = null) {
        return $this->isEmailExists($email, $exclude_id);
    }

    public function checkPhoneExists($phone, $exclude_id = null) {
        return $this->isPhoneExists($phone, $exclude_id);
    }

    public function checkTaxNumberExists($tax_number, $exclude_id = null) {
        return $this->isTaxNumberExists($tax_number, $exclude_id);
    }



    /**
     * التحقق من تكرار البريد الإلكتروني
     */
    private function isEmailExists($email, $exclude_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE email = :email";
            if ($exclude_id) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':email', $email);
            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }
            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من تكرار رقم الهاتف
     */
    private function isPhoneExists($phone, $exclude_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE phone = :phone";
            if ($exclude_id) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':phone', $phone);
            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }
            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * التحقق من تكرار الرقم الضريبي
     */
    private function isTaxNumberExists($tax_number, $exclude_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE tax_number = :tax_number";
            if ($exclude_id) {
                $sql .= " AND id != :exclude_id";
            }

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':tax_number', $tax_number);
            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }
            $stmt->execute();

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * الحصول على إجمالي قيمة المشتريات
     */
    public function getTotalPurchasesAmount() {
        try {
            // هذه دالة مؤقتة - ستحتاج لربطها بجدول المشتريات لاحقاً
            return 0.00;
        } catch (Exception $e) {
            return 0.00;
        }
    }

    /**
     * الحصول على إجمالي المبالغ المستحقة
     */
    public function getTotalOutstandingAmount() {
        try {
            $sql = "SELECT SUM(current_balance) FROM " . $this->table_name . " WHERE current_balance > 0 AND is_active = 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn() ?: 0.00;
        } catch (Exception $e) {
            return 0.00;
        }
    }

    /**
     * الحصول على إجمالي المدفوعات
     */
    public function getTotalPaidAmount() {
        try {
            // هذه دالة مؤقتة - ستحتاج لربطها بجدول المدفوعات لاحقاً
            return 0.00;
        } catch (Exception $e) {
            return 0.00;
        }
    }

    /**
     * الحصول على متوسط قيمة الطلب
     */
    public function getAverageOrderValue() {
        try {
            // هذه دالة مؤقتة - ستحتاج لربطها بجدول المشتريات لاحقاً
            return 0.00;
        } catch (Exception $e) {
            return 0.00;
        }
    }

    /**
     * الحصول على تقرير الموردين مع الفلاتر
     */
    public function getSuppliersReport($filters = []) {
        try {
            $sql = "SELECT * FROM " . $this->table_name . " WHERE 1=1";
            $params = [];

            // فلتر الحالة
            if (!empty($filters['status'])) {
                if ($filters['status'] === 'active') {
                    $sql .= " AND is_active = 1";
                } elseif ($filters['status'] === 'inactive') {
                    $sql .= " AND is_active = 0";
                }
            }

            // فلتر البحث
            if (!empty($filters['search'])) {
                $sql .= " AND (supplier_code LIKE :search OR name LIKE :search OR email LIKE :search)";
                $params[':search'] = '%' . $filters['search'] . '%';
            }

            // فلتر التاريخ
            if (!empty($filters['date_from'])) {
                $sql .= " AND created_at >= :date_from";
                $params[':date_from'] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $sql .= " AND created_at <= :date_to";
                $params[':date_to'] = $filters['date_to'] . ' 23:59:59';
            }

            $sql .= " ORDER BY name";

            $stmt = $this->db->prepare($sql);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }
}
?>
