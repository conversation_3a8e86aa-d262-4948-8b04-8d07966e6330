<?php
/**
 * SeaSystem - صفحة تعديل الفاتورة
 * Edit Invoice Page
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/Invoice.php';
require_once __DIR__ . '/classes/Customer.php';
require_once __DIR__ . '/classes/Supplier.php';
require_once __DIR__ . '/classes/Account.php';
require_once __DIR__ . '/classes/Product.php';

// التأكد من تسجيل الدخول
requireLogin();

// التحقق من وجود معرف الفاتورة
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: invoices.php?error=' . urlencode('معرف الفاتورة مطلوب'));
    exit();
}

$invoice_id = (int)$_GET['id'];

// إنشاء كائنات الفئات
$invoice = new Invoice();
$customer = new Customer();
$supplier = new Supplier();
$account = new Account();
$product = new Product();

// الحصول على بيانات المستخدم الحالي
$current_user = getCurrentUser();

// الحصول على بيانات الفاتورة
$invoice_data = $invoice->getById($invoice_id);

if (!$invoice_data) {
    header('Location: invoices.php?error=' . urlencode('الفاتورة غير موجودة'));
    exit();
}

// التحقق من إمكانية التعديل
if ($invoice_data['status'] == 'paid') {
    header('Location: invoice_view.php?id=' . $invoice_id . '&error=' . urlencode('لا يمكن تعديل فاتورة مدفوعة'));
    exit();
}

// الحصول على عناصر الفاتورة
$invoice_items = $invoice->getInvoiceItems($invoice_id);

$message = '';
$message_type = '';

// معالجة تحديث الفاتورة
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // بيانات الفاتورة الأساسية
    $updated_data = [
        'invoice_number' => $_POST['invoice_number'],
        'customer_id' => $invoice_data['invoice_type'] == 'sales' ? $_POST['customer_id'] : null,
        'supplier_id' => $invoice_data['invoice_type'] == 'purchase' ? $_POST['supplier_id'] : null,
        'invoice_date' => $_POST['invoice_date'],
        'due_date' => $_POST['due_date'],
        'subtotal' => $_POST['subtotal'],
        'tax_amount' => $_POST['tax_amount'] ?? 0,
        'discount_amount' => $_POST['discount_amount'] ?? 0,
        'total_amount' => $_POST['total_amount'],
        'notes' => $_POST['notes'] ?? ''
    ];
    
    // عناصر الفاتورة
    $items_data = [];
    if (!empty($_POST['item_description'])) {
        for ($i = 0; $i < count($_POST['item_description']); $i++) {
            if (!empty($_POST['item_description'][$i])) {
                $items_data[] = [
                    'item_description' => $_POST['item_description'][$i],
                    'quantity' => $_POST['quantity'][$i],
                    'unit_price' => $_POST['unit_price'][$i],
                    'total_price' => $_POST['item_total'][$i],
                    'account_id' => $_POST['account_id'][$i] ?? null
                ];
            }
        }
    }
    
    if (empty($items_data)) {
        $message = 'يجب إضافة عنصر واحد على الأقل للفاتورة';
        $message_type = 'danger';
    } else {
        $result = $invoice->update($invoice_id, $updated_data, $items_data);
        $message = $result['message'];
        $message_type = $result['success'] ? 'success' : 'danger';
        
        if ($result['success']) {
            // إعادة توجيه إلى صفحة عرض الفاتورة
            header('Location: invoice_view.php?id=' . $invoice_id . '&success=' . urlencode($message));
            exit();
        }
    }
}

// الحصول على البيانات المطلوبة
$customers = $customer->getAll();
$suppliers = $supplier->getAll();
$accounts = $account->getAll();
$products = $product->getAll();

// تحديد نوع الفاتورة
$invoice_type = $invoice_data['invoice_type'];
$is_sales = $invoice_type == 'sales';

// أنواع الحسابات للفواتير
$invoice_accounts = $account->getByType($is_sales ? 'revenue' : 'expense');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل فاتورة رقم <?php echo $invoice_data['invoice_number']; ?> - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">

    <style>
        /* تنسيقات الهيدر الثابت الموحد */
        body {
            padding-top: 80px !important;
        }
        
        .search-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 200px;
        }
        .search-input::placeholder { color: rgba(255, 255, 255, 0.7); }
        .search-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }
        .time-display { font-size: 0.85rem; text-align: center; line-height: 1.2; }
        .user-avatar { font-size: 1.5rem; }
        .user-info { text-align: right; line-height: 1.2; }
        .user-name { font-weight: 600; font-size: 0.9rem; }
        .user-role { font-size: 0.75rem; }
        .notification-dropdown { width: 350px; max-height: 400px; overflow-y: auto; }
        .user-dropdown { width: 280px; }
        .version-badge { font-size: 0.6rem; padding: 0.2rem 0.4rem; }
        .quick-controls {
            position: fixed; bottom: 20px; right: 20px; z-index: 1025;
            display: flex; flex-direction: column; gap: 10px;
        }
        .quick-controls .btn {
            width: 40px; height: 40px; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: all 0.3s ease;
        }
        .quick-controls .btn:hover {
            transform: scale(1.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        @media (max-width: 991.98px) {
            .search-input { width: 150px; }
            .time-display { display: none; }
            .quick-controls { bottom: 10px; right: 10px; }
        }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->
    
    <div class="container-fluid">
        <div class="row">
            <!-- المحتوى الرئيسي -->
            <div class="col-12 p-4">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="page-title">
                                <i class="fas fa-edit me-2"></i>
                                تعديل <?php echo $is_sales ? 'فاتورة مبيعات' : 'فاتورة مشتريات'; ?>
                            </h1>
                            <p class="page-subtitle">فاتورة رقم: <?php echo $invoice_data['invoice_number']; ?></p>
                        </div>
                        <div class="col-auto">
                            <a href="invoice_view.php?id=<?php echo $invoice_id; ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-eye me-2"></i>عرض الفاتورة
                            </a>
                            <a href="invoices.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-2"></i>العودة للفواتير
                            </a>
                        </div>
                    </div>
                </div>

                <!-- الرسائل -->
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- نموذج الفاتورة -->
                <form method="POST" id="invoiceForm">
                    <div class="row">
                        <!-- معلومات الفاتورة الأساسية -->
                        <div class="col-lg-8">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>معلومات الفاتورة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="invoice_number" class="form-label">رقم الفاتورة *</label>
                                            <input type="text" class="form-control" id="invoice_number" name="invoice_number" 
                                                   value="<?php echo htmlspecialchars($invoice_data['invoice_number']); ?>" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="invoice_date" class="form-label">تاريخ الفاتورة *</label>
                                            <input type="date" class="form-control" id="invoice_date" name="invoice_date" 
                                                   value="<?php echo $invoice_data['invoice_date']; ?>" required>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="<?php echo $is_sales ? 'customer_id' : 'supplier_id'; ?>" class="form-label">
                                                <?php echo $is_sales ? 'العميل' : 'المورد'; ?> *
                                            </label>
                                            <select class="form-select" id="<?php echo $is_sales ? 'customer_id' : 'supplier_id'; ?>" 
                                                    name="<?php echo $is_sales ? 'customer_id' : 'supplier_id'; ?>" required>
                                                <option value="">اختر <?php echo $is_sales ? 'العميل' : 'المورد'; ?></option>
                                                <?php 
                                                $entities = $is_sales ? $customers : $suppliers;
                                                $selected_id = $is_sales ? $invoice_data['customer_id'] : $invoice_data['supplier_id'];
                                                foreach ($entities as $entity): 
                                                ?>
                                                    <option value="<?php echo $entity['id']; ?>" 
                                                            <?php echo $entity['id'] == $selected_id ? 'selected' : ''; ?>>
                                                        <?php echo $entity['name']; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="due_date" class="form-label">تاريخ الاستحقاق *</label>
                                            <input type="date" class="form-control" id="due_date" name="due_date" 
                                                   value="<?php echo $invoice_data['due_date']; ?>" required>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="notes" class="form-label">ملاحظات</label>
                                        <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo htmlspecialchars($invoice_data['notes']); ?></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- عناصر الفاتورة -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-list me-2"></i>عناصر الفاتورة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered" id="invoiceItemsTable">
                                            <thead class="table-light">
                                                <tr>
                                                    <th width="35%">الوصف</th>
                                                    <th width="15%">الكمية</th>
                                                    <th width="15%">سعر الوحدة</th>
                                                    <th width="15%">الإجمالي</th>
                                                    <th width="15%">الحساب</th>
                                                    <th width="5%">حذف</th>
                                                </tr>
                                            </thead>
                                            <tbody id="invoiceItemsBody">
                                                <?php foreach ($invoice_items as $index => $item): ?>
                                                <tr id="item_<?php echo $index + 1; ?>">
                                                    <td>
                                                        <input type="text" class="form-control" name="item_description[]" 
                                                               value="<?php echo htmlspecialchars($item['item_description']); ?>" required>
                                                    </td>
                                                    <td>
                                                        <input type="number" class="form-control quantity-input" name="quantity[]" 
                                                               value="<?php echo $item['quantity']; ?>" min="1" step="1" 
                                                               onchange="calculateItemTotal(this)" required>
                                                    </td>
                                                    <td>
                                                        <input type="number" class="form-control price-input" name="unit_price[]" 
                                                               value="<?php echo $item['unit_price']; ?>" min="0" step="0.01" 
                                                               onchange="calculateItemTotal(this)" required>
                                                    </td>
                                                    <td>
                                                        <input type="number" class="form-control total-input" name="item_total[]" 
                                                               value="<?php echo $item['total_price']; ?>" readonly>
                                                    </td>
                                                    <td>
                                                        <select class="form-select" name="account_id[]">
                                                            <option value="">اختر الحساب</option>
                                                            <?php foreach ($invoice_accounts as $acc): ?>
                                                                <option value="<?php echo $acc['id']; ?>" 
                                                                        <?php echo $acc['id'] == $item['account_id'] ? 'selected' : ''; ?>>
                                                                    <?php echo $acc['account_code'] . ' - ' . $acc['account_name']; ?>
                                                                </option>
                                                            <?php endforeach; ?>
                                                        </select>
                                                    </td>
                                                    <td>
                                                        <button type="button" class="btn btn-outline-danger btn-sm" 
                                                                onclick="removeInvoiceItem('item_<?php echo $index + 1; ?>')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td colspan="6">
                                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addInvoiceItem()">
                                                            <i class="fas fa-plus me-2"></i>إضافة عنصر
                                                        </button>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ملخص الفاتورة -->
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-calculator me-2"></i>ملخص الفاتورة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label class="form-label">المجموع الفرعي:</label>
                                        </div>
                                        <div class="col-6 text-end">
                                            <strong id="subtotalDisplay"><?php echo number_format($invoice_data['subtotal'], 2); ?></strong>
                                            <input type="hidden" name="subtotal" id="subtotal" value="<?php echo $invoice_data['subtotal']; ?>">
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label for="discount_amount" class="form-label">الخصم:</label>
                                        </div>
                                        <div class="col-6">
                                            <div class="input-group input-group-sm">
                                                <input type="number" class="form-control" id="discount_amount" name="discount_amount" 
                                                       value="<?php echo $invoice_data['discount_amount']; ?>" step="0.01" min="0" onchange="calculateTotal()">
                                                <span class="input-group-text"><?php echo CURRENCY_SYMBOL; ?></span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label for="tax_amount" class="form-label">الضريبة:</label>
                                        </div>
                                        <div class="col-6">
                                            <div class="input-group input-group-sm">
                                                <input type="number" class="form-control" id="tax_amount" name="tax_amount" 
                                                       value="<?php echo $invoice_data['tax_amount']; ?>" step="0.01" min="0" onchange="calculateTotal()">
                                                <span class="input-group-text"><?php echo CURRENCY_SYMBOL; ?></span>
                                            </div>
                                        </div>
                                    </div>

                                    <hr>

                                    <div class="row mb-4">
                                        <div class="col-6">
                                            <strong>الإجمالي:</strong>
                                        </div>
                                        <div class="col-6 text-end">
                                            <h4 class="text-primary mb-0" id="totalDisplay"><?php echo number_format($invoice_data['total_amount'], 2); ?> <?php echo CURRENCY_SYMBOL; ?></h4>
                                            <input type="hidden" name="total_amount" id="total_amount" value="<?php echo $invoice_data['total_amount']; ?>">
                                        </div>
                                    </div>

                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>حفظ التعديلات
                                        </button>
                                        <a href="invoice_view.php?id=<?php echo $invoice_id; ?>" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>إلغاء
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        let itemCounter = <?php echo count($invoice_items); ?>;
        
        // بيانات الحسابات
        const accounts = <?php echo json_encode($invoice_accounts); ?>;
        
        // إضافة عنصر جديد للفاتورة
        function addInvoiceItem() {
            itemCounter++;
            const tbody = document.getElementById('invoiceItemsBody');
            const row = document.createElement('tr');
            row.id = `item_${itemCounter}`;
            
            row.innerHTML = `
                <td>
                    <input type="text" class="form-control" name="item_description[]" 
                           placeholder="وصف العنصر" required>
                </td>
                <td>
                    <input type="number" class="form-control quantity-input" name="quantity[]" 
                           value="1" min="1" step="1" onchange="calculateItemTotal(this)" required>
                </td>
                <td>
                    <input type="number" class="form-control price-input" name="unit_price[]" 
                           value="0" min="0" step="0.01" onchange="calculateItemTotal(this)" required>
                </td>
                <td>
                    <input type="number" class="form-control total-input" name="item_total[]" 
                           value="0" readonly>
                </td>
                <td>
                    <select class="form-select" name="account_id[]">
                        <option value="">اختر الحساب</option>
                        ${accounts.map(account => 
                            `<option value="${account.id}">${account.account_code} - ${account.account_name}</option>`
                        ).join('')}
                    </select>
                </td>
                <td>
                    <button type="button" class="btn btn-outline-danger btn-sm" 
                            onclick="removeInvoiceItem('item_${itemCounter}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
        }
        
        // حذف عنصر من الفاتورة
        function removeInvoiceItem(itemId) {
            const row = document.getElementById(itemId);
            if (row) {
                row.remove();
                calculateTotal();
            }
        }
        
        // حساب إجمالي العنصر
        function calculateItemTotal(input) {
            const row = input.closest('tr');
            const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
            const price = parseFloat(row.querySelector('.price-input').value) || 0;
            const total = quantity * price;
            
            row.querySelector('.total-input').value = total.toFixed(2);
            calculateTotal();
        }
        
        // حساب الإجمالي العام
        function calculateTotal() {
            let subtotal = 0;
            
            // جمع جميع عناصر الفاتورة
            document.querySelectorAll('.total-input').forEach(input => {
                subtotal += parseFloat(input.value) || 0;
            });
            
            const discount = parseFloat(document.getElementById('discount_amount').value) || 0;
            const tax = parseFloat(document.getElementById('tax_amount').value) || 0;
            const total = subtotal - discount + tax;
            
            // تحديث العرض
            document.getElementById('subtotalDisplay').textContent = subtotal.toFixed(2);
            document.getElementById('totalDisplay').textContent = total.toFixed(2) + ' <?php echo CURRENCY_SYMBOL; ?>';
            
            // تحديث الحقول المخفية
            document.getElementById('subtotal').value = subtotal.toFixed(2);
            document.getElementById('total_amount').value = total.toFixed(2);
        }
        
        // حساب الإجمالي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            calculateTotal();
        });
        
        // التحقق من صحة النموذج قبل الإرسال
        document.getElementById('invoiceForm').addEventListener('submit', function(e) {
            const items = document.querySelectorAll('#invoiceItemsBody tr');
            if (items.length === 0) {
                e.preventDefault();
                alert('يجب إضافة عنصر واحد على الأقل للفاتورة');
                return false;
            }
            
            const total = parseFloat(document.getElementById('total_amount').value);
            if (total <= 0) {
                e.preventDefault();
                alert('يجب أن يكون إجمالي الفاتورة أكبر من صفر');
                return false;
            }
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
