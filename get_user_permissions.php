<?php
/**
 * SeaSystem - جلب صلاحيات المستخدم
 * Get User Permissions AJAX
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/Permission.php';

// التأكد من تسجيل الدخول وصلاحيات المدير
if (!isLoggedIn() || !hasPermission('admin')) {
    http_response_code(403);
    echo json_encode(['error' => 'غير مصرح لك بالوصول']);
    exit();
}

// التحقق من وجود معرف المستخدم
if (!isset($_GET['user_id']) || !is_numeric($_GET['user_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'معرف المستخدم مطلوب']);
    exit();
}

try {
    $permission = new Permission();
    $userId = (int)$_GET['user_id'];
    
    // جلب صلاحيات المستخدم
    $userPermissions = $permission->getUserPermissions($userId);
    
    // إرجاع النتيجة
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'permissions' => $userPermissions
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
