<?php
/**
 * SeaSystem - صفحة التقارير
 * Reports Page
 */

require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/includes/sidebar.php';
require_once __DIR__ . '/classes/Account.php';
require_once __DIR__ . '/classes/Customer.php';
require_once __DIR__ . '/classes/Invoice.php';
require_once __DIR__ . '/classes/FinancialReport.php';

// التأكد من تسجيل الدخول
requireLogin();

$account = new Account();
$customer = new Customer();
$invoice = new Invoice();
$financial_report = new FinancialReport();
$current_user = getCurrentUser();

// إحصائيات عامة
$total_customers = count($customer->getAll());
$total_accounts = count($account->getAll());
$invoice_stats = $invoice->getStatistics();

// إحصائيات الحسابات حسب النوع
$account_types = [
    'asset' => 'الأصول',
    'liability' => 'الخصوم',
    'equity' => 'حقوق الملكية',
    'revenue' => 'الإيرادات',
    'expense' => 'المصروفات'
];

$accounts_by_type = [];
foreach ($account_types as $type => $type_name) {
    $accounts_by_type[$type] = $account->getByType($type);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    <link href="assets/css/sidebar-only.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <?php include_once "includes/header.php"; ?>
    <!-- سيتم إدراج الهيدر الموحد هنا -->

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي الموحد -->
            <?php renderSidebar('reports.php'); ?>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="page-title">
                                <i class="fas fa-chart-bar me-2"></i>التقارير المالية
                            </h1>
                            <p class="page-subtitle">تقارير شاملة عن الوضع المالي والمحاسبي</p>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-primary" onclick="window.print()">
                                <i class="fas fa-print me-2"></i>طباعة التقرير
                            </button>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات عامة -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon primary">
                                    <i class="fas fa-file-invoice"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format($invoice_stats['total_invoices']); ?></h3>
                                    <p class="text-muted mb-0">إجمالي الفواتير</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon success">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format($invoice_stats['total_paid'] ?? 0, 0); ?></h3>
                                    <p class="text-muted mb-0">المبالغ المحصلة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo number_format($invoice_stats['total_outstanding'] ?? 0, 0); ?></h3>
                                    <p class="text-muted mb-0">المبالغ المستحقة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon info">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="ms-3">
                                    <h3 class="mb-0"><?php echo $total_customers; ?></h3>
                                    <p class="text-muted mb-0">العملاء</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تقارير الحسابات -->
                <div class="row mb-4">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>توزيع الحسابات حسب النوع
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="accountsChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>ملخص الحسابات
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php foreach ($account_types as $type => $type_name): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <span><?php echo $type_name; ?></span>
                                        <span class="badge bg-primary"><?php echo count($accounts_by_type[$type]); ?></span>
                                    </div>
                                <?php endforeach; ?>
                                <hr>
                                <div class="d-flex justify-content-between align-items-center">
                                    <strong>إجمالي الحسابات</strong>
                                    <strong class="text-primary"><?php echo $total_accounts; ?></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تقارير الفواتير -->
                <div class="row mb-4">
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>تقرير الفواتير
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="invoicesChart" width="400" height="300"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-money-check-alt me-2"></i>التدفق النقدي
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <h4 class="text-success"><?php echo number_format($invoice_stats['total_paid'] ?? 0, 0); ?></h4>
                                        <p class="text-muted">المبالغ المحصلة</p>
                                    </div>
                                    <div class="col-6">
                                        <h4 class="text-danger"><?php echo number_format($invoice_stats['total_outstanding'] ?? 0, 0); ?></h4>
                                        <p class="text-muted">المبالغ المستحقة</p>
                                    </div>
                                </div>

                                <div class="progress mb-3" style="height: 20px;">
                                    <?php
                                    $total = ($invoice_stats['total_paid'] ?? 0) + ($invoice_stats['total_outstanding'] ?? 0);
                                    $paid_percentage = $total > 0 ? (($invoice_stats['total_paid'] ?? 0) / $total) * 100 : 0;
                                    ?>
                                    <div class="progress-bar bg-success" style="width: <?php echo $paid_percentage; ?>%">
                                        محصل (<?php echo number_format($paid_percentage, 1); ?>%)
                                    </div>
                                </div>

                                <div class="small text-muted">
                                    <div class="d-flex justify-content-between">
                                        <span>متوسط الفاتورة:</span>
                                        <strong><?php echo number_format($invoice_stats['average_amount'] ?? 0, 0) . ' ' . CURRENCY_SYMBOL; ?></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تقارير سريعة -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-download me-2"></i>تقارير سريعة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <button class="btn btn-outline-primary w-100" onclick="generateReport('balance_sheet')">
                                            <i class="fas fa-balance-scale me-2"></i>
                                            الميزانية العمومية
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <button class="btn btn-outline-success w-100" onclick="generateReport('income_statement')">
                                            <i class="fas fa-chart-line me-2"></i>
                                            قائمة الدخل
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <button class="btn btn-outline-info w-100" onclick="generateReport('cash_flow')">
                                            <i class="fas fa-money-bill-wave me-2"></i>
                                            التدفق النقدي
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <button class="btn btn-outline-warning w-100" onclick="generateReport('trial_balance')">
                                            <i class="fas fa-calculator me-2"></i>
                                            ميزان المراجعة
                                        </button>
                                    </div>
                                </div>

                                <hr>

                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <button class="btn btn-outline-secondary w-100" onclick="generateReport('customers_report')">
                                            <i class="fas fa-users me-2"></i>
                                            تقرير العملاء
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <button class="btn btn-outline-secondary w-100" onclick="generateReport('suppliers_report')">
                                            <i class="fas fa-truck me-2"></i>
                                            تقرير الموردين
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <button class="btn btn-outline-secondary w-100" onclick="generateReport('sales_report')">
                                            <i class="fas fa-shopping-cart me-2"></i>
                                            تقرير المبيعات
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <button class="btn btn-outline-secondary w-100" onclick="generateReport('purchases_report')">
                                            <i class="fas fa-shopping-bag me-2"></i>
                                            تقرير المشتريات
                                        </button>
                                    </div>
                                </div>

                                <hr>

                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <button class="btn btn-outline-warning w-100" onclick="generateReport('inventory_report')">
                                            <i class="fas fa-boxes me-2"></i>
                                            تقرير المخزون
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <button class="btn btn-outline-info w-100" onclick="generateReport('low_stock_report')">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            مخزون منخفض
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <button class="btn btn-outline-danger w-100" onclick="generateReport('stock_movements')">
                                            <i class="fas fa-exchange-alt me-2"></i>
                                            حركات المخزون
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <button class="btn btn-outline-success w-100" onclick="generateReport('inventory_valuation')">
                                            <i class="fas fa-dollar-sign me-2"></i>
                                            تقييم المخزون
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        // رسم بياني لتوزيع الحسابات
        const accountsCtx = document.getElementById('accountsChart').getContext('2d');
        const accountsChart = new Chart(accountsCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    <?php foreach ($account_types as $type => $type_name): ?>
                        '<?php echo $type_name; ?>',
                    <?php endforeach; ?>
                ],
                datasets: [{
                    data: [
                        <?php foreach ($account_types as $type => $type_name): ?>
                            <?php echo count($accounts_by_type[$type]); ?>,
                        <?php endforeach; ?>
                    ],
                    backgroundColor: [
                        '#667eea',
                        '#764ba2',
                        '#f093fb',
                        '#f5576c',
                        '#4facfe'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // رسم بياني للفواتير
        const invoicesCtx = document.getElementById('invoicesChart').getContext('2d');
        const invoicesChart = new Chart(invoicesCtx, {
            type: 'bar',
            data: {
                labels: ['المبالغ المحصلة', 'المبالغ المستحقة'],
                datasets: [{
                    label: 'المبلغ',
                    data: [
                        <?php echo $invoice_stats['total_paid']; ?>,
                        <?php echo $invoice_stats['total_outstanding']; ?>
                    ],
                    backgroundColor: [
                        '#28a745',
                        '#dc3545'
                    ]
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // توليد التقارير
        function generateReport(reportType) {
            const reportUrls = {
                'balance_sheet': 'report_balance_sheet.php',
                'income_statement': 'report_income_statement.php',
                'cash_flow': 'report_cash_flow.php',
                'trial_balance': 'report_trial_balance.php',
                'customers_report': 'report_customer_statement.php',
                'suppliers_report': 'report_suppliers.php',
                'sales_report': 'report_sales.php',
                'purchases_report': 'report_purchases.php',
                'inventory_report': 'report_inventory.php',
                'low_stock_report': 'report_inventory.php?low_stock=1',
                'stock_movements': 'report_stock_movements.php',
                'inventory_valuation': 'report_inventory.php'
            };

            if (reportUrls[reportType]) {
                window.open(reportUrls[reportType], '_blank');
            } else {
                alert('هذا التقرير غير متوفر حالياً');
            }
        }
    
        // تحديث الوقت والتاريخ
        function updateDateTime() {
            const now = new Date();
            const timeElement = document.getElementById('current-time');
            const dateElement = document.getElementById('current-date');
            
            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString('ar-SA', {
                    hour: '2-digit', minute: '2-digit'
                });
            }
            if (dateElement) {
                dateElement.textContent = now.toLocaleDateString('ar-SA');
            }
        }
        setInterval(updateDateTime, 60000);
        updateDateTime();
    </script>
</body>
</html>
