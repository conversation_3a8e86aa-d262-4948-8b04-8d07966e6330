<?php
/**
 * SeaSystem - تنظيف شامل للمشروع
 * Comprehensive Project Cleanup
 */

// منع الوصول المباشر في الإنتاج
if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes') {
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تنظيف المشروع - SeaSystem</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h3><i class="fas fa-exclamation-triangle me-2"></i>تحذير: تنظيف المشروع</h3>
                        </div>
                        <div class="card-body">
                            <p class="lead">هذا السكريبت سيقوم بحذف الملفات غير الضرورية من المشروع.</p>
                            
                            <h5>الملفات التي سيتم حذفها:</h5>
                            <ul class="text-danger">
                                <li>ملفات الاختبار والتطوير</li>
                                <li>ملفات التقارير المؤقتة</li>
                                <li>ملفات الإصلاح المؤقتة</li>
                                <li>ملفات التوثيق الزائدة</li>
                            </ul>
                            
                            <div class="alert alert-danger">
                                <strong>تحذير:</strong> تأكد من عمل نسخة احتياطية قبل المتابعة!
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="?confirm=yes" class="btn btn-danger btn-lg">
                                    <i class="fas fa-broom me-2"></i>تأكيد التنظيف
                                </a>
                                <a href="dashboard.php" class="btn btn-secondary">إلغاء</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit();
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تنظيف المشروع - SeaSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .cleanup-step {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        .step-success { border-color: #198754; background: #d1e7dd; }
        .step-warning { border-color: #ffc107; background: #fff3cd; }
        .step-danger { border-color: #dc3545; background: #f8d7da; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-broom text-primary me-2"></i>تنظيف شامل للمشروع
                </h1>
                
                <?php
                // قائمة الملفات المراد حذفها
                $filesToDelete = [
                    // ملفات الاختبار
                    'test.php',
                    'test_all_pages_header.php',
                    'test_products_display.php',
                    'test_supplier_report_fix.php',
                    'check_database.php',
                    'verify_products_deletion.php',
                    
                    // ملفات الإصلاح والتحديث
                    'fix_all_headers.php',
                    'update_headers.php',
                    'apply_universal_header.php',
                    'apply_header_to_all_pages.php',
                    'fix_products_display.php',
                    'direct_add_products.php',
                    'delete_all_products.php',
                    'create_missing_tables.php',
                    'security_update.php',
                    'restore_password_security.php',
                    
                    // ملفات التقارير المؤقتة
                    'cleanup_summary.php',
                    'journal_cleanup_summary.php',
                    'system_summary.php',
                    
                    // ملفات التوثيق الزائدة
                    'SYSTEM_INSPECTION_REPORT.md',
                    'FIXED_HEADER_GUIDE.md',
                    'ALL_PAGES_HEADER_REPORT.md',
                    'HEADER_IMPLEMENTATION_REPORT.md',
                    'DEV_MODE_REPORT.md',
                    'SECURITY_REPORT.md',
                    'SUPPLIER_REPORT_FIX_REPORT.md',
                    'PRODUCTS_DISPLAY_FIX_REPORT.md',
                    
                    // ملفات إضافية
                    'create_permissions_system.php',
                    'project_cleanup.php' // هذا الملف نفسه
                ];
                
                $deletedFiles = [];
                $notFoundFiles = [];
                $errorFiles = [];
                
                echo '<div class="cleanup-step step-warning">';
                echo '<h4><i class="fas fa-search me-2"></i>فحص الملفات...</h4>';
                echo '<p>جاري فحص ' . count($filesToDelete) . ' ملف للحذف...</p>';
                echo '</div>';
                
                foreach ($filesToDelete as $file) {
                    if (file_exists($file)) {
                        if (unlink($file)) {
                            $deletedFiles[] = $file;
                            echo '<div class="cleanup-step step-success">';
                            echo '<i class="fas fa-check-circle text-success me-2"></i>';
                            echo '<strong>تم حذف:</strong> ' . $file;
                            echo '</div>';
                        } else {
                            $errorFiles[] = $file;
                            echo '<div class="cleanup-step step-danger">';
                            echo '<i class="fas fa-times-circle text-danger me-2"></i>';
                            echo '<strong>خطأ في حذف:</strong> ' . $file;
                            echo '</div>';
                        }
                    } else {
                        $notFoundFiles[] = $file;
                    }
                }
                
                // تنظيف ملفات CSS و JS غير المستخدمة
                echo '<div class="cleanup-step step-warning">';
                echo '<h4><i class="fas fa-code me-2"></i>تنظيف ملفات CSS/JS...</h4>';
                
                $cssFiles = glob('assets/css/*.css');
                $jsFiles = glob('assets/js/*.js');
                
                // قائمة الملفات المطلوبة
                $requiredCss = ['style.css', 'fixed-header.css', 'sidebar-only.css'];
                $requiredJs = ['main.js', 'fixed-header.js'];
                
                foreach ($cssFiles as $cssFile) {
                    $filename = basename($cssFile);
                    if (!in_array($filename, $requiredCss)) {
                        if (unlink($cssFile)) {
                            echo '<p class="text-success mb-1">✅ حذف CSS: ' . $filename . '</p>';
                        }
                    }
                }
                
                foreach ($jsFiles as $jsFile) {
                    $filename = basename($jsFile);
                    if (!in_array($filename, $requiredJs)) {
                        if (unlink($jsFile)) {
                            echo '<p class="text-success mb-1">✅ حذف JS: ' . $filename . '</p>';
                        }
                    }
                }
                echo '</div>';
                
                // ملخص النتائج
                echo '<div class="cleanup-step step-success">';
                echo '<h3><i class="fas fa-chart-pie me-2"></i>ملخص التنظيف</h3>';
                echo '<div class="row">';
                
                echo '<div class="col-md-4">';
                echo '<div class="text-center p-3 border rounded bg-success text-white">';
                echo '<h4>' . count($deletedFiles) . '</h4>';
                echo '<p class="mb-0">ملف محذوف</p>';
                echo '</div>';
                echo '</div>';
                
                echo '<div class="col-md-4">';
                echo '<div class="text-center p-3 border rounded bg-warning text-dark">';
                echo '<h4>' . count($notFoundFiles) . '</h4>';
                echo '<p class="mb-0">ملف غير موجود</p>';
                echo '</div>';
                echo '</div>';
                
                echo '<div class="col-md-4">';
                echo '<div class="text-center p-3 border rounded bg-danger text-white">';
                echo '<h4>' . count($errorFiles) . '</h4>';
                echo '<p class="mb-0">خطأ في الحذف</p>';
                echo '</div>';
                echo '</div>';
                
                echo '</div>';
                echo '</div>';
                
                // تحسين قاعدة البيانات
                echo '<div class="cleanup-step step-warning">';
                echo '<h4><i class="fas fa-database me-2"></i>تحسين قاعدة البيانات...</h4>';
                
                try {
                    require_once 'config/database.php';
                    $database = new Database();
                    $pdo = $database->getConnection();
                    
                    // تحسين الجداول
                    $tables = ['users', 'customers', 'suppliers', 'products', 'invoices', 'payments', 'accounts', 'journal_entries'];
                    
                    foreach ($tables as $table) {
                        try {
                            $pdo->exec("OPTIMIZE TABLE $table");
                            echo '<p class="text-success mb-1">✅ تم تحسين جدول: ' . $table . '</p>';
                        } catch (Exception $e) {
                            echo '<p class="text-warning mb-1">⚠️ جدول غير موجود: ' . $table . '</p>';
                        }
                    }
                    
                    echo '<p class="text-success"><strong>✅ تم تحسين قاعدة البيانات بنجاح</strong></p>';
                    
                } catch (Exception $e) {
                    echo '<p class="text-danger">❌ خطأ في تحسين قاعدة البيانات: ' . $e->getMessage() . '</p>';
                }
                
                echo '</div>';
                
                // التوصيات النهائية
                echo '<div class="cleanup-step step-success">';
                echo '<h3><i class="fas fa-lightbulb me-2"></i>التوصيات النهائية</h3>';
                echo '<ul>';
                echo '<li><strong>✅ تم تنظيف المشروع بنجاح</strong></li>';
                echo '<li>احذف هذا الملف (project_cleanup.php) يدوياً</li>';
                echo '<li>قم بعمل نسخة احتياطية من المشروع النظيف</li>';
                echo '<li>اختبر جميع وظائف النظام للتأكد من عملها</li>';
                echo '<li>راجع ملف README.md للحصول على التوثيق المحدث</li>';
                echo '</ul>';
                echo '</div>';
                
                echo '<div class="text-center mt-4">';
                echo '<a href="dashboard.php" class="btn btn-primary btn-lg">';
                echo '<i class="fas fa-home me-2"></i>العودة للوحة التحكم';
                echo '</a>';
                echo '</div>';
                ?>
            </div>
        </div>
    </div>
</body>
</html>
