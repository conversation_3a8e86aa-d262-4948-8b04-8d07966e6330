<?php
/**
 * SeaSystem - التحقق من التكرار عبر AJAX
 * AJAX Duplicate Check
 */

// تعريف الثابت للوصول
define('SEASYSTEM_ACCESS', true);

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/Customer.php';
require_once 'classes/Supplier.php';
require_once 'classes/Inventory.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح']);
    exit();
}

// التحقق من وجود البيانات المطلوبة
if (!isset($_POST['type']) || !isset($_POST['value'])) {
    http_response_code(400);
    echo json_encode(['error' => 'بيانات مفقودة']);
    exit();
}

$type = $_POST['type'];
$value = trim($_POST['value']);
$exclude_id = !empty($_POST['exclude_id']) ? (int)$_POST['exclude_id'] : null;

// إذا كانت القيمة فارغة، لا يوجد تكرار
if (empty($value)) {
    echo json_encode(['exists' => false, 'message' => '']);
    exit();
}

$response = ['exists' => false, 'message' => ''];

try {
    switch ($type) {
        // التحقق من تكرار العملاء
        case 'customer_code':
            $customer = new Customer();
            $exists = $customer->checkCustomerCodeExists($value, $exclude_id);
            if ($exists) {
                $response = [
                    'exists' => true,
                    'message' => 'رمز العميل "' . htmlspecialchars($value) . '" موجود مسبقاً'
                ];
            }
            break;
            
        case 'customer_name':
            $customer = new Customer();
            $exists = $customer->checkCustomerNameExists($value, $exclude_id);
            if ($exists) {
                $response = [
                    'exists' => true,
                    'message' => 'اسم العميل "' . htmlspecialchars($value) . '" موجود مسبقاً'
                ];
            }
            break;
            
        case 'customer_email':
            $customer = new Customer();
            $exists = $customer->checkEmailExists($value, $exclude_id);
            if ($exists) {
                $response = [
                    'exists' => true,
                    'message' => 'البريد الإلكتروني "' . htmlspecialchars($value) . '" موجود مسبقاً'
                ];
            }
            break;
            
        case 'customer_phone':
            $customer = new Customer();
            $exists = $customer->checkPhoneExists($value, $exclude_id);
            if ($exists) {
                $response = [
                    'exists' => true,
                    'message' => 'رقم الهاتف "' . htmlspecialchars($value) . '" موجود مسبقاً'
                ];
            }
            break;
            
        // التحقق من تكرار الموردين
        case 'supplier_code':
            $supplier = new Supplier($pdo);
            $exists = $supplier->checkSupplierCodeExists($value, $exclude_id);
            if ($exists) {
                $response = [
                    'exists' => true,
                    'message' => 'رمز المورد "' . htmlspecialchars($value) . '" موجود مسبقاً'
                ];
            }
            break;
            
        case 'supplier_name':
            $supplier = new Supplier($pdo);
            $exists = $supplier->checkSupplierNameExists($value, $exclude_id);
            if ($exists) {
                $response = [
                    'exists' => true,
                    'message' => 'اسم المورد "' . htmlspecialchars($value) . '" موجود مسبقاً'
                ];
            }
            break;
            
        case 'supplier_email':
            $supplier = new Supplier($pdo);
            $exists = $supplier->checkEmailExists($value, $exclude_id);
            if ($exists) {
                $response = [
                    'exists' => true,
                    'message' => 'البريد الإلكتروني "' . htmlspecialchars($value) . '" موجود مسبقاً'
                ];
            }
            break;
            
        case 'supplier_phone':
            $supplier = new Supplier($pdo);
            $exists = $supplier->checkPhoneExists($value, $exclude_id);
            if ($exists) {
                $response = [
                    'exists' => true,
                    'message' => 'رقم الهاتف "' . htmlspecialchars($value) . '" موجود مسبقاً'
                ];
            }
            break;
            
        // التحقق من تكرار المنتجات
        case 'product_code':
            $inventory = new Inventory();
            $exists = $inventory->checkProductCodeExists($value, $exclude_id);
            if ($exists) {
                $response = [
                    'exists' => true,
                    'message' => 'رمز المنتج "' . htmlspecialchars($value) . '" موجود مسبقاً'
                ];
            }
            break;
            
        case 'product_name':
            $inventory = new Inventory();
            $exists = $inventory->checkProductNameExists($value, $exclude_id);
            if ($exists) {
                $response = [
                    'exists' => true,
                    'message' => 'اسم المنتج "' . htmlspecialchars($value) . '" موجود مسبقاً'
                ];
            }
            break;
            
        case 'barcode':
            $inventory = new Inventory();
            $exists = $inventory->checkBarcodeExists($value, $exclude_id);
            if ($exists) {
                $response = [
                    'exists' => true,
                    'message' => 'الباركود "' . htmlspecialchars($value) . '" موجود مسبقاً'
                ];
            }
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'نوع التحقق غير صحيح']);
            exit();
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في النظام: ' . $e->getMessage()]);
    exit();
}

// إرجاع النتيجة
header('Content-Type: application/json');
echo json_encode($response);
?>
