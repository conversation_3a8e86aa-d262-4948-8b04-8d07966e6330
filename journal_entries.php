<?php
/**
 * SeaSystem - دفتر اليومية
 * Journal Entries Management
 */

session_start();
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/JournalEntry.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
requireLogin();

$database = new Database();
$db = $database->getConnection();
$journalEntry = new JournalEntry($db);

// معالجة الطلبات
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create':
                $entries_data = json_decode($_POST['entries'], true);

                $entry_data = [
                    'entry_date' => $_POST['date'],
                    'entry_number' => $_POST['reference'],
                    'description' => $_POST['description'],
                    'reference_type' => 'manual',
                    'status' => 'draft'
                ];

                $details_data = $entries_data;

                $result = $journalEntry->create($entry_data, $details_data);
                if ($result['success']) {
                    $message = 'تم إنشاء القيد بنجاح';
                    $messageType = 'success';
                } else {
                    $message = $result['message'] ?? 'حدث خطأ أثناء إنشاء القيد';
                    $messageType = 'error';
                }
                break;

            case 'delete':
                $result = $journalEntry->delete($_POST['id']);
                if ($result['success']) {
                    $message = 'تم حذف القيد بنجاح';
                    $messageType = 'success';
                } else {
                    $message = $result['message'] ?? 'حدث خطأ أثناء حذف القيد';
                    $messageType = 'error';
                }
                break;
        }
    }
}

// جلب جميع القيود
$entries = $journalEntry->getAll();

// جلب الحسابات للاستخدام في النموذج
require_once 'classes/Account.php';
$account = new Account($db);
$accounts = $account->getAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دفتر اليومية - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-book me-2"></i>
                        دفتر اليومية
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createEntryModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة قيد جديد
                        </button>
                    </div>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType == 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- جدول القيود -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة القيود
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>رقم القيد</th>
                                        <th>التاريخ</th>
                                        <th>المرجع</th>
                                        <th>الوصف</th>
                                        <th>المدين</th>
                                        <th>الدائن</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($entries && count($entries) > 0): ?>
                                        <?php foreach ($entries as $entry): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($entry['id']); ?></td>
                                                <td><?php echo date('Y-m-d', strtotime($entry['entry_date'] ?? $entry['date'] ?? date('Y-m-d'))); ?></td>
                                                <td><?php echo htmlspecialchars($entry['entry_number'] ?? $entry['reference'] ?? ''); ?></td>
                                                <td><?php echo htmlspecialchars($entry['description'] ?? ''); ?></td>
                                                <td><?php echo number_format((float)($entry['debit_amount'] ?? 0), 2); ?> <?php echo CURRENCY_SYMBOL; ?></td>
                                                <td><?php echo number_format((float)($entry['credit_amount'] ?? 0), 2); ?> <?php echo CURRENCY_SYMBOL; ?></td>
                                                <td>
                                                    <div class="d-flex gap-2">
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="viewEntry(<?php echo $entry['id']; ?>)">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">عرض</small>
                                                        </div>
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteEntry(<?php echo $entry['id']; ?>)">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                            <small class="d-block text-muted mt-1">حذف</small>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="7" class="text-center">لا توجد قيود محاسبية</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- نموذج إضافة قيد جديد -->
    <div class="modal fade" id="createEntryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة قيد محاسبي جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="createEntryForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="create">
                        <input type="hidden" name="entries" id="entriesData">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date" class="form-label">التاريخ</label>
                                    <input type="date" class="form-control" id="date" name="date" value="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="reference" class="form-label">المرجع</label>
                                    <input type="text" class="form-control" id="reference" name="reference" placeholder="رقم المرجع">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="2" placeholder="وصف القيد" required></textarea>
                        </div>

                        <!-- جدول إدخال القيود -->
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">تفاصيل القيد</h6>
                                <button type="button" class="btn btn-sm btn-success" onclick="addEntryRow()">
                                    <i class="fas fa-plus"></i> إضافة سطر
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm" id="entryTable">
                                        <thead>
                                            <tr>
                                                <th>الحساب</th>
                                                <th>الوصف</th>
                                                <th>مدين</th>
                                                <th>دائن</th>
                                                <th>إجراء</th>
                                            </tr>
                                        </thead>
                                        <tbody id="entryTableBody">
                                            <!-- سيتم إضافة الصفوف هنا بواسطة JavaScript -->
                                        </tbody>
                                        <tfoot>
                                            <tr class="table-info">
                                                <th colspan="2">الإجمالي</th>
                                                <th id="totalDebit">0.00</th>
                                                <th id="totalCredit">0.00</th>
                                                <th></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ القيد</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نموذج حذف القيد -->
    <div class="modal fade" id="deleteEntryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    هل أنت متأكد من حذف هذا القيد؟ لا يمكن التراجع عن هذا الإجراء.
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="id" id="deleteEntryId">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        let entryRowCount = 0;
        const accounts = <?php echo json_encode($accounts); ?>;

        function addEntryRow() {
            entryRowCount++;
            const tbody = document.getElementById('entryTableBody');
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <select class="form-select form-select-sm" name="account_id_${entryRowCount}" required>
                        <option value="">اختر الحساب</option>
                        ${accounts.map(account => `<option value="${account.id}">${account.name}</option>`).join('')}
                    </select>
                </td>
                <td>
                    <input type="text" class="form-control form-control-sm" name="description_${entryRowCount}" placeholder="وصف">
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm debit-input" name="debit_${entryRowCount}"
                           step="0.01" min="0" placeholder="0.00" onchange="updateTotals()">
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm credit-input" name="credit_${entryRowCount}"
                           step="0.01" min="0" placeholder="0.00" onchange="updateTotals()">
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeEntryRow(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        }

        function removeEntryRow(button) {
            button.closest('tr').remove();
            updateTotals();
        }

        function updateTotals() {
            let totalDebit = 0;
            let totalCredit = 0;

            document.querySelectorAll('.debit-input').forEach(input => {
                totalDebit += parseFloat(input.value) || 0;
            });

            document.querySelectorAll('.credit-input').forEach(input => {
                totalCredit += parseFloat(input.value) || 0;
            });

            document.getElementById('totalDebit').textContent = totalDebit.toFixed(2);
            document.getElementById('totalCredit').textContent = totalCredit.toFixed(2);
        }

        function deleteEntry(id) {
            document.getElementById('deleteEntryId').value = id;
            new bootstrap.Modal(document.getElementById('deleteEntryModal')).show();
        }

        function viewEntry(id) {
            // يمكن إضافة وظيفة عرض تفاصيل القيد هنا
            alert('عرض تفاصيل القيد رقم: ' + id);
        }

        // إضافة صف واحد افتراضي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addEntryRow();
            addEntryRow(); // إضافة صفين للبداية
        });

        // التحقق من توازن القيد قبل الإرسال
        document.getElementById('createEntryForm').addEventListener('submit', function(e) {
            const totalDebit = parseFloat(document.getElementById('totalDebit').textContent);
            const totalCredit = parseFloat(document.getElementById('totalCredit').textContent);

            if (Math.abs(totalDebit - totalCredit) > 0.01) {
                e.preventDefault();
                alert('يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن');
                return false;
            }

            // جمع بيانات القيود
            const entries = [];
            const rows = document.querySelectorAll('#entryTableBody tr');

            rows.forEach((row, index) => {
                const accountSelect = row.querySelector('select');
                const descriptionInput = row.querySelector('input[name*="description"]');
                const debitInput = row.querySelector('.debit-input');
                const creditInput = row.querySelector('.credit-input');

                if (accountSelect.value && (debitInput.value || creditInput.value)) {
                    entries.push({
                        account_id: accountSelect.value,
                        description: descriptionInput.value,
                        debit: parseFloat(debitInput.value) || 0,
                        credit: parseFloat(creditInput.value) || 0
                    });
                }
            });

            document.getElementById('entriesData').value = JSON.stringify(entries);
        });
    </script>
</body>
</html>
