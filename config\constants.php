<?php
/**
 * SeaSystem - ملف الثوابت
 * Constants Configuration File
 */

// منع الوصول المباشر
if (!defined('DB_HOST')) {
    // إذا لم يتم تحميل ملف قاعدة البيانات، قم بتحميله
    require_once __DIR__ . '/database.php';
}

// ثوابت النظام الأساسية
if (!defined('SITE_NAME')) {
    define('SITE_NAME', 'SeaSystem');
}

if (!defined('SITE_VERSION')) {
    define('SITE_VERSION', '1.0.0');
}

if (!defined('SITE_DESCRIPTION')) {
    define('SITE_DESCRIPTION', 'نظام محاسبي متكامل');
}

// ثوابت العملة والتنسيق
if (!defined('CURRENCY_SYMBOL')) {
    define('CURRENCY_SYMBOL', 'ر.س');
}

if (!defined('CURRENCY_CODE')) {
    define('CURRENCY_CODE', 'SAR');
}

if (!defined('DATE_FORMAT')) {
    define('DATE_FORMAT', 'Y-m-d');
}

if (!defined('DATETIME_FORMAT')) {
    define('DATETIME_FORMAT', 'Y-m-d H:i:s');
}

if (!defined('TIME_FORMAT')) {
    define('TIME_FORMAT', 'H:i:s');
}

// ثوابت الصفحات والتصفح
if (!defined('RECORDS_PER_PAGE')) {
    define('RECORDS_PER_PAGE', 25);
}

if (!defined('MAX_UPLOAD_SIZE')) {
    define('MAX_UPLOAD_SIZE', 5242880); // 5MB
}

// ثوابت الأمان
if (!defined('SESSION_TIMEOUT')) {
    define('SESSION_TIMEOUT', 3600); // ساعة واحدة
}

if (!defined('MAX_LOGIN_ATTEMPTS')) {
    define('MAX_LOGIN_ATTEMPTS', 5);
}

// ثوابت المسارات
if (!defined('ASSETS_PATH')) {
    define('ASSETS_PATH', '/seasystem/assets/');
}

if (!defined('UPLOADS_PATH')) {
    define('UPLOADS_PATH', '/seasystem/uploads/');
}

if (!defined('REPORTS_PATH')) {
    define('REPORTS_PATH', '/seasystem/reports/');
}

// ثوابت قاعدة البيانات الإضافية
if (!defined('DB_PREFIX')) {
    define('DB_PREFIX', '');
}

// ثوابت الحسابات المحاسبية
if (!defined('CASH_ACCOUNT_CODE')) {
    define('CASH_ACCOUNT_CODE', '1001');
}

if (!defined('BANK_ACCOUNT_CODE')) {
    define('BANK_ACCOUNT_CODE', '1002');
}

if (!defined('CUSTOMERS_ACCOUNT_CODE')) {
    define('CUSTOMERS_ACCOUNT_CODE', '1201');
}

if (!defined('SUPPLIERS_ACCOUNT_CODE')) {
    define('SUPPLIERS_ACCOUNT_CODE', '2001');
}

if (!defined('SALES_ACCOUNT_CODE')) {
    define('SALES_ACCOUNT_CODE', '4001');
}

if (!defined('PURCHASES_ACCOUNT_CODE')) {
    define('PURCHASES_ACCOUNT_CODE', '5001');
}

// ثوابت أنواع المعاملات
if (!defined('TRANSACTION_TYPE_DEBIT')) {
    define('TRANSACTION_TYPE_DEBIT', 'debit');
}

if (!defined('TRANSACTION_TYPE_CREDIT')) {
    define('TRANSACTION_TYPE_CREDIT', 'credit');
}

// ثوابت حالات الفواتير
if (!defined('INVOICE_STATUS_DRAFT')) {
    define('INVOICE_STATUS_DRAFT', 'draft');
}

if (!defined('INVOICE_STATUS_PENDING')) {
    define('INVOICE_STATUS_PENDING', 'pending');
}

if (!defined('INVOICE_STATUS_PAID')) {
    define('INVOICE_STATUS_PAID', 'paid');
}

if (!defined('INVOICE_STATUS_CANCELLED')) {
    define('INVOICE_STATUS_CANCELLED', 'cancelled');
}

// ثوابت أنواع الدفع
if (!defined('PAYMENT_TYPE_CASH')) {
    define('PAYMENT_TYPE_CASH', 'cash');
}

if (!defined('PAYMENT_TYPE_BANK')) {
    define('PAYMENT_TYPE_BANK', 'bank');
}

if (!defined('PAYMENT_TYPE_CHECK')) {
    define('PAYMENT_TYPE_CHECK', 'check');
}

if (!defined('PAYMENT_TYPE_CREDIT')) {
    define('PAYMENT_TYPE_CREDIT', 'credit');
}

// ثوابت مستويات المستخدمين
if (!defined('USER_ROLE_ADMIN')) {
    define('USER_ROLE_ADMIN', 'admin');
}

if (!defined('USER_ROLE_MANAGER')) {
    define('USER_ROLE_MANAGER', 'manager');
}

if (!defined('USER_ROLE_ACCOUNTANT')) {
    define('USER_ROLE_ACCOUNTANT', 'accountant');
}

if (!defined('USER_ROLE_USER')) {
    define('USER_ROLE_USER', 'user');
}

// ثوابت الرسائل
if (!defined('MSG_SUCCESS')) {
    define('MSG_SUCCESS', 'success');
}

if (!defined('MSG_ERROR')) {
    define('MSG_ERROR', 'error');
}

if (!defined('MSG_WARNING')) {
    define('MSG_WARNING', 'warning');
}

if (!defined('MSG_INFO')) {
    define('MSG_INFO', 'info');
}

// ثوابت التصدير
if (!defined('EXPORT_FORMAT_PDF')) {
    define('EXPORT_FORMAT_PDF', 'pdf');
}

if (!defined('EXPORT_FORMAT_EXCEL')) {
    define('EXPORT_FORMAT_EXCEL', 'excel');
}

if (!defined('EXPORT_FORMAT_CSV')) {
    define('EXPORT_FORMAT_CSV', 'csv');
}

// ثوابت اللغة والمنطقة
if (!defined('DEFAULT_LANGUAGE')) {
    define('DEFAULT_LANGUAGE', 'ar');
}

if (!defined('DEFAULT_TIMEZONE')) {
    define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
}

if (!defined('DEFAULT_LOCALE')) {
    define('DEFAULT_LOCALE', 'ar_SA');
}

// ثوابت التطبيق
if (!defined('APP_ENV')) {
    define('APP_ENV', 'production'); // development, testing, production
}

if (!defined('DEBUG_MODE')) {
    define('DEBUG_MODE', false);
}

if (!defined('LOG_ERRORS')) {
    define('LOG_ERRORS', true);
}

// تعيين المنطقة الزمنية
date_default_timezone_set(DEFAULT_TIMEZONE);

// تعيين الترميز
if (!headers_sent()) {
    header('Content-Type: text/html; charset=UTF-8');
}

// دالة مساعدة للحصول على رابط الأصول
if (!function_exists('asset')) {
    function asset($path) {
        return SITE_URL . ASSETS_PATH . ltrim($path, '/');
    }
}

// دالة مساعدة لتنسيق العملة
if (!function_exists('format_currency')) {
    function format_currency($amount, $decimals = 2) {
        return number_format($amount, $decimals) . ' ' . CURRENCY_SYMBOL;
    }
}

// دالة مساعدة لتنسيق التاريخ
if (!function_exists('format_date')) {
    function format_date($date, $format = DATE_FORMAT) {
        if (empty($date) || $date === '0000-00-00' || $date === '0000-00-00 00:00:00') {
            return '';
        }
        return date($format, strtotime($date));
    }
}

// دالة مساعدة للتحقق من صحة البريد الإلكتروني
if (!function_exists('is_valid_email')) {
    function is_valid_email($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
}

// دالة مساعدة لتنظيف النصوص
if (!function_exists('clean_input')) {
    function clean_input($data) {
        $data = trim($data);
        $data = stripslashes($data);
        $data = htmlspecialchars($data);
        return $data;
    }
}

// دالة مساعدة لإنشاء رمز عشوائي
if (!function_exists('generate_code')) {
    function generate_code($prefix = '', $length = 6) {
        $code = $prefix . strtoupper(substr(uniqid(), -$length));
        return $code;
    }
}

?>
