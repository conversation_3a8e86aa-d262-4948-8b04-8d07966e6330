<?php
/**
 * SeaSystem - اختبار نظام ترقيم الفواتير
 * Invoice Numbering System Test
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/classes/Invoice.php';

// التأكد من تسجيل الدخول
requireLogin();

// إنشاء كائن الفاتورة
$invoice = new Invoice();

// الحصول على بيانات المستخدم الحالي
$current_user = getCurrentUser();

// اختبار توليد أرقام الفواتير
$sales_number = $invoice->generateInvoiceNumber('sales');
$purchase_number = $invoice->generateInvoiceNumber('purchase');

// الحصول على إحصائيات الترقيم
$numbering_stats = $invoice->getNumberingStats();

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام ترقيم الفواتير - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/fixed-header.css" rel="stylesheet">
    
    <style>
        body { padding-top: 80px !important; }
        .test-card { background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.08); padding: 2rem; margin-bottom: 2rem; }
        .number-display { font-family: 'Courier New', monospace; font-size: 1.2rem; font-weight: bold; background: #f8f9fa; padding: 1rem; border-radius: 8px; border-left: 4px solid #198754; }
        .sales-number { border-left-color: #198754; }
        .purchase-number { border-left-color: #0dcaf0; }
        .stats-table { background: #f8f9fa; }
    </style>
</head>
<body>
    <?php include_once "includes/header.php"; ?>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12 p-4">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="page-title">
                                <i class="fas fa-hashtag me-2"></i>اختبار نظام ترقيم الفواتير
                            </h1>
                            <p class="page-subtitle">التحقق من صحة نظام الترقيم التلقائي للفواتير</p>
                        </div>
                        <div class="col-auto">
                            <a href="dashboard.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-2"></i>العودة للوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>

                <!-- اختبار توليد الأرقام -->
                <div class="test-card">
                    <h3 class="text-success mb-4">
                        <i class="fas fa-cogs me-2"></i>اختبار توليد أرقام الفواتير
                    </h3>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-success">
                                <i class="fas fa-file-invoice-dollar me-2"></i>فاتورة مبيعات جديدة
                            </h5>
                            <div class="number-display sales-number">
                                <?php echo $sales_number; ?>
                            </div>
                            <small class="text-muted">
                                النمط: SALES-YYYYMM-NNNN
                            </small>
                        </div>
                        
                        <div class="col-md-6">
                            <h5 class="text-info">
                                <i class="fas fa-file-invoice me-2"></i>فاتورة مشتريات جديدة
                            </h5>
                            <div class="number-display purchase-number">
                                <?php echo $purchase_number; ?>
                            </div>
                            <small class="text-muted">
                                النمط: PURCHASE-YYYYMM-NNNN
                            </small>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات الترقيم -->
                <div class="test-card">
                    <h3 class="text-primary mb-4">
                        <i class="fas fa-chart-bar me-2"></i>إحصائيات نظام الترقيم
                    </h3>
                    
                    <?php if (!empty($numbering_stats)): ?>
                    <div class="table-responsive">
                        <table class="table table-striped stats-table">
                            <thead class="table-dark">
                                <tr>
                                    <th>نوع الفاتورة</th>
                                    <th>إجمالي الفواتير</th>
                                    <th>أول رقم</th>
                                    <th>آخر رقم</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($numbering_stats as $stat): ?>
                                <tr>
                                    <td>
                                        <span class="badge <?php echo $stat['invoice_type'] == 'sales' ? 'bg-success' : 'bg-info'; ?>">
                                            <?php echo $stat['invoice_type'] == 'sales' ? 'مبيعات' : 'مشتريات'; ?>
                                        </span>
                                    </td>
                                    <td><?php echo number_format($stat['total_count']); ?></td>
                                    <td><code><?php echo $stat['first_number']; ?></code></td>
                                    <td><code><?php echo $stat['last_number']; ?></code></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        لا توجد فواتير في النظام حتى الآن.
                    </div>
                    <?php endif; ?>
                </div>

                <!-- شرح نظام الترقيم -->
                <div class="test-card">
                    <h3 class="text-warning mb-4">
                        <i class="fas fa-info-circle me-2"></i>شرح نظام الترقيم
                    </h3>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>🟢 فواتير المبيعات</h5>
                            <ul class="list-unstyled">
                                <li><strong>البادئة:</strong> SALES-</li>
                                <li><strong>التاريخ:</strong> YYYYMM (السنة + الشهر)</li>
                                <li><strong>الرقم التسلسلي:</strong> 4 أرقام (0001-9999)</li>
                                <li><strong>مثال:</strong> <code>SALES-202412-0001</code></li>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>🔵 فواتير المشتريات</h5>
                            <ul class="list-unstyled">
                                <li><strong>البادئة:</strong> PURCHASE-</li>
                                <li><strong>التاريخ:</strong> YYYYMM (السنة + الشهر)</li>
                                <li><strong>الرقم التسلسلي:</strong> 4 أرقام (0001-9999)</li>
                                <li><strong>مثال:</strong> <code>PURCHASE-202412-0001</code></li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-success mt-3">
                        <h6><i class="fas fa-shield-alt me-2"></i>مميزات النظام:</h6>
                        <ul class="mb-0">
                            <li>✅ أرقام تلقائية غير مكررة</li>
                            <li>✅ تمييز واضح بين أنواع الفواتير</li>
                            <li>✅ ترقيم شهري منفصل</li>
                            <li>✅ حماية من التكرار</li>
                            <li>✅ سهولة البحث والفرز</li>
                        </ul>
                    </div>
                </div>

                <!-- أزرار الاختبار -->
                <div class="test-card">
                    <h3 class="text-info mb-4">
                        <i class="fas fa-play me-2"></i>اختبار إنشاء الفواتير
                    </h3>
                    
                    <div class="d-flex gap-3 justify-content-center">
                        <a href="invoice_create.php?type=sales" class="btn btn-success btn-lg">
                            <i class="fas fa-file-invoice-dollar me-2"></i>إنشاء فاتورة مبيعات
                            <small class="d-block">سيحصل على رقم: <?php echo $sales_number; ?></small>
                        </a>
                        
                        <a href="invoice_create.php?type=purchase" class="btn btn-info btn-lg">
                            <i class="fas fa-file-invoice me-2"></i>إنشاء فاتورة مشتريات
                            <small class="d-block">سيحصل على رقم: <?php echo $purchase_number; ?></small>
                        </a>
                        
                        <button onclick="location.reload()" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-sync-alt me-2"></i>تحديث الأرقام
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/fixed-header.js"></script>
</body>
</html>
