<?php
/**
 * إصلاح جدول العملاء وإضافة الأعمدة المفقودة
 */

require_once __DIR__ . '/config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>🔧 إصلاح جدول العملاء</h2>";
    
    // فحص الأعمدة الموجودة
    echo "<h3>1. فحص الأعمدة الموجودة:</h3>";
    $sql = "DESCRIBE customers";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    $existing_columns = [];
    foreach ($columns as $column) {
        $existing_columns[] = $column['Field'];
        echo "- " . $column['Field'] . " (" . $column['Type'] . ")<br>";
    }
    
    // قائمة الأعمدة المطلوبة
    $required_columns = [
        'opening_balance' => 'DECIMAL(15,2) DEFAULT 0.00',
        'current_balance' => 'DECIMAL(15,2) DEFAULT 0.00',
        'credit_limit' => 'DECIMAL(15,2) DEFAULT 0.00',
        'is_active' => 'BOOLEAN DEFAULT TRUE'
    ];
    
    echo "<h3>2. إضافة الأعمدة المفقودة:</h3>";
    
    foreach ($required_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $existing_columns)) {
            try {
                $sql = "ALTER TABLE customers ADD COLUMN $column_name $column_definition";
                $db->exec($sql);
                echo "✅ تم إضافة العمود: $column_name<br>";
            } catch (Exception $e) {
                echo "❌ فشل في إضافة العمود $column_name: " . $e->getMessage() . "<br>";
            }
        } else {
            echo "✅ العمود $column_name موجود بالفعل<br>";
        }
    }
    
    // التحقق من الهيكل النهائي
    echo "<h3>3. الهيكل النهائي للجدول:</h3>";
    $sql = "DESCRIBE customers";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $final_columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f8f9fa;'><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($final_columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // حذف البيانات الموجودة وإعادة إدراجها
    echo "<h3>4. إعادة إدراج البيانات:</h3>";
    
    // حذف البيانات الموجودة
    $sql = "DELETE FROM customers";
    $db->exec($sql);
    echo "✅ تم حذف البيانات القديمة<br>";
    
    // إعادة تعيين AUTO_INCREMENT
    $sql = "ALTER TABLE customers AUTO_INCREMENT = 1";
    $db->exec($sql);
    echo "✅ تم إعادة تعيين AUTO_INCREMENT<br>";
    
    // إدراج البيانات الجديدة
    $customers_data = [
        [
            'customer_code' => 'CUS001',
            'name' => 'أحمد محمد علي',
            'email' => '<EMAIL>',
            'phone' => '01111111111',
            'address' => 'شارع التحرير، القاهرة',
            'tax_number' => '*********',
            'credit_limit' => 50000.00,
            'opening_balance' => 5000.00,
            'current_balance' => 5000.00,
            'is_active' => 1
        ],
        [
            'customer_code' => 'CUS002',
            'name' => 'فاطمة حسن محمود',
            'email' => '<EMAIL>',
            'phone' => '01111111112',
            'address' => 'شارع الهرم، الجيزة',
            'tax_number' => '*********',
            'credit_limit' => 30000.00,
            'opening_balance' => 3000.00,
            'current_balance' => 3000.00,
            'is_active' => 1
        ],
        [
            'customer_code' => 'CUS003',
            'name' => 'محمد أحمد السيد',
            'email' => '<EMAIL>',
            'phone' => '01111111113',
            'address' => 'شارع الكورنيش، الإسكندرية',
            'tax_number' => '*********',
            'credit_limit' => 75000.00,
            'opening_balance' => 7500.00,
            'current_balance' => 7500.00,
            'is_active' => 1
        ],
        [
            'customer_code' => 'CUS004',
            'name' => 'سارة عبد الرحمن',
            'email' => '<EMAIL>',
            'phone' => '01111111114',
            'address' => 'شارع الجامعة، أسيوط',
            'tax_number' => '*********',
            'credit_limit' => 45000.00,
            'opening_balance' => 4500.00,
            'current_balance' => 4500.00,
            'is_active' => 1
        ],
        [
            'customer_code' => 'CUS005',
            'name' => 'خالد عبد الله',
            'email' => '<EMAIL>',
            'phone' => '01111111115',
            'address' => 'شارع النصر، المنصورة',
            'tax_number' => '*********',
            'credit_limit' => 60000.00,
            'opening_balance' => 6000.00,
            'current_balance' => 6000.00,
            'is_active' => 1
        ]
    ];
    
    $sql = "INSERT INTO customers (customer_code, name, email, phone, address, tax_number, credit_limit, opening_balance, current_balance, is_active) 
            VALUES (:customer_code, :name, :email, :phone, :address, :tax_number, :credit_limit, :opening_balance, :current_balance, :is_active)";
    
    $stmt = $db->prepare($sql);
    
    foreach ($customers_data as $data) {
        try {
            $stmt->execute($data);
            echo "✅ تم إدراج العميل: " . $data['name'] . "<br>";
        } catch (Exception $e) {
            echo "❌ فشل في إدراج العميل " . $data['name'] . ": " . $e->getMessage() . "<br>";
        }
    }
    
    // التحقق النهائي
    echo "<h3>5. التحقق النهائي:</h3>";
    $sql = "SELECT COUNT(*) as total FROM customers";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $count = $stmt->fetch();
    echo "إجمالي العملاء: " . $count['total'] . "<br>";
    
    $sql = "SELECT COUNT(*) as active FROM customers WHERE is_active = 1";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $active_count = $stmt->fetch();
    echo "العملاء النشطين: " . $active_count['active'] . "<br>";
    
    echo "<br><div style='text-align: center; background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h4 style='color: #155724;'>🎉 تم إصلاح جدول العملاء بنجاح!</h4>";
    echo "<a href='customers.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة العملاء</a> ";
    echo "<a href='test_customer_class.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار فئة العملاء</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "❌ <strong>خطأ:</strong><br>" . $e->getMessage();
    echo "</div>";
}
?>
