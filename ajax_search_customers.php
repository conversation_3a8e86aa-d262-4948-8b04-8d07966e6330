<?php
/**
 * البحث الديناميكي في العملاء
 * Dynamic Customer Search
 */

// تعريف الثابت للوصول
define('SEASYSTEM_ACCESS', true);

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/Customer.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    exit('غير مصرح');
}

// التحقق من وجود مصطلح البحث
$search_term = $_GET['search'] ?? '';

// إنشاء كائن العميل
$customer = new Customer($pdo);

// البحث في العملاء
if (!empty($search_term)) {
    $customers = $customer->search($search_term);
} else {
    $customers = $customer->getAll();
}

// إرجاع النتائج كـ HTML
if (empty($customers)): ?>
    <tr>
        <td colspan="8" class="text-center py-4">
            <i class="fas fa-users fa-2x text-muted mb-2"></i>
            <p class="text-muted mb-0">لا توجد نتائج للبحث "<?php echo htmlspecialchars($search_term); ?>"</p>
        </td>
    </tr>
<?php else: ?>
    <?php foreach ($customers as $cust): ?>
        <tr>
            <td><strong><?php echo htmlspecialchars($cust['customer_code']); ?></strong></td>
            <td><?php echo htmlspecialchars($cust['name']); ?></td>
            <td><?php echo htmlspecialchars($cust['email']); ?></td>
            <td><?php echo htmlspecialchars($cust['phone']); ?></td>
            <td><?php echo htmlspecialchars($cust['tax_number']); ?></td>
            <td>
                <span class="<?php echo $cust['current_balance'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                    <?php echo number_format($cust['current_balance'], 2) . ' ' . CURRENCY_SYMBOL; ?>
                </span>
            </td>
            <td>
                <?php if ($cust['is_active']): ?>
                    <span class="badge bg-success">نشط</span>
                <?php else: ?>
                    <span class="badge bg-secondary">غير نشط</span>
                <?php endif; ?>
            </td>
            <td>
                <div class="d-flex gap-2">
                    <!-- زر العرض أولاً -->
                    <div class="text-center">
                        <button type="button" class="btn btn-outline-info btn-sm"
                                onclick="viewCustomer(<?php echo $cust['id']; ?>)">
                            <i class="fas fa-eye"></i>
                        </button>
                        <small class="d-block text-muted mt-1">عرض</small>
                    </div>
                    <!-- زر التعديل ثانياً -->
                    <div class="text-center">
                        <button type="button" class="btn btn-outline-primary btn-sm"
                                onclick="editCustomer(<?php echo htmlspecialchars(json_encode($cust)); ?>)">
                            <i class="fas fa-edit"></i>
                        </button>
                        <small class="d-block text-muted mt-1">تعديل</small>
                    </div>
                    <!-- زر الحذف أخيراً -->
                    <div class="text-center">
                        <button type="button" class="btn btn-outline-danger btn-sm btn-delete"
                                onclick="deleteCustomer(<?php echo $cust['id']; ?>)">
                            <i class="fas fa-trash"></i>
                        </button>
                        <small class="d-block text-muted mt-1">حذف</small>
                    </div>
                </div>
            </td>
        </tr>
    <?php endforeach; ?>
<?php endif; ?>
