<?php
/**
 * SeaSystem - الحصول على رقم محجوز مؤقتاً
 * Get Reserved Number (Temporary)
 */

// تعريف الثابت للوصول
define('SEASYSTEM_ACCESS', true);

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/NumberGenerator.php';
require_once 'includes/auth.php';

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'غير مصرح']);
    exit();
}

// التحقق من وجود نوع الكيان
if (!isset($_GET['type'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'نوع الكيان مطلوب']);
    exit();
}

$type = $_GET['type'];
$numberGenerator = new NumberGenerator();

// تنظيف الحجوزات المنتهية الصلاحية
$numberGenerator->cleanupExpiredReservations();

try {
    $number = '';
    
    switch ($type) {
        case 'customer':
            // التحقق من وجود رقم محجوز مسبقاً
            $existing = $numberGenerator->getReservedNumber('customer');
            if ($existing) {
                $number = $existing;
            } else {
                $number = $numberGenerator->reserveNumber('customer', 'CUS', 3);
            }
            break;

        case 'supplier':
            $existing = $numberGenerator->getReservedNumber('supplier');
            if ($existing) {
                $number = $existing;
            } else {
                $number = $numberGenerator->reserveNumber('supplier', 'SUP', 3);
            }
            break;

        case 'product':
            $existing = $numberGenerator->getReservedNumber('product');
            if ($existing) {
                $number = $existing;
            } else {
                $number = $numberGenerator->reserveNumber('product', 'PRD', 3);
            }
            break;

        case 'warehouse':
            $existing = $numberGenerator->getReservedNumber('warehouse');
            if ($existing) {
                $number = $existing;
            } else {
                $number = $numberGenerator->reserveNumber('warehouse', 'WH', 3);
            }
            break;

        case 'invoice_sales':
            $existing = $numberGenerator->getReservedNumber('invoice_sales');
            if ($existing) {
                $number = $existing;
            } else {
                $number = $numberGenerator->reserveNumber('invoice_sales', 'SALES' . date('Ym'), 4);
            }
            break;

        case 'invoice_purchase':
            $existing = $numberGenerator->getReservedNumber('invoice_purchase');
            if ($existing) {
                $number = $existing;
            } else {
                $number = $numberGenerator->reserveNumber('invoice_purchase', 'PURCH' . date('Ym'), 4);
            }
            break;

        case 'payment':
            $payment_type = $_GET['payment_type'] ?? 'received';
            $prefix = ($payment_type == 'received') ? 'REC' : 'PAY';
            $existing = $numberGenerator->getReservedNumber('payment');
            if ($existing) {
                $number = $existing;
            } else {
                $number = $numberGenerator->reserveNumber('payment', $prefix . date('Ym'), 3);
            }
            break;

        case 'journal':
            $existing = $numberGenerator->getReservedNumber('journal');
            if ($existing) {
                $number = $existing;
            } else {
                $number = $numberGenerator->reserveNumber('journal', 'JE' . date('Ym'), 4);
            }
            break;

        default:
            throw new Exception('نوع كيان غير صحيح');
    }

    // إرجاع النتيجة
    echo json_encode([
        'success' => true,
        'number' => $number,
        'type' => $type,
        'reserved' => true,
        'message' => 'تم حجز الرقم مؤقتاً'
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في النظام: ' . $e->getMessage()
    ]);
}
?>
